#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
运行脚本：启动多交易对监控系统
"""

import os
import sys
import subprocess
import argparse
from loguru import logger

def main():
    """
    主函数：解析命令行参数并启动多交易对监控系统
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="多交易对监控系统启动脚本")
    parser.add_argument("--symbols", type=str, default="BTC/USDT,ETH/USDT",
                        help="要监控的交易对，用逗号分隔，例如：BTC/USDT,ETH/USDT,XRP/USDT")
    parser.add_argument("--exchange", type=str, default="gate", help="交易所ID，默认为gate")
    parser.add_argument("--interval", type=int, default=5, help="检查间隔，单位为分钟，默认为5分钟")
    parser.add_argument("--mode", type=str, default="simple", choices=["multi", "simple"],
                        help="运行模式：multi-多级别联立分析，simple-简单线段终结判断，默认为simple")
    parser.add_argument("--log-level", type=str, default="INFO",
                        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                        help="日志级别，默认为INFO")
    
    args = parser.parse_args()
    
    # 设置日志
    logger.remove()
    logger.add(sys.stderr, level=args.log_level)
    logger.add("logs/run_multi_{time}.log", rotation="100 MB", level="INFO")
    
    # 输出启动信息
    logger.info(f"启动多交易对监控系统")
    logger.info(f"交易对: {args.symbols}")
    logger.info(f"交易所: {args.exchange}")
    logger.info(f"检查间隔: {args.interval}分钟")
    logger.info(f"运行模式: {args.mode}")
    
    try:
        # 构建命令行参数
        cmd = [
            sys.executable,  # 当前Python解释器
            "main_multi_symbols.py",
            f"--symbols={args.symbols}",
            f"--exchange={args.exchange}",
            f"--interval={args.interval}",
            f"--mode={args.mode}",
            f"--log-level={args.log_level}"
        ]
        
        # 启动子进程
        logger.info(f"启动命令: {' '.join(cmd)}")
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1
        )
        
        # 实时输出子进程的输出
        logger.info("监控系统已启动，按Ctrl+C结束程序")
        
        # 读取并输出子进程的输出
        for line in iter(process.stdout.readline, ''):
            if line:
                print(line.strip())
        
        # 等待子进程结束
        process.wait()
        
        # 检查子进程退出状态
        if process.returncode != 0:
            logger.error(f"子进程异常退出，退出码: {process.returncode}")
            # 输出子进程的错误信息
            error_output = process.stderr.read()
            if error_output:
                logger.error(f"错误信息: {error_output}")
        else:
            logger.info("监控系统正常退出")
            
    except KeyboardInterrupt:
        logger.info("接收到停止信号，正在停止系统...")
        if process and process.poll() is None:
            process.terminate()
            process.wait(timeout=5)
        logger.info("系统已停止")
    except Exception as e:
        logger.exception(f"启动监控系统时出错: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 