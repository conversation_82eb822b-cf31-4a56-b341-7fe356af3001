#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_signal_priority_logic():
    """测试优化后的信号优先级逻辑"""
    print("="*80)
    print("信号优先级逻辑优化测试")
    print("="*80)
    
    # 模拟不同的信号场景
    test_scenarios = [
        {
            'name': '场景1: 当前K线买入信号 vs 5根K线前卖出信号',
            'buy_signals': [{'type': '中枢上沿买点', 'strength': 8, 'bar_position': 0}],
            'sell_signals': [{'type': '第一类卖点', 'strength': 10, 'bar_position': 5}],
            'expected': 'buy',
            'reason': '当前K线的买入信号应该优先于过时的卖出信号'
        },
        {
            'name': '场景2: 2根K线前买入信号 vs 当前K线卖出信号',
            'buy_signals': [{'type': '第二类买点', 'strength': 9, 'bar_position': 2}],
            'sell_signals': [{'type': '第三类卖点', 'strength': 7, 'bar_position': 0}],
            'expected': 'sell',
            'reason': '当前K线的卖出信号应该优先于较旧的买入信号'
        },
        {
            'name': '场景3: 当前K线强买入信号 vs 当前K线弱卖出信号',
            'buy_signals': [{'type': '第一类买点', 'strength': 10, 'bar_position': 0}],
            'sell_signals': [{'type': '第三类卖点', 'strength': 6, 'bar_position': 0}],
            'expected': 'buy',
            'reason': '同样新鲜的信号，强度更高的应该优先'
        },
        {
            'name': '场景4: 当前K线强度相近的买卖信号',
            'buy_signals': [{'type': '第二类买点', 'strength': 8, 'bar_position': 0}],
            'sell_signals': [{'type': '第二类卖点', 'strength': 8, 'bar_position': 0}],
            'expected': 'wait',
            'reason': '强度相近的新鲜信号应该建议观望'
        },
        {
            'name': '场景5: 都是过时信号',
            'buy_signals': [{'type': '第一类买点', 'strength': 10, 'bar_position': 12}],
            'sell_signals': [{'type': '第一类卖点', 'strength': 9, 'bar_position': 15}],
            'expected': 'wait',
            'reason': '过时的信号都应该建议观望'
        }
    ]
    
    def calculate_weighted_score(signal):
        """计算包含时效性权重的综合评分"""
        base_strength = signal['strength']
        bar_position = signal['bar_position']
        
        # 时效性权重：越近的信号权重越高
        if bar_position == 0:
            time_weight = 2.0  # 当前K线，最高权重
        elif bar_position <= 2:
            time_weight = 1.5  # 2根K线内，高权重
        elif bar_position <= 5:
            time_weight = 1.2  # 5根K线内，中等权重
        elif bar_position <= 10:
            time_weight = 1.0  # 10根K线内，正常权重
        else:
            time_weight = 0.5  # 超过10根K线，低权重
        
        return base_strength * time_weight
    
    def make_decision(buy_signals, sell_signals):
        """模拟优化后的决策逻辑"""
        if not buy_signals and not sell_signals:
            return 'wait', '无信号'
        elif buy_signals and not sell_signals:
            strongest_buy = max(buy_signals, key=lambda x: x['strength'])
            if strongest_buy['bar_position'] <= 10:
                return 'buy', f"单一买入信号: {strongest_buy['type']}"
            else:
                return 'wait', '买入信号过时'
        elif sell_signals and not buy_signals:
            strongest_sell = max(sell_signals, key=lambda x: x['strength'])
            if strongest_sell['bar_position'] <= 10:
                return 'sell', f"单一卖出信号: {strongest_sell['type']}"
            else:
                return 'wait', '卖出信号过时'
        else:
            # 同时有买卖信号的情况
            strongest_buy = max(buy_signals, key=lambda x: x['strength'])
            strongest_sell = max(sell_signals, key=lambda x: x['strength'])
            
            buy_weighted_score = calculate_weighted_score(strongest_buy)
            sell_weighted_score = calculate_weighted_score(strongest_sell)
            
            # 设置时效性阈值
            recent_threshold = 3
            valid_threshold = 10
            
            buy_is_recent = strongest_buy['bar_position'] <= recent_threshold
            sell_is_recent = strongest_sell['bar_position'] <= recent_threshold
            buy_is_valid = strongest_buy['bar_position'] <= valid_threshold
            sell_is_valid = strongest_sell['bar_position'] <= valid_threshold
            
            if not buy_is_valid and not sell_is_valid:
                return 'wait', '买卖信号均已过时'
            elif buy_is_recent and not sell_is_recent:
                return 'buy', f'买点更新鲜 ({strongest_buy["bar_position"]} vs {strongest_sell["bar_position"]}根K线前)'
            elif sell_is_recent and not buy_is_recent:
                return 'sell', f'卖点更新鲜 ({strongest_sell["bar_position"]} vs {strongest_buy["bar_position"]}根K线前)'
            elif buy_is_recent and sell_is_recent:
                if buy_weighted_score > sell_weighted_score * 1.1:
                    return 'buy', f'买点综合评分更高 ({buy_weighted_score:.1f} vs {sell_weighted_score:.1f})'
                elif sell_weighted_score > buy_weighted_score * 1.1:
                    return 'sell', f'卖点综合评分更高 ({sell_weighted_score:.1f} vs {buy_weighted_score:.1f})'
                else:
                    return 'wait', f'信号强度接近 ({buy_weighted_score:.1f} vs {sell_weighted_score:.1f})'
            else:
                if buy_weighted_score > sell_weighted_score * 1.2:
                    return 'buy', f'买点综合评分显著更高 ({buy_weighted_score:.1f} vs {sell_weighted_score:.1f})'
                elif sell_weighted_score > buy_weighted_score * 1.2:
                    return 'sell', f'卖点综合评分显著更高 ({sell_weighted_score:.1f} vs {buy_weighted_score:.1f})'
                else:
                    return 'wait', '信号都不够新鲜且强度接近'
    
    print("\n测试结果:")
    print("-" * 80)
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{scenario['name']}")
        print(f"买入信号: {scenario['buy_signals']}")
        print(f"卖出信号: {scenario['sell_signals']}")
        
        decision, reason = make_decision(scenario['buy_signals'], scenario['sell_signals'])
        
        print(f"决策结果: {decision}")
        print(f"决策理由: {reason}")
        print(f"预期结果: {scenario['expected']}")
        print(f"测试结果: {'✅ 通过' if decision == scenario['expected'] else '❌ 失败'}")
        print(f"预期原因: {scenario['reason']}")
        
        if decision != scenario['expected']:
            print(f"⚠️  决策逻辑可能需要进一步调整")
    
    print("\n" + "="*80)
    print("优化效果总结")
    print("="*80)
    
    print("✅ 优化成果:")
    print("1. 引入时效性权重，当前K线信号权重最高")
    print("2. 新鲜信号（3根K线内）优先于过时信号")
    print("3. 同样新鲜的信号按加权评分比较")
    print("4. 过时信号（超过10根K线）建议观望")
    print("5. 强度相近的信号建议观望等待明确方向")
    
    print("\n📊 时效性权重设计:")
    print("- 当前K线: 2.0倍权重（最高优先级）")
    print("- 2根K线内: 1.5倍权重（高优先级）")
    print("- 5根K线内: 1.2倍权重（中等优先级）")
    print("- 10根K线内: 1.0倍权重（正常优先级）")
    print("- 超过10根K线: 0.5倍权重（低优先级）")
    
    print("\n🎯 决策阈值:")
    print("- 新鲜信号阈值: 3根K线内")
    print("- 有效信号阈值: 10根K线内")
    print("- 新鲜信号决策阈值: 1.1倍优势")
    print("- 一般信号决策阈值: 1.2倍优势")

if __name__ == "__main__":
    try:
        test_signal_priority_logic()
        print("\n✅ 信号优先级逻辑测试完成")
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
