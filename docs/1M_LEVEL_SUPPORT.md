# 1分钟级别支持文档

## 概述

为系统增加了完整的1分钟级别判断逻辑，使其成为最小的时间分析单位，适合超短线和高频交易场景。

## 1分钟级别特征

### 基本参数

| 参数 | 数值 | 说明 |
|------|------|------|
| **中枢高度比例** | 1.5% | 最小的中枢高度，反映微观波动 |
| **延伸比例** | 0.8 | 最保守的延伸目标 |
| **波动率因子** | 0.8 | 最严格的风险控制 |
| **最少K线数** | 9 | 构成中枢的最少K线数量 |

### 级别对比

| 级别 | 中枢高度% | 延伸比例 | 波动率因子 | 适用场景 |
|------|-----------|----------|------------|----------|
| **1m** | 1.5% | 0.8 | 0.8 | 超短线、高频交易 |
| **5m** | 2.0% | 1.0 | 1.0 | 短线操作基准 |
| **15m** | 3.5% | 1.5 | 1.3 | 中短线操作 |
| **30m** | 5.0% | 2.0 | 1.6 | 中线操作 |
| **1h** | 8.0% | 2.5 | 2.0 | 中长线操作 |

## 实际应用效果

### 买入信号止盈目标对比

以入场价格2650.00为例：

| 级别 | 止盈1 | 止盈1% | 止盈2 | 止盈2% | 止盈3 | 止盈3% |
|------|-------|--------|-------|--------|-------|--------|
| **1m** | 2691.23 | +1.6% | 2701.68 | +2.0% | 2717.58 | +2.6% |
| **5m** | 2703.26 | +2.0% | 2729.50 | +3.0% | 2756.00 | +4.0% |
| **15m** | 2731.43 | +3.1% | 2835.50 | +7.0% | 2905.06 | +9.6% |
| **30m** | 2759.71 | +4.1% | 2981.25 | +12.5% | 3113.75 | +17.5% |
| **1h** | 2811.12 | +6.1% | 3286.00 | +24.0% | 3551.00 | +34.0% |

### 卖出信号止盈目标对比

| 级别 | 止盈1 | 止盈1% | 止盈2 | 止盈2% | 止盈3 | 止盈3% |
|------|-------|--------|-------|--------|-------|--------|
| **1m** | 2609.08 | -1.5% | 2598.32 | -2.0% | 2582.42 | -2.6% |
| **5m** | 2597.26 | -2.0% | 2570.50 | -3.0% | 2544.00 | -4.0% |
| **15m** | 2569.78 | -3.0% | 2464.50 | -7.0% | 2394.94 | -9.6% |
| **30m** | 2542.41 | -4.1% | 2318.75 | -12.5% | 2186.25 | -17.5% |
| **1h** | 2493.12 | -5.9% | 2014.00 | -24.0% | 1749.00 | -34.0% |

## 级别关系验证

✅ **买入止盈目标级别递增关系正确**：
- 1m: 2691.23 (基准)
- 5m: 2703.26 (+0.4%)
- 15m: 2731.43 (+1.5%)
- 30m: 2759.71 (+2.5%)
- 1h: 2811.12 (+4.5%)

✅ **卖出止盈目标级别递减关系正确**：
- 1m: 2609.08 (基准)
- 5m: 2597.26 (-0.5%)
- 15m: 2569.78 (-1.5%)
- 30m: 2542.41 (-2.5%)
- 1h: 2493.12 (-4.4%)

## 1分钟级别的特点

### 📊 技术特征
- **⚡ 最小时间单位**：1分钟K线，最精细的时间粒度
- **📉 中枢高度最小**：约1.5%，反映微观市场波动
- **🎯 延伸比例最小**：0.8倍，目标设置相对保守
- **📈 波动率因子最小**：0.8倍，风险控制最为严格
- **⏱️ 时间敏感性最高**：信号时效性要求极高

### 🔍 应用场景
1. **日内超短线交易**
   - 快进快出的交易策略
   - 捕捉短期价格波动
   - 高频交易的基础分析

2. **精确进出场时机**
   - 大级别信号的精细化执行
   - 优化入场和出场点位
   - 减少滑点和时间成本

3. **风险控制**
   - 最后的风险防线
   - 及时止损和止盈
   - 精确的仓位管理

4. **高频交易策略**
   - 算法交易的基础
   - 套利机会的识别
   - 市场微观结构分析

### ⚖️ 与其他级别的关系

#### 级别递归关系
- **1m → 5m**：5个1分钟K线构成1个5分钟K线
- **时间因子**：0.2（1分钟 = 0.2 × 5分钟）
- **范围因子**：0.8（更小的价格波动范围）

#### 在多级别分析中的作用
1. **精细化执行**：大级别给方向，1m级别给精确时机
2. **信号确认**：1m级别的信号可以确认大级别信号的有效性
3. **风险管理**：1m级别提供最及时的风险预警

## 使用示例

### 命令行使用

```bash
# 检查1分钟级别信号
python main.py --check-signals --check-timeframes 1m

# 多级别信号检查（包含1m）
python main.py --check-signals --check-timeframes 1m,5m,15m,1h

# 1分钟级别综合分析
python main.py --mode comprehensive --timeframe 1m --run-once

# 持续监控1分钟级别信号
python main.py --monitor-signals --check-timeframes 1m --monitor-interval 1
```

### 编程接口使用

```python
from chan_analysis import ChanAnalysis

# 创建分析器
analyzer = ChanAnalysis(df_1m)

# 计算1分钟级别买点策略
targets = analyzer.calculate_chan_profit_targets(
    entry_point={'index': -1, 'price': 2650.0},
    signal_type='buy',
    buy_sell_point_type=1,
    related_pivot_idx=0,
    current_level='1m'
)

# 获取1分钟级别特征
level_info = analyzer._get_level_specific_pivot_info('1m')
print(f"1m级别描述: {level_info['description']}")
print(f"中枢高度比例: {level_info['pivot_height_ratio']*100:.1f}%")
```

## 注意事项

### 1. 数据质量要求
- **高频数据**：需要高质量的1分钟K线数据
- **延迟敏感**：对数据延迟要求极高
- **成交量**：需要关注1分钟级别的成交量变化

### 2. 交易成本考虑
- **手续费**：高频交易需要考虑累积手续费
- **滑点**：1分钟级别的滑点影响相对较大
- **流动性**：确保有足够的市场流动性

### 3. 风险管理
- **止损及时**：1分钟级别需要更及时的止损
- **仓位控制**：建议使用较小的仓位
- **心理压力**：高频操作对交易者心理要求较高

## 技术实现

### 级别特征配置
```python
'1m': {
    'min_bars_in_pivot': 9,
    'pivot_height_ratio': 0.015,  # 1.5%
    'extension_ratio': 0.8,
    'volatility_factor': 0.8,
    'description': '1分钟级别：最小时间单位，适合超短线操作'
}
```

### 理论级别调整
```python
'1m': {
    'range_factor': 0.8,    # 更小的价格范围
    'time_factor': 0.2      # 1分钟 = 0.2 × 5分钟
}
```

## 总结

1分钟级别的加入完善了系统的时间级别覆盖，使其能够：

✅ **支持超短线交易**：为高频和日内交易提供精确分析
✅ **完善级别体系**：形成完整的1m-5m-15m-30m-1h级别链
✅ **保持理论一致性**：严格遵循缠论的级别递归关系
✅ **提供精确执行**：为大级别信号提供精细化执行方案

现在系统真正实现了从1分钟到1小时的全级别覆盖，为不同类型的交易者提供了完整的分析工具！
