# API频率限制解决方案

## 问题描述

在使用yfinance获取实时股票数据时，经常遇到以下错误：
```
❌ 数据获取失败: Too Many Requests. Rate limited. Try after a while.
```

这是因为yfinance API对请求频率有限制，频繁请求会被服务器拒绝。

## 解决方案

### 方案1：使用模拟数据系统（推荐用于测试）

**文件：** `demo_with_mock_data.py`

**优点：**
- 完全避免API限制
- 生成逼真的价格数据
- 可以快速测试交易策略
- 支持多种加密货币模拟

**使用方法：**
```bash
python demo_with_mock_data.py
```

**特点：**
- 使用几何布朗运动生成价格数据
- 包含周期性波动和突发事件
- 支持BTC和ETH模拟
- 完整的技术分析和买卖点识别

### 方案2：低频率实时监控（推荐用于实盘）

**文件：** `low_frequency_monitor.py`

**优点：**
- 使用真实市场数据
- 降低API请求频率
- 智能缓存机制
- 适合长期监控

**特点：**
- 每10分钟检查一次（可调整）
- 8分钟数据缓存
- 自动重试机制
- 支持多种交易品种

**使用方法：**
```bash
python low_frequency_monitor.py
```

### 方案3：修复后的实时系统

**文件：** `real_data_trading.py`

**改进：**
- 添加了5分钟数据缓存
- 增加了重试延迟（5秒起，指数退避）
- 改进了错误处理
- 数据类型转换修复

**使用建议：**
- 适合偶尔使用
- 避免频繁启动
- 建议间隔10分钟以上

## 数据类型修复

### 问题
```
ufunc 'subtract' did not contain a loop with signature matching types (dtype('<U4'), dtype('float64')) -> None
```

### 解决方案
在所有数据获取函数中添加了数据类型转换：

```python
# 转换数据格式
df = pd.DataFrame({
    'timestamp': data.index,
    'open': pd.to_numeric(data['Open'], errors='coerce'),
    'high': pd.to_numeric(data['High'], errors='coerce'),
    'low': pd.to_numeric(data['Low'], errors='coerce'),
    'close': pd.to_numeric(data['Close'], errors='coerce'),
    'volume': pd.to_numeric(data['Volume'], errors='coerce')
})

# 删除包含NaN的行
df = df.dropna()
```

## 推荐使用流程

### 1. 开发和测试阶段
使用模拟数据系统：
```bash
python demo_with_mock_data.py
```

### 2. 实盘监控阶段
使用低频率监控系统：
```bash
python low_frequency_monitor.py
```

### 3. 偶尔检查
使用修复后的实时系统：
```bash
python real_data_trading.py
```

## 环境要求

### Python 2.7兼容性
所有脚本都已修复Python 2.7兼容性问题：
- 使用`raw_input()`而不是`input()`
- 避免使用f-string
- 添加异常处理

### 依赖安装
```bash
# 如果需要使用真实数据
pip install yfinance

# 基础依赖（已包含）
pip install pandas numpy
```

## 最佳实践

1. **避免频繁请求**：最少间隔5分钟
2. **使用缓存**：避免重复获取相同数据
3. **错误处理**：实现重试机制
4. **数据验证**：确保数据类型正确
5. **监控限制**：注意API使用限额

## 故障排除

### 如果仍然遇到API限制：
1. 等待15-30分钟后重试
2. 使用不同的交易品种
3. 降低检查频率
4. 考虑使用模拟数据进行测试

### 如果遇到数据类型错误：
1. 检查数据源格式
2. 确保使用了`pd.to_numeric()`转换
3. 验证数据不包含字符串类型的价格

## 总结

通过以上解决方案，您可以：
- ✅ 避免API频率限制
- ✅ 获得稳定的数据源
- ✅ 进行有效的买卖点监控
- ✅ 支持长期实时监控

建议根据具体需求选择合适的方案。 