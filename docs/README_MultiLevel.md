# 多级别联立线段终结判断系统

基于缠论理论的多级别联立分析系统，通过分析多个时间级别的数据来提高线段终结判断的准确率。

## 🎯 核心特性

### 多级别联立分析
- **时间级别**: 5分钟、15分钟、30分钟、1小时
- **联立判断**: 多级别共振时信号更可靠
- **权重计算**: 大级别权重更高，影响最终判断
- **置信度评估**: 0-100%的置信度评分

### 缠论理论应用
- **分型识别**: 自动识别顶分型和底分型
- **笔的划分**: 基于分型构建笔的结构
- **线段划分**: 基于笔构建线段结构
- **背驰检测**: MACD背驰、成交量背驰等多维度检测

### 智能判断逻辑
- **多级别共振**: 2个以上级别出现终结信号 (+30分)
- **大级别权重**: 大级别终结信号权重较高 (+25分)
- **背驰强度**: 多级别背驰强度评估 (+20分)
- **趋势一致性**: 多级别趋势方向一致 (+15分)
- **特殊加分**: 1小时级别信号、小大级别一致等

## 🚀 快速开始

### 安装依赖
```bash
pip install loguru pandas ccxt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 运行单次分析
```bash
# 基础分析
python main.py --run-once --symbol BTC/USDT

# 显示详细结果
python main.py --run-once --symbol BTC/USDT --show-details

# 指定交易所
python main.py --run-once --symbol BTC/USDT --exchange huobi --show-details
```

### 启动监控模式
```bash
# 每5分钟检查一次
python main.py --symbol BTC/USDT --interval 5

# 每10分钟检查一次，指定交易所
python main.py --symbol ETH/USDT --exchange okex --interval 10
```

## 📊 输出示例

### 控制台输出
```
=== BTC/USDT 多级别分析结果 ===
最终判断: 🚨 线段终结
置信度: 75%
判断理由: 3个级别出现终结信号; 大级别终结信号权重较高; 多级别趋势方向一致
趋势一致性: 是

=== 各级别详细结果 ===
  5m: ✅ 终结 📉 down (背驰强度: 2, 价格: 43250.50)
 15m: ✅ 终结 📉 down (背驰强度: 1, 价格: 43251.20)
 30m: ❌ 未终结 📉 down (背驰强度: 0, 价格: 43252.00)
  1h: ✅ 终结 📉 down (背驰强度: 2, 价格: 43253.10)

=== 交易建议 ===
建议操作: 🟢 BUY
风险等级: medium
建议理由: 下降线段终结，建议买入
```

### 通知消息
```
🚨 BTC/USDT 多级别线段终结信号 🚨
时间: 2025-01-03 16:45:23
置信度: 75%
判断理由: 3个级别出现终结信号; 大级别终结信号权重较高; 多级别趋势方向一致
趋势一致性: 是

📊 各级别分析结果:
  5m: ✅ 终结 📉 down (背驰强度: 2)
  15m: ✅ 终结 📉 down (背驰强度: 1)
  30m: ❌ 未终结 📉 down (背驰强度: 0)
  1h: ✅ 终结 📉 down (背驰强度: 2)

💡 交易建议: 🟢 BUY
风险等级: medium
建议理由: 下降线段终结，建议买入
```

## 📁 文件结构

```
gupiao/
├── main.py                    # 主程序入口
├── multi_level_analysis.py    # 多级别分析核心模块
├── data_fetcher.py           # 数据获取模块（支持多交易所）
├── chan_analysis.py          # 缠论分析模块
├── notifier.py               # 通知模块
├── scheduler.py              # 定时调度模块
├── logs/                     # 日志文件目录
├── reports/                  # 分析报告目录
└── notifications/            # 通知文件目录
```

## 🔧 配置说明

### 支持的交易所
- Binance (币安)
- Huobi (火币)
- OKEx (欧易)
- KuCoin (库币)
- Gate.io
- Bybit
- MEXC

### 时间级别
- 5m: 5分钟K线
- 15m: 15分钟K线
- 30m: 30分钟K线
- 1h: 1小时K线

### 置信度评分规则
- **60分以上**: 判断为线段终结
- **80分以上**: 低风险信号
- **60-79分**: 中等风险信号
- **60分以下**: 高风险信号（不触发）

## 📈 算法优势

### 相比单级别分析的改进
1. **准确率提升**: 多级别联立减少假信号
2. **风险控制**: 置信度评估帮助风险管理
3. **趋势确认**: 大小级别趋势一致性验证
4. **背驰强度**: 多维度背驰检测更可靠

### 缠论理论应用
1. **级别关系**: 大级别决定方向，小级别决定进出点
2. **背驰判断**: 价格与指标的背离关系
3. **结构分析**: 分型-笔-线段的层次结构
4. **走势类型**: 上升、下降、盘整的识别

## 🚨 风险提示

1. **市场风险**: 任何技术分析都无法保证100%准确
2. **数据依赖**: 系统依赖实时数据质量
3. **网络风险**: 网络中断可能影响数据获取
4. **参数调优**: 可能需要根据市场情况调整参数

## 📞 技术支持

如遇到问题，请检查：
1. 网络连接是否正常
2. 依赖包是否正确安装
3. 日志文件中的错误信息
4. 交易所API是否可访问

---

**免责声明**: 本系统仅供学习和研究使用，不构成投资建议。投资有风险，入市需谨慎。 