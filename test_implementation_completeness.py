#!/usr/bin/env python
# -*- coding: utf-8 -*-

import ast
import re

def check_implementation_completeness():
    """检查智能上下文感知系统的实现完整性"""
    print("="*80)
    print("智能上下文感知系统实现完整性检查")
    print("="*80)
    
    # 读取multi_level_analysis.py文件
    try:
        with open('multi_level_analysis.py', 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print("❌ 无法找到multi_level_analysis.py文件")
        return False
    
    # 检查关键方法是否存在
    required_methods = [
        '_intelligent_signal_validation',
        '_analyze_market_context',
        '_calculate_profit_potential',
        '_calculate_context_adjusted_validity',
        '_make_intelligent_recommendation',
        '_calculate_signal_relevance'
    ]
    
    print("1. 检查关键方法是否存在:")
    missing_methods = []
    for method in required_methods:
        if f"def {method}(" in content:
            print(f"   ✅ {method}")
        else:
            print(f"   ❌ {method}")
            missing_methods.append(method)
    
    # 检查是否调用了智能验证系统
    print("\n2. 检查智能验证系统调用:")
    
    # 检查买入信号调用
    buy_call_pattern = r'_intelligent_signal_validation\(\s*strongest_buy'
    buy_calls = re.findall(buy_call_pattern, content)
    if buy_calls:
        print("   ✅ 买入信号使用智能验证系统")
    else:
        print("   ❌ 买入信号未使用智能验证系统")
    
    # 检查卖出信号调用
    sell_call_pattern = r'_intelligent_signal_validation\(\s*strongest_sell'
    sell_calls = re.findall(sell_call_pattern, content)
    if sell_calls:
        print("   ✅ 卖出信号使用智能验证系统")
    else:
        print("   ❌ 卖出信号未使用智能验证系统")
    
    # 检查买卖信号同时存在的情况
    both_signals_pattern = r'buy_should_recommend.*sell_should_recommend'
    both_calls = re.findall(both_signals_pattern, content, re.DOTALL)
    if both_calls:
        print("   ✅ 买卖信号同时存在时使用智能验证系统")
    else:
        print("   ❌ 买卖信号同时存在时未使用智能验证系统")
    
    # 检查是否移除了旧的硬性时效性检查
    print("\n3. 检查旧逻辑移除情况:")
    
    # 检查是否还有硬性的8根K线或10根K线检查
    old_logic_patterns = [
        r'bar_position.*<=.*10.*:',
        r'bar_position.*<=.*8.*:',
        r'信号已过时.*建议观望',
        r'buy_is_recent.*sell_is_recent'
    ]
    
    old_logic_found = False
    for pattern in old_logic_patterns:
        matches = re.findall(pattern, content)
        if matches:
            print(f"   ⚠️  发现可能的旧逻辑: {pattern}")
            old_logic_found = True
    
    if not old_logic_found:
        print("   ✅ 旧的硬性时效性检查已移除")
    
    # 检查次级别建议功能
    print("\n4. 检查次级别建议功能:")
    
    next_level_patterns = [
        r'next_level_suggestion',
        r'等待次级别',
        r'关注.*级别',
        r'next_level_map'
    ]
    
    next_level_features = 0
    for pattern in next_level_patterns:
        matches = re.findall(pattern, content)
        if matches:
            next_level_features += len(matches)
    
    if next_level_features > 0:
        print(f"   ✅ 次级别建议功能已实现 (发现{next_level_features}处相关代码)")
    else:
        print("   ❌ 次级别建议功能未实现")
    
    # 检查利润空间分析
    print("\n5. 检查利润空间分析:")
    
    profit_patterns = [
        r'profit_potential',
        r'remaining_space',
        r'risk_reward_ratio',
        r'is_profitable'
    ]
    
    profit_features = 0
    for pattern in profit_patterns:
        matches = re.findall(pattern, content)
        if matches:
            profit_features += len(matches)
    
    if profit_features > 0:
        print(f"   ✅ 利润空间分析已实现 (发现{profit_features}处相关代码)")
    else:
        print("   ❌ 利润空间分析未实现")
    
    # 检查市场上下文分析
    print("\n6. 检查市场上下文分析:")
    
    context_patterns = [
        r'trend_strength',
        r'volatility',
        r'price_position',
        r'volume_ratio',
        r'is_trending'
    ]
    
    context_features = 0
    for pattern in context_patterns:
        matches = re.findall(pattern, content)
        if matches:
            context_features += len(matches)
    
    if context_features > 0:
        print(f"   ✅ 市场上下文分析已实现 (发现{context_features}处相关代码)")
    else:
        print("   ❌ 市场上下文分析未实现")
    
    # 检查1m级别支持
    print("\n7. 检查1m级别支持:")
    
    if "'1m':" in content:
        print("   ✅ 1m级别已添加到配置中")
    else:
        print("   ❌ 1m级别未添加到配置中")
    
    # 总结
    print("\n" + "="*80)
    print("实现完整性总结")
    print("="*80)
    
    total_checks = 7
    passed_checks = 0
    
    if not missing_methods:
        passed_checks += 1
    if buy_calls and sell_calls and both_calls:
        passed_checks += 1
    if not old_logic_found:
        passed_checks += 1
    if next_level_features > 0:
        passed_checks += 1
    if profit_features > 0:
        passed_checks += 1
    if context_features > 0:
        passed_checks += 1
    if "'1m':" in content:
        passed_checks += 1
    
    completion_rate = (passed_checks / total_checks) * 100
    
    print(f"完成度: {passed_checks}/{total_checks} ({completion_rate:.1f}%)")
    
    if completion_rate >= 90:
        print("🎉 实现完整性优秀！")
        status = "excellent"
    elif completion_rate >= 70:
        print("👍 实现完整性良好")
        status = "good"
    elif completion_rate >= 50:
        print("⚠️  实现完整性一般，需要改进")
        status = "fair"
    else:
        print("❌ 实现完整性不足，需要大量改进")
        status = "poor"
    
    # 具体建议
    print("\n📋 改进建议:")
    if missing_methods:
        print(f"- 缺失关键方法: {', '.join(missing_methods)}")
    if not (buy_calls and sell_calls and both_calls):
        print("- 需要确保所有信号判断都使用智能验证系统")
    if old_logic_found:
        print("- 需要完全移除旧的硬性时效性检查逻辑")
    if next_level_features == 0:
        print("- 需要实现次级别建议功能")
    if profit_features == 0:
        print("- 需要实现利润空间分析功能")
    if context_features == 0:
        print("- 需要实现市场上下文分析功能")
    if "'1m':" not in content:
        print("- 需要添加1m级别支持")
    
    return status == "excellent" or status == "good"

if __name__ == "__main__":
    try:
        success = check_implementation_completeness()
        if success:
            print("\n✅ 智能上下文感知系统实现检查通过")
        else:
            print("\n❌ 智能上下文感知系统实现需要改进")
    except Exception as e:
        print(f"\n❌ 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()
