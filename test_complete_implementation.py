#!/usr/bin/env python
# -*- coding: utf-8 -*-

import ast
import re

def check_complete_implementation():
    """检查次级别确认系统的完整实现"""
    print("="*80)
    print("次级别确认系统完整性检查")
    print("="*80)
    
    # 读取实现文件
    try:
        with open('multi_level_analysis.py', 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print("❌ 无法找到multi_level_analysis.py文件")
        return False
    
    # 检查项目列表
    checks = []
    
    # 1. 检查等待状态管理器初始化
    print("1. 等待状态管理器初始化")
    print("-" * 40)
    
    waiting_init_pattern = r'self\.waiting_for_confirmation\s*=\s*\{'
    if re.search(waiting_init_pattern, content):
        print("   ✅ 等待状态管理器已初始化")
        checks.append(True)
        
        # 检查必要字段
        required_fields = [
            'is_waiting', 'target_level', 'parent_level', 
            'parent_signal', 'direction', 'entry_targets',
            'wait_start_time', 'max_wait_minutes'
        ]
        
        missing_fields = []
        for field in required_fields:
            if f"'{field}'" not in content:
                missing_fields.append(field)
        
        if not missing_fields:
            print("   ✅ 所有必要字段都已定义")
            checks.append(True)
        else:
            print(f"   ❌ 缺失字段: {missing_fields}")
            checks.append(False)
    else:
        print("   ❌ 等待状态管理器未初始化")
        checks.append(False)
        checks.append(False)
    
    # 2. 检查核心方法实现
    print("\n2. 核心方法实现")
    print("-" * 40)
    
    core_methods = [
        'set_waiting_for_confirmation',
        'check_waiting_timeout', 
        'clear_waiting_state',
        'check_next_level_confirmation',
        '_generate_immediate_entry_alert',
        '_set_waiting_for_next_level'
    ]
    
    for method in core_methods:
        if f"def {method}(" in content:
            print(f"   ✅ {method}")
            checks.append(True)
        else:
            print(f"   ❌ {method}")
            checks.append(False)
    
    # 3. 检查智能推荐系统集成
    print("\n3. 智能推荐系统集成")
    print("-" * 40)
    
    # 检查是否在智能推荐中调用等待设置
    integration_patterns = [
        r'_set_waiting_for_next_level\(',
        r'next_level_map\s*=.*1h.*30m',
        r'建议关注.*级别进场时机'
    ]
    
    for i, pattern in enumerate(integration_patterns, 1):
        if re.search(pattern, content):
            print(f"   ✅ 集成检查 {i}: 通过")
            checks.append(True)
        else:
            print(f"   ❌ 集成检查 {i}: 失败")
            checks.append(False)
    
    # 4. 检查实时监听集成
    print("\n4. 实时监听集成")
    print("-" * 40)
    
    # 检查是否在check_realtime_signals中调用确认检查
    realtime_patterns = [
        r'check_next_level_confirmation\(',
        r'immediate_entry.*confirmation_info',
        r'检测到.*级别确认信号'
    ]
    
    for i, pattern in enumerate(realtime_patterns, 1):
        if re.search(pattern, content):
            print(f"   ✅ 实时监听 {i}: 通过")
            checks.append(True)
        else:
            print(f"   ❌ 实时监听 {i}: 失败")
            checks.append(False)
    
    # 5. 检查确认条件逻辑
    print("\n5. 确认条件逻辑")
    print("-" * 40)

    confirmation_patterns = [
        r"signal\['bar_position'\]\s*<=\s*3",  # 时效性检查
        r"signal\['strength'\]\s*>=\s*6",      # 强度检查
        r'waiting_direction',                   # 方向匹配
        r'target_level'                        # 级别匹配
    ]
    
    for i, pattern in enumerate(confirmation_patterns, 1):
        if re.search(pattern, content):
            print(f"   ✅ 确认条件 {i}: 通过")
            checks.append(True)
        else:
            print(f"   ❌ 确认条件 {i}: 失败")
            checks.append(False)
    
    # 6. 检查立即入场提醒
    print("\n6. 立即入场提醒")
    print("-" * 40)
    
    alert_patterns = [
        r'🚨.*立即入场信号',
        r'父级别.*-.*确认信号',
        r'建议操作.*立即',
        r'止损.*止盈'
    ]
    
    for i, pattern in enumerate(alert_patterns, 1):
        if re.search(pattern, content):
            print(f"   ✅ 提醒内容 {i}: 通过")
            checks.append(True)
        else:
            print(f"   ❌ 提醒内容 {i}: 失败")
            checks.append(False)
    
    # 7. 检查超时处理
    print("\n7. 超时处理")
    print("-" * 40)
    
    timeout_patterns = [
        r'datetime\.now\(\).*timedelta',
        r'max_wait_minutes',
        r'等待.*超时',
        r'clear_waiting_state'
    ]
    
    for i, pattern in enumerate(timeout_patterns, 1):
        if re.search(pattern, content):
            print(f"   ✅ 超时处理 {i}: 通过")
            checks.append(True)
        else:
            print(f"   ❌ 超时处理 {i}: 失败")
            checks.append(False)
    
    # 8. 检查错误处理
    print("\n8. 错误处理")
    print("-" * 40)
    
    error_patterns = [
        r'try:.*except.*Exception',
        r'logger\.error',
        r'设置等待.*状态失败'
    ]
    
    for i, pattern in enumerate(error_patterns, 1):
        if re.search(pattern, content, re.DOTALL):
            print(f"   ✅ 错误处理 {i}: 通过")
            checks.append(True)
        else:
            print(f"   ❌ 错误处理 {i}: 失败")
            checks.append(False)
    
    # 总结检查结果
    print("\n" + "="*80)
    print("完整性检查总结")
    print("="*80)
    
    passed_checks = sum(checks)
    total_checks = len(checks)
    success_rate = (passed_checks / total_checks) * 100
    
    print(f"检查项目: {total_checks}")
    print(f"通过项目: {passed_checks}")
    print(f"成功率: {success_rate:.1f}%")
    
    # 功能完整性评估
    if success_rate >= 95:
        print("🎉 实现完整性优秀！")
        status = "excellent"
    elif success_rate >= 85:
        print("👍 实现完整性良好")
        status = "good"
    elif success_rate >= 70:
        print("⚠️ 实现完整性一般")
        status = "fair"
    else:
        print("❌ 实现完整性不足")
        status = "poor"
    
    # 详细功能检查
    print("\n📋 功能模块检查:")
    modules = [
        ("等待状态管理", checks[0:2]),
        ("核心方法实现", checks[2:8]),
        ("智能推荐集成", checks[8:11]),
        ("实时监听集成", checks[11:14]),
        ("确认条件逻辑", checks[14:18]),
        ("立即入场提醒", checks[18:22]),
        ("超时处理", checks[22:26]),
        ("错误处理", checks[26:29])
    ]
    
    for module_name, module_checks in modules:
        module_passed = sum(module_checks)
        module_total = len(module_checks)
        module_rate = (module_passed / module_total) * 100 if module_total > 0 else 0
        
        if module_rate >= 90:
            status_icon = "✅"
        elif module_rate >= 70:
            status_icon = "⚠️"
        else:
            status_icon = "❌"
        
        print(f"  {status_icon} {module_name}: {module_passed}/{module_total} ({module_rate:.0f}%)")
    
    # 使用建议
    print("\n🚀 使用建议:")
    if success_rate >= 90:
        print("  ✅ 系统已完整实现，可以正常使用")
        print("  ✅ 所有核心功能都已到位")
        print("  ✅ 建议进行实际测试验证")
    elif success_rate >= 70:
        print("  ⚠️ 系统基本可用，但建议完善缺失功能")
        print("  ⚠️ 重点关注失败的检查项目")
    else:
        print("  ❌ 系统实现不完整，需要重大改进")
        print("  ❌ 建议重新检查核心功能实现")
    
    print("\n📞 实际应用:")
    print("  1. 运行监控: python3 main.py --monitor-signals")
    print("  2. 系统自动检测大级别信号")
    print("  3. 自动设置等待次级别确认状态")
    print("  4. 持续监听目标级别信号")
    print("  5. 检测到确认信号立即提醒")
    
    return status in ["excellent", "good"]

if __name__ == "__main__":
    try:
        success = check_complete_implementation()
        if success:
            print("\n🎉 次级别确认系统实现完整性检查通过！")
        else:
            print("\n⚠️ 次级别确认系统实现需要改进")
    except Exception as e:
        print(f"\n❌ 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()
