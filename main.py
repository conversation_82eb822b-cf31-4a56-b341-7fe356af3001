#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
主入口文件：启动多级别联立线段终结判断系统
"""

import os
import sys
import argparse
import datetime
import time
from loguru import logger
import pandas as pd

from multi_level_analysis import MultiLevelAnalysis
from notifier import Notifier
from scheduler import Scheduler, SegmentMonitor


class MultiLevelSegmentDetector:
    """
    多级别线段终结判断系统
    基于缠论理论的多级别联立分析，提高判断准确率
    """
    def __init__(self, symbol="BTC/USDT", exchange=None, log_level="INFO"):
        """
        初始化多级别线段终结判断系统
        """
        # 创建必要的目录
        os.makedirs("logs", exist_ok=True)
        os.makedirs("data", exist_ok=True)
        os.makedirs("reports", exist_ok=True)
        os.makedirs("charts", exist_ok=True)
        os.makedirs("notifications", exist_ok=True)
        
        # 设置日志
        logger.remove()  # 移除默认处理器
        logger.add(sys.stderr, level=log_level)  # 添加标准错误输出
        logger.add("logs/multi_level_detector_{time}.log", rotation="100 MB", level="INFO")  # 添加文件输出
        
        logger.info("初始化多级别线段终结判断系统，交易对: {}".format(symbol))
        
        # 初始化组件
        self.symbol = symbol
        self.multi_analyzer = MultiLevelAnalysis(symbol=symbol, exchange_id=exchange)
        
        # 创建通知器，默认启用日志、文件和钉钉通知
        self.notifier = Notifier(notification_methods=['log', 'file', 'dingtalk'])
        
        self.scheduler = None
        
        # 记录上一次的判断结果
        self.last_judgment = {
            'final_judgment': False,
            'confidence': 0,
            'timestamp': None
        }
        
        logger.info("多级别系统初始化完成")
    
    def run_once(self):
        """
        运行一次多级别线段终结判断
        """
        logger.info(f"开始执行 {self.symbol} 多级别线段终结判断")
        
        try:
            # 执行多级别联立分析
            judgment_result = self.multi_analyzer.multi_level_judgment()
            
            # 获取当前判断状态
            current_judgment = judgment_result['final_judgment']
            current_confidence = judgment_result['confidence']
            
            # 检查状态是否发生变化
            if current_judgment and not self.last_judgment['final_judgment']:
                # 从未终结变为终结，需要发送通知
                message = self._format_notification(judgment_result)
                self.notifier.send(message)
                logger.warning(f"{self.symbol} 多级别线段终结信号，置信度: {current_confidence}%")
                
                # 保存详细分析报告
                self._save_analysis_report(judgment_result)
                
                # 生成多时间周期分析图表
                chart_path = self.multi_analyzer.plot_multi_timeframe_analysis()
                if chart_path:
                    logger.info(f"已生成多时间周期分析图表: {chart_path}")
                
            elif not current_judgment and self.last_judgment['final_judgment']:
                # 从终结变为未终结
                logger.info(f"{self.symbol} 线段终结信号消失，开始新的走势")
            elif current_judgment:
                # 持续终结状态，检查置信度变化
                confidence_change = current_confidence - self.last_judgment['confidence']
                if abs(confidence_change) >= 10:  # 置信度变化超过10%
                    logger.info(f"{self.symbol} 线段终结信号置信度变化: {confidence_change:+.1f}% (当前: {current_confidence}%)")
            
            # 更新上一次状态
            self.last_judgment = {
                'final_judgment': current_judgment,
                'confidence': current_confidence,
                'timestamp': pd.Timestamp.now()
            }
            
            # 输出当前状态摘要
            self._log_status_summary(judgment_result)
            
            return current_judgment
            
        except Exception as e:
            logger.error(f"执行多级别线段终结判断时出错: {str(e)}")
            return False
    
    def _format_notification(self, judgment_result):
        """
        格式化通知消息
        """
        message = f"🚨 {self.symbol} 多级别线段终结信号 🚨\n"
        message += f"时间: {pd.Timestamp.now()}\n"
        message += f"置信度: {judgment_result['confidence']}%\n"
        message += f"判断理由: {judgment_result['reason']}\n"
        message += f"趋势一致性: {'是' if judgment_result['trend_consistency'] else '否'}\n"
        
        # 添加各级别详细信息
        message += "\n📊 各级别分析结果:\n"
        for tf, result in judgment_result['level_results'].items():
            status = "✅ 终结" if result['segment_ended'] else "❌ 未终结"
            trend_emoji = "📈" if result['current_trend'] == 'up' else "📉" if result['current_trend'] == 'down' else "➡️"
            message += f"  {tf}: {status} {trend_emoji} {result['current_trend']} (背驰强度: {result['divergence']['strength']})\n"
        
        # 添加交易建议
        suggestion = self.multi_analyzer.get_trading_suggestion(judgment_result)
        action_emoji = "🔴" if suggestion['action'] == 'sell' else "🟢" if suggestion['action'] == 'buy' else "🟡"
        message += f"\n💡 交易建议: {action_emoji} {suggestion['action'].upper()}\n"
        message += f"风险等级: {suggestion['risk_level']}\n"
        message += f"建议理由: {suggestion['reasoning']}\n"
        
        return message
    
    def _save_analysis_report(self, judgment_result):
        """
        保存详细分析报告到文件
        """
        try:
            timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
            filename = f"reports/analysis_report_{self.symbol.replace('/', '_')}_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"多级别线段终结分析报告\n")
                f.write(f"=" * 50 + "\n")
                f.write(f"交易对: {self.symbol}\n")
                f.write(f"分析时间: {pd.Timestamp.now()}\n")
                f.write(f"最终判断: {'线段终结' if judgment_result['final_judgment'] else '线段未终结'}\n")
                f.write(f"置信度: {judgment_result['confidence']}%\n")
                f.write(f"判断理由: {judgment_result['reason']}\n")
                f.write(f"趋势一致性: {'是' if judgment_result['trend_consistency'] else '否'}\n\n")
                
                f.write("各级别详细分析:\n")
                f.write("-" * 30 + "\n")
                for tf, result in judgment_result['level_results'].items():
                    f.write(f"\n{tf} 级别:\n")
                    f.write(f"  线段终结: {'是' if result['segment_ended'] else '否'}\n")
                    f.write(f"  当前趋势: {result['current_trend']}\n")
                    f.write(f"  最新价格: {result['last_price']}\n")
                    f.write(f"  数据质量: {result['data_quality']} 条K线\n")
                    f.write(f"  背驰信息:\n")
                    for key, value in result['divergence'].items():
                        f.write(f"    {key}: {value}\n")
                    f.write(f"  分析详情: {result['details']}\n")
                
                # 添加交易建议
                suggestion = self.multi_analyzer.get_trading_suggestion(judgment_result)
                f.write(f"\n交易建议:\n")
                f.write("-" * 30 + "\n")
                f.write(f"建议操作: {suggestion['action']}\n")
                f.write(f"风险等级: {suggestion['risk_level']}\n")
                f.write(f"建议理由: {suggestion['reasoning']}\n")
            
            logger.info(f"分析报告已保存: {filename}")
            
        except Exception as e:
            logger.error(f"保存分析报告失败: {str(e)}")
    
    def _log_status_summary(self, judgment_result):
        """
        记录状态摘要
        """
        ended_count = sum(1 for r in judgment_result['level_results'].values() if r['segment_ended'])
        total_count = len(judgment_result['level_results'])
        
        logger.info(f"状态摘要: {ended_count}/{total_count} 个级别显示终结信号, "
                   f"置信度: {judgment_result['confidence']}%, "
                   f"趋势一致: {'是' if judgment_result['trend_consistency'] else '否'}")
    
    def start_scheduler(self, interval=5):
        """
        启动定时调度器
        
        Args:
            interval: 检查间隔，单位为分钟
        """
        logger.info(f"启动多级别定时调度器，间隔: {interval}分钟")
        
        self.scheduler = Scheduler(interval=interval)
        self.scheduler.add_job(self.run_once)
        self.scheduler.start()
    
    def generate_charts(self):
        """
        生成缠论分析图表
        
        Returns:
            dict: 包含各个时间周期图表路径的字典
        """
        logger.info(f"开始为 {self.symbol} 生成缠论分析图表")
        chart_paths = {}
        
        try:
            # 生成多时间周期联合分析图表
            multi_chart_path = self.multi_analyzer.plot_multi_timeframe_analysis()
            if multi_chart_path:
                chart_paths['multi'] = multi_chart_path
                logger.info(f"已生成多时间周期联合分析图表: {multi_chart_path}")
            
            # 获取多时间周期数据
            multi_data = self.multi_analyzer.get_multi_level_data(limit=200)
            
            # 为每个时间周期生成单独的分析图表
            for tf, df in multi_data.items():
                if df is not None and not df.empty:
                    analyzer = self.multi_analyzer.analyzers[tf]
                    analyzer.analyze(df)
                    
                    # 生成图表
                    from chart_plotter import ChartPlotter
                    plotter = ChartPlotter()
                    
                    # 为DataFrame添加交易对和时间周期信息，方便绘图
                    df.attrs['symbol'] = self.symbol
                    df.attrs['timeframe'] = tf
                    
                    chart_path = plotter.plot_chan_analysis(
                        df=df,
                        symbol=self.symbol,
                        timeframe=tf,
                        fx_list=analyzer.fx_list,
                        bi_list=analyzer.bi_list,
                        xd_list=analyzer.xd_list,
                        show_ma=True,
                        show_macd=True
                    )
                    
                    if chart_path:
                        chart_paths[tf] = chart_path
                        logger.info(f"已生成 {tf} 时间周期分析图表: {chart_path}")
            
            return chart_paths
            
        except Exception as e:
            logger.error(f"生成缠论分析图表时出错: {str(e)}")
            return chart_paths

    def run_comprehensive_analysis(self, timeframe='15m', print_report=True):
        """
        运行综合市场分析（包含多级别分析和市场分析）
        
        参数:
            timeframe: 主要分析时间周期
            print_report: 是否打印详细报告
            
        返回:
            dict: 综合分析结果
        """
        logger.info(f"开始执行 {self.symbol} 综合市场分析")
        
        try:
            # 执行综合市场分析
            analysis_result = self.multi_analyzer.comprehensive_market_analysis(timeframe=timeframe)
            
            if analysis_result is None:
                logger.error("综合市场分析失败")
                return None
            
            # 获取综合建议
            advice = analysis_result['comprehensive_advice']
            current_action = advice['final_action']
            current_confidence = advice['confidence']
            
            # 检查是否有重要的交易信号
            if current_action != 'wait' and current_confidence >= 70:
                # 发送高置信度交易信号通知
                message = self._format_comprehensive_notification(analysis_result)
                self.notifier.send(message)
                logger.warning(f"{self.symbol} 高置信度交易信号: {current_action}，置信度: {current_confidence}%")
                
                # 保存综合分析报告
                self._save_comprehensive_report(analysis_result)
            
            # 打印详细报告
            if print_report:
                self.multi_analyzer.print_comprehensive_report(analysis_result)
            
            # 输出摘要信息
            logger.info(f"{self.symbol} 综合分析完成 - 建议: {current_action}, 置信度: {current_confidence}%")
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"执行综合市场分析时出错: {str(e)}")
            return None
    
    def _format_comprehensive_notification(self, analysis_result):
        """
        格式化综合分析通知消息
        """
        advice = analysis_result['comprehensive_advice']
        market = analysis_result['market_analysis']
        multi = analysis_result['multi_level_analysis']
        
        action_emoji = {'buy': '🟢', 'sell': '🔴', 'wait': '🟡'}
        risk_emoji = {'low': '🟢', 'medium': '🟡', 'high': '🔴'}
        
        message = f"🚨 {analysis_result['symbol']} 综合交易信号 🚨\n"
        message += f"时间: {analysis_result['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}\n"
        message += f"当前价格: {analysis_result['current_price']:.4f}\n\n"
        
        message += f"💡 综合建议: {action_emoji.get(advice['final_action'], '')} {advice['final_action'].upper()}\n"
        message += f"置信度: {advice['confidence']}/100\n"
        message += f"风险等级: {risk_emoji.get(advice['risk_level'], '')} {advice['risk_level']}\n\n"
        
        if advice['final_action'] != 'wait':
            message += f"📊 交易参数:\n"
            message += f"  入场价格: {advice['entry_price']:.4f}\n"
            if advice['stop_loss']:
                message += f"  止损位: {advice['stop_loss']:.4f}\n"
            if advice['take_profit']:
                message += f"  止盈位: {advice['take_profit']:.4f}\n"
            message += f"  建议仓位: {advice['position_size']}\n\n"
        
        message += f"🧠 决策依据:\n"
        for reason in advice['reasoning'][:3]:  # 只显示前3个理由
            message += f"  • {reason}\n"
        
        # 添加关键信号信息
        if market['buy_signals']:
            strongest_buy = market['buy_signals'][0]
            message += f"\n🟢 最强买入信号: {strongest_buy['type']} (强度: {strongest_buy['strength']}/10)\n"
        
        if market['sell_signals']:
            strongest_sell = market['sell_signals'][0]
            message += f"\n🔴 最强卖出信号: {strongest_sell['type']} (强度: {strongest_sell['strength']}/10)\n"
        
        message += f"\n🔄 多级别状态: {'线段终结' if multi['final_judgment'] else '线段未终结'} ({multi['confidence']}%)\n"
        
        return message
    
    def _save_comprehensive_report(self, analysis_result):
        """
        保存综合分析报告到文件
        """
        try:
            timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
            filename = f"reports/comprehensive_report_{analysis_result['symbol'].replace('/', '_')}_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"综合市场分析报告\n")
                f.write(f"=" * 80 + "\n")
                f.write(f"交易对: {analysis_result['symbol']}\n")
                f.write(f"分析时间: {analysis_result['timestamp']}\n")
                f.write(f"当前价格: {analysis_result['current_price']:.4f}\n")
                f.write(f"主要周期: {analysis_result['timeframe']}\n\n")
                
                # 缠论分析结果
                chan = analysis_result['chan_analysis']
                f.write(f"缠论分析结果:\n")
                f.write(f"-" * 40 + "\n")
                f.write(f"分型数量: {chan['fractal_count']}\n")
                f.write(f"笔数量: {chan['stroke_count']}\n")
                f.write(f"线段数量: {chan['segment_count']}\n")
                f.write(f"中枢数量: {chan['pivot_count']}\n")
                f.write(f"当前趋势: {chan['latest_trend']}\n\n")
                
                # 多级别分析结果
                multi = analysis_result['multi_level_analysis']
                f.write(f"多级别分析结果:\n")
                f.write(f"-" * 40 + "\n")
                f.write(f"线段终结: {'是' if multi['final_judgment'] else '否'}\n")
                f.write(f"置信度: {multi['confidence']}%\n")
                f.write(f"趋势一致性: {'是' if multi['trend_consistency'] else '否'}\n")
                f.write(f"判断理由: {multi['reason']}\n\n")
                
                # 市场分析结果
                market = analysis_result['market_analysis']
                f.write(f"市场分析结果:\n")
                f.write(f"-" * 40 + "\n")
                f.write(f"趋势分析: {market['trend_analysis']['description']}\n")
                f.write(f"买入信号数量: {len(market['buy_signals'])}\n")
                f.write(f"卖出信号数量: {len(market['sell_signals'])}\n\n")
                
                # 综合交易建议
                advice = analysis_result['comprehensive_advice']
                f.write(f"综合交易建议:\n")
                f.write(f"-" * 40 + "\n")
                f.write(f"建议操作: {advice['final_action']}\n")
                f.write(f"综合置信度: {advice['confidence']}/100\n")
                f.write(f"风险等级: {advice['risk_level']}\n")
                if advice['final_action'] != 'wait':
                    f.write(f"入场价格: {advice['entry_price']:.4f}\n")
                    if advice['stop_loss']:
                        f.write(f"止损位: {advice['stop_loss']:.4f}\n")
                    if advice['take_profit']:
                        f.write(f"止盈位: {advice['take_profit']:.4f}\n")
                    f.write(f"建议仓位: {advice['position_size']}\n")
                
                f.write(f"\n决策理由:\n")
                for reason in advice['reasoning']:
                    f.write(f"  • {reason}\n")
                
                # 等待条件和风险提示
                if market['waiting_conditions']:
                    f.write(f"\n等待条件:\n")
                    for condition in market['waiting_conditions']:
                        f.write(f"  • {condition}\n")
                
                f.write(f"\n风险提示:\n")
                for warning in market['risk_warning']:
                    f.write(f"  {warning}\n")
            
            logger.info(f"综合分析报告已保存: {filename}")
            
        except Exception as e:
            logger.error(f"保存综合分析报告失败: {str(e)}")


def generate_enhanced_notification_message(symbol, tf, signal_result):
    """生成包含增强分析的通知消息"""
    notification_message = "🚨 {} {} 买卖点信号提醒 🚨\n".format(symbol, tf)
    notification_message += "时间: {}\n".format(signal_result['timestamp'])
    notification_message += "推荐操作: {}\n".format(signal_result['recommendation'].upper())
    notification_message += "信号强度: {}/10\n".format(signal_result['signal_strength'])
    notification_message += "信号说明: {}\n\n".format(signal_result['message'])

    # 增强的买入信号分析
    if signal_result['buy_signals']:
        notification_message += "🟢 买入信号分析:\n"
        for i, signal in enumerate(signal_result['buy_signals'], 1):
            bar_pos = "当前K线" if signal['bar_position'] == 0 else "{}根K线前".format(signal['bar_position'])

            # 基础信息
            signal_priority = "主" if i == 1 else "次要" if signal.get('credibility_score', 5) > 6 else "重复"
            notification_message += "  📍 信号 #{}: {} ({}信号)\n".format(i, signal['type'], signal_priority)
            notification_message += "     基础信息: 价格 {:.4f} | 强度 {}/10 | {}\n".format(
                signal['price'], signal['strength'], bar_pos)

            # 可信度分析
            if 'credibility_score' in signal:
                notification_message += "     可信度评分: {:.1f}/10 ({})\n".format(
                    signal['credibility_score'], signal.get('confidence_level', '未知'))

                if 'detailed_reason' in signal:
                    reason_short = signal['detailed_reason'][:100] + "..." if len(signal['detailed_reason']) > 100 else signal['detailed_reason']
                    notification_message += "     🔍 详细分析: {}\n".format(reason_short)

                if 'risk_warnings' in signal and signal['risk_warnings']:
                    warnings_text = "; ".join(signal['risk_warnings'][:2])
                    notification_message += "     ⚠️ 风险提示: {}\n".format(warnings_text)
            else:
                notification_message += "     可信度评分: 未分析\n"

            if 'description' in signal:
                notification_message += "     说明: {}\n".format(signal['description'])
            if 'reason' in signal and signal['reason']:
                notification_message += "     原因: {}\n".format(signal['reason'])
            notification_message += "\n"

    # 增强的卖出信号分析
    if signal_result['sell_signals']:
        notification_message += "🔴 卖出信号分析:\n"
        for i, signal in enumerate(signal_result['sell_signals'], 1):
            bar_pos = "当前K线" if signal['bar_position'] == 0 else "{}根K线前".format(signal['bar_position'])

            # 基础信息
            signal_priority = "主" if i == 1 else "次要" if signal.get('credibility_score', 5) > 6 else "重复"
            notification_message += "  📍 信号 #{}: {} ({}信号)\n".format(i, signal['type'], signal_priority)
            notification_message += "     基础信息: 价格 {:.4f} | 强度 {}/10 | {}\n".format(
                signal['price'], signal['strength'], bar_pos)

            # 可信度分析
            if 'credibility_score' in signal:
                notification_message += "     可信度评分: {:.1f}/10 ({})\n".format(
                    signal['credibility_score'], signal.get('confidence_level', '未知'))

                if 'detailed_reason' in signal:
                    reason_short = signal['detailed_reason'][:100] + "..." if len(signal['detailed_reason']) > 100 else signal['detailed_reason']
                    notification_message += "     🔍 详细分析: {}\n".format(reason_short)

                if 'risk_warnings' in signal and signal['risk_warnings']:
                    warnings_text = "; ".join(signal['risk_warnings'][:2])
                    notification_message += "     ⚠️ 风险提示: {}\n".format(warnings_text)
            else:
                notification_message += "     可信度评分: 未分析\n"

            if 'description' in signal:
                notification_message += "     说明: {}\n".format(signal['description'])
            if 'reason' in signal and signal['reason']:
                notification_message += "     原因: {}\n".format(signal['reason'])
            notification_message += "\n"

    return notification_message


def main():
    """主入口函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="多级别线段终结判断系统")
    parser.add_argument("--symbols", type=str, default="BTC/USDT,ETH/USDT", 
                        help="要监控的交易对，多个交易对用逗号分隔，例如：BTC/USDT,ETH/USDT")
    parser.add_argument("--exchange", type=str, default=None, help="交易所ID")
    parser.add_argument("--interval", type=int, default=5, help="检查间隔，单位为分钟")
    parser.add_argument("--mode", type=str, default="comprehensive", 
                        choices=["multi", "simple", "comprehensive"], 
                        help="运行模式：multi-多级别联立分析，simple-简单线段终结判断，comprehensive-综合市场分析")
    parser.add_argument("--timeframe", type=str, default="15m",
                        choices=["1m", "5m", "15m", "30m", "1h"],
                        help="综合分析的主要时间周期")
    parser.add_argument("--log-level", type=str, default="INFO", 
                        choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
                        help="日志级别")
    parser.add_argument("--generate-charts", action="store_true",
                        help="是否生成缠论分析图表，默认为否")
    parser.add_argument("--plot-only", action="store_true",
                        help="仅生成图表而不启动监控系统，默认为否")
    parser.add_argument("--run-once", action="store_true",
                        help="仅运行一次分析而不启动定时监控，默认为否")
    parser.add_argument("--check-signals", action="store_true",
                        help="实时检查当前是否有买卖点信号形成")
    parser.add_argument("--check-timeframes", type=str, default="1m,5m,15m,1h",
                        help="实时检查信号的时间周期，多个周期用逗号分隔，例如：1m,5m,15m,1h")
    parser.add_argument("--monitor-signals", action="store_true",
                        help="持续监控买卖点信号，并在发现信号时发送通知")
    parser.add_argument("--monitor-interval", type=int, default=5,
                        help="监控买卖点信号的间隔时间，单位为分钟")
    
    args = parser.parse_args()
    
    # 将交易对字符串转换为列表
    symbols = [s.strip() for s in args.symbols.split(",")]
    
    # 仅生成图表模式
    if args.plot_only:
        for symbol in symbols:
            try:
                logger.info(f"正在为 {symbol} 生成缠论分析图表...")
                detector = MultiLevelSegmentDetector(
                    symbol=symbol, 
                    exchange=args.exchange,
                    log_level=args.log_level
                )
                chart_paths = detector.generate_charts()
                
                if chart_paths:
                    logger.info(f"已为 {symbol} 生成以下图表:")
                    for tf, path in chart_paths.items():
                        logger.info(f"  {tf}: {path}")
                else:
                    logger.warning(f"未能为 {symbol} 生成任何图表")
            except Exception as e:
                logger.error(f"为 {symbol} 生成图表时出错: {str(e)}")
        
        return
    
    # 持续监控买卖点信号模式
    if args.monitor_signals:
        for symbol in symbols:
            try:
                logger.info(f"启动 {symbol} 的买卖点信号监控...")
                detector = MultiLevelSegmentDetector(
                    symbol=symbol, 
                    exchange=args.exchange,
                    log_level=args.log_level
                )
                
                # 定义监控任务
                def signal_monitor():
                    """持续监控买卖点信号"""
                    while True:
                        try:
                            logger.info(f"检查 {symbol} 的买卖点信号...")
                            timeframes = [tf.strip() for tf in args.check_timeframes.split(",")]
                            
                            for tf in timeframes:
                                signal_result = detector.multi_analyzer.check_realtime_signals(timeframe=tf)
                                
                                # 打印结果
                                print("\n" + "-"*80)
                                print("📊 {} {} 实时信号检查结果:".format(symbol, tf))
                                print("-"*80)
                                print("⏰ 检查时间: {}".format(signal_result['timestamp']))
                                
                                if signal_result['buy_signals'] or signal_result['sell_signals']:
                                    action_emoji = {'buy': '🟢 买入', 'sell': '🔴 卖出', 'wait': '🟡 等待'}
                                    print("推荐操作: {}".format(action_emoji.get(signal_result['recommendation'], signal_result['recommendation'])))
                                    print("信号强度: {}/10".format(signal_result['signal_strength']))
                                    print("信号说明: {}".format(signal_result['message']))
                                
                                # 检查是否有新鲜的信号（3根K线内）
                                has_recent_signals = False
                                
                                # 检查买入信号
                                if signal_result['buy_signals']:
                                    for signal in signal_result['buy_signals']:
                                        if signal['bar_position'] <= 3:
                                            has_recent_signals = True
                                            break
                                
                                # 检查卖出信号
                                if not has_recent_signals and signal_result['sell_signals']:
                                    for signal in signal_result['sell_signals']:
                                        if signal['bar_position'] <= 3:
                                            has_recent_signals = True
                                            break
                                
                                # 生成增强的通知消息
                                notification_message = generate_enhanced_notification_message(symbol, tf, signal_result)

                                # 打印买入信号（增强版）
                                if signal_result['buy_signals']:
                                    print("\n🟢 买入信号分析:")
                                    for i, signal in enumerate(signal_result['buy_signals'], 1):
                                        bar_pos = "当前K线" if signal['bar_position'] == 0 else "{}根K线前".format(signal['bar_position'])

                                        # 基础信息
                                        signal_priority = "主" if i == 1 else "次要" if signal.get('credibility_score', 5) > 6 else "重复"
                                        print("  📍 信号 #{}: {} ({}信号)".format(i, signal['type'], signal_priority))
                                        print("     基础信息: 价格 {:.4f} | 强度 {}/10 | {}".format(
                                            signal['price'], signal['strength'], bar_pos))

                                        # 可信度分析
                                        if 'credibility_score' in signal:
                                            print("     可信度评分: {:.1f}/10 ({})".format(
                                                signal['credibility_score'], signal.get('confidence_level', '未知')))

                                            if 'detailed_reason' in signal:
                                                reason_short = signal['detailed_reason'][:80] + "..." if len(signal['detailed_reason']) > 80 else signal['detailed_reason']
                                                print("     🔍 详细分析: {}".format(reason_short))

                                            if 'risk_warnings' in signal and signal['risk_warnings']:
                                                warnings_text = "; ".join(signal['risk_warnings'][:2])
                                                print("     ⚠️ 风险提示: {}".format(warnings_text))
                                        else:
                                            print("     可信度评分: 未分析")

                                        if 'description' in signal:
                                            print("     说明: {}".format(signal['description']))
                                        if 'reason' in signal and signal['reason']:
                                            print("     原因: {}".format(signal['reason']))
                                        print()

                                # 打印卖出信号（增强版）
                                if signal_result['sell_signals']:
                                    print("\n🔴 卖出信号分析:")
                                    for i, signal in enumerate(signal_result['sell_signals'], 1):
                                        bar_pos = "当前K线" if signal['bar_position'] == 0 else "{}根K线前".format(signal['bar_position'])

                                        # 基础信息
                                        signal_priority = "主" if i == 1 else "次要" if signal.get('credibility_score', 5) > 6 else "重复"
                                        print("  📍 信号 #{}: {} ({}信号)".format(i, signal['type'], signal_priority))
                                        print("     基础信息: 价格 {:.4f} | 强度 {}/10 | {}".format(
                                            signal['price'], signal['strength'], bar_pos))

                                        # 可信度分析
                                        if 'credibility_score' in signal:
                                            print("     可信度评分: {:.1f}/10 ({})".format(
                                                signal['credibility_score'], signal.get('confidence_level', '未知')))

                                            if 'detailed_reason' in signal:
                                                reason_short = signal['detailed_reason'][:80] + "..." if len(signal['detailed_reason']) > 80 else signal['detailed_reason']
                                                print("     🔍 详细分析: {}".format(reason_short))

                                            if 'risk_warnings' in signal and signal['risk_warnings']:
                                                warnings_text = "; ".join(signal['risk_warnings'][:2])
                                                print("     ⚠️ 风险提示: {}".format(warnings_text))
                                        else:
                                            print("     可信度评分: 未分析")

                                        if 'description' in signal:
                                            print("     说明: {}".format(signal['description']))
                                        if 'reason' in signal and signal['reason']:
                                            print("     原因: {}".format(signal['reason']))
                                        print()
                                
                                # 保存到文件（所有信号都保存）
                                detector.notifier.send_file(notification_message)
                                
                                # 只有最近的信号才发送到钉钉
                                if has_recent_signals and signal_result['recommendation'] != 'wait':
                                    detector.notifier.send_dingtalk(notification_message)
                                    logger.info("已发送 {} {} 买卖点信号通知到钉钉".format(symbol, tf))
                                else:
                                    logger.info("无新鲜的买卖点信号，仅保存到文件")
                                
                                logger.info("已发送 {} {} 买卖点信号通知".format(symbol, tf))
                                
                                if not signal_result['buy_signals'] and not signal_result['sell_signals']:
                                    print("结果: {}".format(signal_result['message']))
                                
                                print("-"*80)
                        
                            # 按照指定间隔等待
                            time.sleep(args.monitor_interval * 60)
                        except Exception as e:
                            logger.error(f"监控 {symbol} 信号时出错: {str(e)}")
                            time.sleep(60)  # 出错后等待1分钟再重试
                
                # 启动监控线程
                import threading
                thread = threading.Thread(target=signal_monitor, daemon=True)
                thread.start()
                logger.info(f"已启动 {symbol} 的买卖点信号监控，间隔: {args.monitor_interval}分钟")
                
            except Exception as e:
                logger.error(f"启动 {symbol} 的买卖点信号监控失败: {str(e)}")
        
        # 保持主线程运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("用户中断，正在退出...")
        
        return
    
    # 实时检查买卖点信号模式
    if args.check_signals:
        for symbol in symbols:
            try:
                logger.info(f"正在检查 {symbol} 的实时买卖点信号...")
                detector = MultiLevelSegmentDetector(
                    symbol=symbol, 
                    exchange=args.exchange,
                    log_level=args.log_level
                )
                
                # 检查指定时间周期的信号
                timeframes = [tf.strip() for tf in args.check_timeframes.split(",")]
                for tf in timeframes:
                    signal_result = detector.multi_analyzer.check_realtime_signals(timeframe=tf)
                    
                    # 打印结果
                    print("\n" + "-"*80)
                    print("📊 {} {} 实时信号检查结果:".format(symbol, tf))
                    print("-"*80)
                    print("⏰ 检查时间: {}".format(signal_result['timestamp']))
                    
                    if signal_result['buy_signals'] or signal_result['sell_signals']:
                        action_emoji = {'buy': '🟢 买入', 'sell': '🔴 卖出', 'wait': '🟡 等待'}
                        print("推荐操作: {}".format(action_emoji.get(signal_result['recommendation'], signal_result['recommendation'])))
                        print("信号强度: {}/10".format(signal_result['signal_strength']))
                        print("信号说明: {}".format(signal_result['message']))
                    
                    # 检查是否有新鲜的信号（3根K线内）
                    has_recent_signals = False
                    
                    # 检查买入信号
                    if signal_result['buy_signals']:
                        for signal in signal_result['buy_signals']:
                            if signal['bar_position'] <= 3:
                                has_recent_signals = True
                                break
                    
                    # 检查卖出信号
                    if not has_recent_signals and signal_result['sell_signals']:
                        for signal in signal_result['sell_signals']:
                            if signal['bar_position'] <= 3:
                                has_recent_signals = True
                                break
                    
                    # 生成增强的通知消息
                    notification_message = generate_enhanced_notification_message(symbol, tf, signal_result)

                    # 打印买入信号（增强版）
                    if signal_result['buy_signals']:
                        print("\n🟢 买入信号分析:")
                        for i, signal in enumerate(signal_result['buy_signals'], 1):
                            bar_pos = "当前K线" if signal['bar_position'] == 0 else "{}根K线前".format(signal['bar_position'])

                            # 基础信息
                            signal_priority = "主" if i == 1 else "次要" if signal.get('credibility_score', 5) > 6 else "重复"
                            print("  📍 信号 #{}: {} ({}信号)".format(i, signal['type'], signal_priority))
                            print("     基础信息: 价格 {:.4f} | 强度 {}/10 | {}".format(
                                signal['price'], signal['strength'], bar_pos))

                            # 可信度分析
                            if 'credibility_score' in signal:
                                print("     可信度评分: {:.1f}/10 ({})".format(
                                    signal['credibility_score'], signal.get('confidence_level', '未知')))

                                if 'detailed_reason' in signal:
                                    reason_short = signal['detailed_reason'][:80] + "..." if len(signal['detailed_reason']) > 80 else signal['detailed_reason']
                                    print("     🔍 详细分析: {}".format(reason_short))

                                if 'risk_warnings' in signal and signal['risk_warnings']:
                                    warnings_text = "; ".join(signal['risk_warnings'][:2])
                                    print("     ⚠️ 风险提示: {}".format(warnings_text))
                            else:
                                print("     可信度评分: 未分析")

                            if 'description' in signal:
                                print("     说明: {}".format(signal['description']))
                            if 'reason' in signal and signal['reason']:
                                print("     原因: {}".format(signal['reason']))
                            print()

                    # 打印卖出信号（增强版）
                    if signal_result['sell_signals']:
                        print("\n🔴 卖出信号分析:")
                        for i, signal in enumerate(signal_result['sell_signals'], 1):
                            bar_pos = "当前K线" if signal['bar_position'] == 0 else "{}根K线前".format(signal['bar_position'])

                            # 基础信息
                            signal_priority = "主" if i == 1 else "次要" if signal.get('credibility_score', 5) > 6 else "重复"
                            print("  📍 信号 #{}: {} ({}信号)".format(i, signal['type'], signal_priority))
                            print("     基础信息: 价格 {:.4f} | 强度 {}/10 | {}".format(
                                signal['price'], signal['strength'], bar_pos))

                            # 可信度分析
                            if 'credibility_score' in signal:
                                print("     可信度评分: {:.1f}/10 ({})".format(
                                    signal['credibility_score'], signal.get('confidence_level', '未知')))

                                if 'detailed_reason' in signal:
                                    reason_short = signal['detailed_reason'][:80] + "..." if len(signal['detailed_reason']) > 80 else signal['detailed_reason']
                                    print("     🔍 详细分析: {}".format(reason_short))

                                if 'risk_warnings' in signal and signal['risk_warnings']:
                                    warnings_text = "; ".join(signal['risk_warnings'][:2])
                                    print("     ⚠️ 风险提示: {}".format(warnings_text))
                            else:
                                print("     可信度评分: 未分析")

                            if 'description' in signal:
                                print("     说明: {}".format(signal['description']))
                            if 'reason' in signal and signal['reason']:
                                print("     原因: {}".format(signal['reason']))
                            print()
                    
                    # 保存到文件（所有信号都保存）
                    detector.notifier.send_file(notification_message)
                    
                    # 只有最近的信号才发送到钉钉
                    if has_recent_signals and signal_result['recommendation'] != 'wait':
                        detector.notifier.send_dingtalk(notification_message)
                        logger.info("已发送 {} {} 买卖点信号通知到钉钉".format(symbol, tf))
                    else:
                        logger.info("无新鲜的买卖点信号，仅保存到文件")
                    
                    logger.info("已发送 {} {} 买卖点信号通知".format(symbol, tf))
                    
                    if not signal_result['buy_signals'] and not signal_result['sell_signals']:
                        print("结果: {}".format(signal_result['message']))
                    
                    print("-"*80)
            except Exception as e:
                logger.error(f"检查 {symbol} 实时信号时出错: {str(e)}")
        
        return
    
    # 根据模式选择不同的启动方式
    if args.mode == "comprehensive":
        # 综合市场分析模式
        for symbol in symbols:
            try:
                detector = MultiLevelSegmentDetector(
                    symbol=symbol, 
                    exchange=args.exchange,
                    log_level=args.log_level
                )
                
                # 如果需要生成图表
                if args.generate_charts:
                    chart_paths = detector.generate_charts()
                    if chart_paths:
                        logger.info(f"已为 {symbol} 生成图表")
                
                if args.run_once:
                    # 仅运行一次综合分析
                    logger.info(f"开始对 {symbol} 进行一次性综合市场分析...")
                    result = detector.run_comprehensive_analysis(timeframe=args.timeframe)
                    if result:
                        logger.info(f"{symbol} 综合分析完成")
                    else:
                        logger.error(f"{symbol} 综合分析失败")
                else:
                    # 启动定时综合分析
                    def comprehensive_scheduler():
                        """综合分析定时任务"""
                        while True:
                            try:
                                detector.run_comprehensive_analysis(
                                    timeframe=args.timeframe, 
                                    print_report=False  # 定时任务不打印详细报告
                                )
                                time.sleep(args.interval * 60)  # 转换为秒
                            except Exception as e:
                                logger.error(f"{symbol} 定时综合分析出错: {str(e)}")
                                time.sleep(60)  # 出错后等待1分钟再重试
                    
                    import threading
                    thread = threading.Thread(target=comprehensive_scheduler, daemon=True)
                    thread.start()
                    logger.info(f"已启动 {symbol} 的定时综合市场分析，间隔: {args.interval}分钟")
                    
            except Exception as e:
                logger.error(f"启动 {symbol} 的综合市场分析失败: {str(e)}")
    
    elif args.mode == "multi":
        # 多级别联立分析模式，每个交易对独立运行一个多级别分析器
        for symbol in symbols:
            try:
                detector = MultiLevelSegmentDetector(
                    symbol=symbol, 
                    exchange=args.exchange,
                    log_level=args.log_level
                )
                
                # 如果需要生成图表
                if args.generate_charts:
                    chart_paths = detector.generate_charts()
                    if chart_paths:
                        logger.info(f"已为 {symbol} 生成图表")
                
                if args.run_once:
                    # 仅运行一次多级别分析
                    result = detector.run_once()
                    logger.info(f"{symbol} 多级别分析完成，结果: {result}")
                else:
                    # 启动定时监控
                    detector.start_scheduler(interval=args.interval)
                    logger.info(f"已启动 {symbol} 的多级别线段终结判断")
            except Exception as e:
                logger.error(f"启动 {symbol} 的多级别线段终结判断失败: {str(e)}")
    
    else:
        # 简单线段终结判断模式，使用单一级别监控器
        try:
            monitor = SegmentMonitor(symbols=symbols, exchange_id=args.exchange)
            
            # 如果需要生成图表
            if args.generate_charts:
                from chart_plotter import ChartPlotter
                plotter = ChartPlotter()
                
                # 为每个交易对生成5分钟K线图表
                for symbol in symbols:
                    try:
                        # 获取数据
                        klines = monitor.data_fetcher.get_klines(symbol=symbol)
                        if not klines.empty:
                            # 分析K线
                            analyzer = monitor.analyzers[symbol]
                            analyzer.analyze(klines)
                            
                            # 生成图表
                            klines.attrs['symbol'] = symbol
                            klines.attrs['timeframe'] = '5m'
                            
                            chart_path = plotter.plot_chan_analysis(
                                df=klines,
                                symbol=symbol,
                                timeframe='5m',
                                fx_list=analyzer.fx_list,
                                bi_list=analyzer.bi_list,
                                xd_list=analyzer.xd_list
                            )
                            
                            if chart_path:
                                logger.info(f"已为 {symbol} 生成5分钟K线图表: {chart_path}")
                    except Exception as e:
                        logger.error(f"为 {symbol} 生成图表时出错: {str(e)}")
            
            if args.run_once:
                # 仅运行一次简单分析
                for symbol in symbols:
                    result = monitor.check_segment_end(symbol)
                    logger.info(f"{symbol} 简单分析完成，结果: {result}")
            else:
                # 启动定时监控
                monitor.start_schedule()
                logger.info(f"已启动简单线段终结监控，交易对: {symbols}")
        except Exception as e:
            logger.error(f"启动简单线段终结监控失败: {str(e)}")
    
    # 如果不是一次性运行，保持主线程运行
    if not args.run_once:
        try:
            # 保持主线程运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("用户中断，正在退出...")
        except Exception as e:
            logger.error(f"程序运行时出错: {str(e)}")
    else:
        logger.info("一次性分析完成，程序退出")


if __name__ == "__main__":
    main() 