#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试优化后的卖点策略
验证第一、二、三类卖点的止盈止损逻辑
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from chan_analysis import ChanAnalysis
from loguru import logger

def generate_mock_data_for_sell_test(length=100, base_price=100):
    """
    生成用于测试卖点策略的模拟数据
    模拟一个上涨后回调的走势
    """
    np.random.seed(42)
    
    # 生成时间序列
    start_time = datetime.now() - timedelta(hours=length)
    timestamps = [start_time + timedelta(hours=i) for i in range(length)]
    
    # 生成价格数据：先上涨，然后回调
    prices = []
    current_price = base_price
    
    for i in range(length):
        if i < 30:
            # 前30根K线：上涨趋势
            change = np.random.normal(0.5, 1.0)  # 偏向上涨
        elif i < 60:
            # 中间30根K线：震荡整理
            change = np.random.normal(0, 0.8)
        else:
            # 后40根K线：下跌趋势
            change = np.random.normal(-0.3, 1.2)  # 偏向下跌
        
        current_price *= (1 + change / 100)
        prices.append(current_price)
    
    # 生成OHLC数据
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        # 生成开高低收
        open_price = prices[i-1] if i > 0 else close
        high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.5)) / 100)
        low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.5)) / 100)
        volume = np.random.randint(1000, 10000)
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def test_sell_strategies():
    """测试三种类型的卖点策略"""
    print("="*80)
    print("测试优化后的卖点策略")
    print("="*80)
    
    # 生成测试数据
    df = generate_mock_data_for_sell_test(100, 100)
    print(f"\n生成了 {len(df)} 条K线数据")
    print(f"价格范围: {df['low'].min():.2f} - {df['high'].max():.2f}")
    
    # 创建缠论分析器
    chan_analyzer = ChanAnalysis(df)
    
    # 模拟一些中枢数据
    chan_analyzer.pivot_zones = [
        (20, 40, 108, 102),  # 第一个中枢：开始索引，结束索引，上沿，下沿
        (50, 70, 115, 110),  # 第二个中枢
        (75, 90, 105, 100)   # 第三个中枢
    ]
    
    print("\n" + "="*60)
    print("测试不同类型卖点的止盈策略")
    print("="*60)
    
    # 测试第一类卖点
    print("\n【第一类卖点测试】")
    print("场景：趋势转折点，上涨趋势结束")
    entry_point_1 = {'index': 35, 'price': 110.0, 'timestamp': '2024-01-01 11:00:00'}
    
    targets_1 = chan_analyzer.calculate_chan_profit_targets(
        entry_point=entry_point_1,
        signal_type='sell',
        buy_sell_point_type=1,
        related_pivot_idx=0,
        current_level='1h'
    )
    
    print_strategy_details("第一类卖点", targets_1, entry_point_1['price'])
    
    # 测试第二类卖点
    print("\n【第二类卖点测试】")
    print("场景：中枢突破后的反弹卖点")
    entry_point_2 = {'index': 55, 'price': 112.0, 'timestamp': '2024-01-01 15:00:00'}
    
    targets_2 = chan_analyzer.calculate_chan_profit_targets(
        entry_point=entry_point_2,
        signal_type='sell',
        buy_sell_point_type=2,
        related_pivot_idx=1,
        current_level='30m'
    )
    
    print_strategy_details("第二类卖点", targets_2, entry_point_2['price'])
    
    # 测试第三类卖点
    print("\n【第三类卖点测试】")
    print("场景：中枢震荡中的卖点")
    entry_point_3 = {'index': 80, 'price': 103.0, 'timestamp': '2024-01-01 20:00:00'}
    
    targets_3 = chan_analyzer.calculate_chan_profit_targets(
        entry_point=entry_point_3,
        signal_type='sell',
        buy_sell_point_type=3,
        related_pivot_idx=2,
        current_level='15m'
    )
    
    print_strategy_details("第三类卖点", targets_3, entry_point_3['price'])
    
    # 对比分析
    print("\n" + "="*60)
    print("策略对比分析")
    print("="*60)
    
    strategies = [
        ("第一类卖点", targets_1, entry_point_1['price']),
        ("第二类卖点", targets_2, entry_point_2['price']),
        ("第三类卖点", targets_3, entry_point_3['price'])
    ]
    
    print(f"{'策略类型':<12} {'止损比例':<10} {'第一止盈':<10} {'第二止盈':<10} {'第三止盈':<10} {'风险收益比':<10}")
    print("-" * 70)
    
    for strategy_name, targets, entry_price in strategies:
        stop_loss_pct = ((targets['stop_loss'] / entry_price - 1) * 100) if targets['stop_loss'] else 0
        profit_targets = targets.get('profit_targets', [])
        
        tp1_pct = ((profit_targets[0]['price'] / entry_price - 1) * 100) if len(profit_targets) > 0 else 0
        tp2_pct = ((profit_targets[1]['price'] / entry_price - 1) * 100) if len(profit_targets) > 1 else 0
        tp3_pct = ((profit_targets[2]['price'] / entry_price - 1) * 100) if len(profit_targets) > 2 else 0
        
        risk_reward = targets.get('risk_reward_ratio', 0)
        
        print(f"{strategy_name:<12} {stop_loss_pct:>+7.1f}% {tp1_pct:>+7.1f}% {tp2_pct:>+7.1f}% {tp3_pct:>+7.1f}% {risk_reward:>8.2f}")

def print_strategy_details(strategy_name, targets, entry_price):
    """打印策略详细信息"""
    print(f"\n策略描述: {targets.get('strategy_description', 'N/A')}")
    print(f"入场价格: {entry_price:.2f}")
    
    if targets.get('stop_loss'):
        stop_loss_pct = (targets['stop_loss'] / entry_price - 1) * 100
        print(f"止损位: {targets['stop_loss']:.2f} ({stop_loss_pct:+.1f}%)")
    
    profit_targets = targets.get('profit_targets', [])
    if profit_targets:
        print("止盈目标:")
        for i, target in enumerate(profit_targets, 1):
            print(f"  目标{i}: {target['price']:.2f} ({target['expected_return']:+.1f}%) - {target['description']} - 减仓{target['percentage']*100:.0f}%")
    
    position_mgmt = targets.get('position_management', {})
    if position_mgmt:
        print(f"仓位管理: 初始仓位{position_mgmt.get('initial_position', 0)*100:.0f}%, 最大仓位{position_mgmt.get('max_position', 0)*100:.0f}%")
    
    risk_reward = targets.get('risk_reward_ratio')
    if risk_reward:
        print(f"风险收益比: 1:{risk_reward:.2f}")

if __name__ == "__main__":
    # 设置日志
    logger.add("logs/sell_strategy_test_{time}.log", rotation="100 MB")
    
    try:
        test_sell_strategies()
        print("\n✅ 卖点策略测试完成")
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        logger.error(f"卖点策略测试失败: {str(e)}")
