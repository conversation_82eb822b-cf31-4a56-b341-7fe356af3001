#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from chan_analysis import ChanAnalysis

def test_1m_level_support():
    """测试1m级别的判断逻辑"""
    print("="*80)
    print("1分钟级别判断逻辑测试")
    print("="*80)
    
    # 生成1分钟级别的模拟数据
    np.random.seed(42)
    length = 200  # 200根1分钟K线，约3.3小时
    base_price = 2650.0
    
    start_time = datetime.now() - timedelta(minutes=length)
    timestamps = [start_time + timedelta(minutes=i) for i in range(length)]
    
    prices = []
    current_price = base_price
    
    for i in range(length):
        # 1分钟级别的波动相对较小
        change = np.random.normal(0, 0.3)  # 较小的波动率
        current_price *= (1 + change / 100)
        current_price = max(2600, min(2700, current_price))
        prices.append(current_price)
    
    # 确保最后价格接近基准价格
    prices[-1] = base_price
    
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        open_price = prices[i-1] if i > 0 else close
        high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.2)) / 100)
        low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.2)) / 100)
        volume = np.random.randint(1000, 5000)  # 1分钟级别成交量较小
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    
    print(f"生成了 {len(df)} 条1分钟K线数据")
    print(f"价格范围: {df['low'].min():.2f} - {df['high'].max():.2f}")
    print(f"当前价格: {df['close'].iloc[-1]:.2f}")
    print(f"时间跨度: {(timestamps[-1] - timestamps[0]).total_seconds() / 60:.0f} 分钟")
    
    # 创建缠论分析器
    chan_analyzer = ChanAnalysis(df)
    chan_analyzer.pivot_zones = [
        (50, 100, 2670, 2630),   # 1分钟级别的小中枢
        (120, 160, 2680, 2640),  # 另一个小中枢
    ]
    
    # 测试不同时间级别的对比，重点关注1m级别
    timeframes = ['1m', '5m', '15m', '30m', '1h']
    entry_point = {
        'index': len(df) - 1,
        'price': 2650.0,
        'timestamp': df['timestamp'].iloc[-1].strftime('%Y-%m-%d %H:%M:%S')
    }
    
    print("\n" + "="*80)
    print("1分钟级别特征分析")
    print("="*80)
    
    # 获取1m级别的特征信息
    level_info_1m = chan_analyzer._get_level_specific_pivot_info('1m')
    if level_info_1m:
        print("1分钟级别特征:")
        print(f"  - 描述: {level_info_1m['description']}")
        print(f"  - 中枢高度比例: {level_info_1m['pivot_height_ratio']*100:.1f}%")
        print(f"  - 延伸比例: {level_info_1m['extension_ratio']:.1f}")
        print(f"  - 波动率因子: {level_info_1m['volatility_factor']:.1f}")
        print(f"  - 最少K线数: {level_info_1m['min_bars_in_pivot']}")
        
        # 计算1m级别的中枢范围
        pivot_height = entry_point['price'] * level_info_1m['pivot_height_ratio']
        print(f"  - 基于当前价格的中枢高度: {pivot_height:.2f}")
        print(f"  - 中枢上沿: {entry_point['price'] + pivot_height/2:.2f}")
        print(f"  - 中枢下沿: {entry_point['price'] - pivot_height/2:.2f}")
    
    print("\n" + "="*80)
    print("多级别买入信号对比（包含1m级别）")
    print("="*80)
    
    buy_results = {}
    for tf in timeframes:
        try:
            targets = chan_analyzer.calculate_chan_profit_targets(
                entry_point=entry_point,
                signal_type='buy',
                buy_sell_point_type=2,  # 第二类买点
                related_pivot_idx=0,
                current_level=tf
            )
            buy_results[tf] = targets
            
            print(f"\n【{tf}级别买入分析】")
            level_info = chan_analyzer._get_level_specific_pivot_info(tf)
            if level_info:
                print(f"级别描述: {level_info.get('description', 'N/A')}")
            
            profit_targets = targets.get('profit_targets', [])
            stop_loss = targets.get('stop_loss', 0)
            
            print(f"止损: {stop_loss:.2f}")
            for i, target in enumerate(profit_targets, 1):
                print(f"止盈{i}: {target['price']:.2f} ({target['expected_return']:+.1f}%) - {target['description']}")
                
        except Exception as e:
            print(f"❌ {tf}级别分析失败: {str(e)}")
            buy_results[tf] = None
    
    print("\n" + "="*80)
    print("多级别卖出信号对比（包含1m级别）")
    print("="*80)
    
    sell_results = {}
    for tf in timeframes:
        try:
            targets = chan_analyzer.calculate_chan_profit_targets(
                entry_point=entry_point,
                signal_type='sell',
                buy_sell_point_type=2,  # 第二类卖点
                related_pivot_idx=0,
                current_level=tf
            )
            sell_results[tf] = targets
            
            print(f"\n【{tf}级别卖出分析】")
            profit_targets = targets.get('profit_targets', [])
            stop_loss = targets.get('stop_loss', 0)
            
            print(f"止损: {stop_loss:.2f}")
            for i, target in enumerate(profit_targets, 1):
                print(f"止盈{i}: {target['price']:.2f} ({target['expected_return']:+.1f}%) - {target['description']}")
                
        except Exception as e:
            print(f"❌ {tf}级别分析失败: {str(e)}")
            sell_results[tf] = None
    
    # 对比分析表格
    print("\n" + "="*80)
    print("级别对比分析表（重点关注1m级别）")
    print("="*80)
    
    print(f"{'级别':<6} {'方向':<4} {'止盈1':<10} {'止盈1%':<8} {'中枢高度%':<10} {'延伸比例':<8}")
    print("-" * 60)
    
    for tf in timeframes:
        level_info = chan_analyzer._get_level_specific_pivot_info(tf)
        pivot_height_pct = level_info['pivot_height_ratio'] * 100 if level_info else 0
        extension_ratio = level_info['extension_ratio'] if level_info else 0
        
        # 买入信号
        if buy_results.get(tf):
            buy_targets = buy_results[tf].get('profit_targets', [])
            if buy_targets:
                tp1 = buy_targets[0]['price']
                tp1_pct = (tp1 / entry_point['price'] - 1) * 100
                print(f"{tf:<6} {'买':<4} {tp1:<10.2f} {tp1_pct:<8.1f} {pivot_height_pct:<10.1f} {extension_ratio:<8.1f}")
        
        # 卖出信号
        if sell_results.get(tf):
            sell_targets = sell_results[tf].get('profit_targets', [])
            if sell_targets:
                tp1 = sell_targets[0]['price']
                tp1_pct = (tp1 / entry_point['price'] - 1) * 100
                print(f"{tf:<6} {'卖':<4} {tp1:<10.2f} {tp1_pct:<8.1f} {pivot_height_pct:<10.1f} {extension_ratio:<8.1f}")
    
    print("\n" + "="*80)
    print("1分钟级别特点分析")
    print("="*80)
    
    print("📊 1分钟级别的特点:")
    print("- ⚡ 最小时间单位：适合超短线、高频交易")
    print("- 📉 中枢高度最小：约1.5%，反映微观波动")
    print("- 🎯 延伸比例最小：0.8倍，目标相对保守")
    print("- 📈 波动率因子最小：0.8倍，风险控制严格")
    print("- ⏱️ 时间敏感性最高：信号时效性要求极高")
    
    print("\n🔍 1分钟级别的应用场景:")
    print("- 日内超短线交易")
    print("- 精确的进出场时机把握")
    print("- 大级别信号的精细化执行")
    print("- 高频交易策略的基础")
    print("- 风险控制的最后防线")
    
    print("\n⚖️ 与其他级别的关系:")
    print("- 1m < 5m < 15m < 30m < 1h (级别递增)")
    print("- 中枢高度: 1.5% < 2.0% < 3.5% < 5.0% < 8.0%")
    print("- 延伸比例: 0.8 < 1.0 < 1.5 < 2.0 < 2.5")
    print("- 适用性: 超短线 < 短线 < 中短线 < 中线 < 中长线")
    
    # 验证级别关系
    print("\n" + "="*80)
    print("级别关系验证")
    print("="*80)
    
    # 验证买入信号的级别递增关系
    buy_tp1_values = []
    valid_levels = []
    for tf in timeframes:
        if buy_results.get(tf) and buy_results[tf].get('profit_targets'):
            buy_tp1_values.append(buy_results[tf]['profit_targets'][0]['price'])
            valid_levels.append(tf)
    
    if len(buy_tp1_values) >= 2:
        is_ascending = all(buy_tp1_values[i] <= buy_tp1_values[i+1] for i in range(len(buy_tp1_values)-1))
        print(f"✅ 买入止盈目标级别递增关系: {'正确' if is_ascending else '错误'}")
        
        for i, tf in enumerate(valid_levels):
            if i == 0:
                print(f"  - {tf}: {buy_tp1_values[i]:.2f} (基准)")
            else:
                change = (buy_tp1_values[i] / buy_tp1_values[0] - 1) * 100
                print(f"  - {tf}: {buy_tp1_values[i]:.2f} (+{change:.1f}%)")
    
    return buy_results, sell_results

if __name__ == "__main__":
    try:
        buy_results, sell_results = test_1m_level_support()
        print("\n✅ 1分钟级别判断逻辑测试完成")
        print("现在系统完全支持1m级别的缠论分析和止盈止损计算")
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
