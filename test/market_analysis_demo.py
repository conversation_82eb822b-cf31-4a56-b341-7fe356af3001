#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
市场分析演示程序 - 实际使用场景
"""
import pandas as pd
from datetime import datetime, timedelta
from chan_analysis import ChanAnalysis
from chart_plotter import ChartPlotter
from market_analyzer import MarketAnalyzer
from loguru import logger

def create_realistic_data():
    """
    创建更真实的市场数据
    """
    # 生成100个5分钟K线数据
    base_time = datetime.now() - timedelta(hours=8)
    timestamps = [base_time + timedelta(minutes=5*i) for i in range(100)]
    
    # 模拟真实的BTC价格走势
    import numpy as np
    np.random.seed(42)
    
    base_price = 50000
    prices = [base_price]
    
    # 模拟价格随机游走，但有趋势性
    for i in range(1, 100):
        # 添加趋势因子
        if i < 30:  # 前30根K线上涨趋势
            trend = 0.002
        elif i < 60:  # 中间30根K线震荡
            trend = 0.0005 * np.sin(i * 0.3)
        else:  # 后40根K线下跌趋势
            trend = -0.001
        
        # 随机波动 + 趋势
        change = np.random.normal(trend, 0.01)
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    # 生成OHLC数据
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        # 更真实的OHLC生成
        volatility = abs(np.random.normal(0, 0.005))
        
        open_price = close * (1 + np.random.normal(0, 0.002))
        high = max(open_price, close) * (1 + volatility)
        low = min(open_price, close) * (1 - volatility)
        volume = np.random.lognormal(8, 0.5)  # 对数正态分布的成交量
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def analyze_current_market():
    """
    分析当前市场状态
    """
    print("🔍 正在分析当前市场状态...")
    print("="*60)
    
    # 1. 获取数据（这里用模拟数据，实际使用时替换为真实数据）
    df = create_realistic_data()
    current_time = df['timestamp'].iloc[-1].strftime('%Y-%m-%d %H:%M:%S')
    current_price = df['close'].iloc[-1]
    
    print(f"📊 数据时间: {current_time}")
    print(f"💰 当前价格: {current_price:.2f} USDT")
    
    # 2. 缠论分析
    chan = ChanAnalysis()
    chan.set_klines(df)
    is_ended, reason = chan.analyze()
    
    fx_list = chan.fx_list
    bi_list = chan.bi_list
    
    print(f"📈 技术分析: {len(fx_list)}个分型, {len(bi_list)}笔")
    
    # 3. 识别中枢和买卖点
    plotter = ChartPlotter()
    pivot_zones = plotter.identify_pivot_zones(bi_list, df)
    
    # 识别各类买卖点
    buy_points1, sell_points1 = plotter.identify_first_buy_sell_points(bi_list, df, pivot_zones)
    buy_points2, sell_points2 = plotter.identify_second_buy_sell_points(bi_list, df, pivot_zones)
    buy_points3, sell_points3 = plotter.identify_third_buy_sell_points(bi_list, df, pivot_zones)
    
    # 4. 市场分析
    buy_points_all = {
        '1': (buy_points1, sell_points1),
        '2': (buy_points2, sell_points2),
        '3': (buy_points3, sell_points3)
    }
    
    market_analyzer = MarketAnalyzer()
    analysis = market_analyzer.analyze_market_status(
        df, bi_list, pivot_zones, buy_points_all, buy_points_all
    )
    
    # 5. 输出分析结果
    print_market_summary(analysis)
    
    return analysis

def print_market_summary(analysis):
    """
    打印市场分析摘要
    """
    print("\n" + "="*60)
    print("📋 市场分析摘要")
    print("="*60)
    
    # 当前状态
    trend = analysis['trend_analysis']
    print(f"📈 市场趋势: {trend['description']}")
    
    # 买卖信号
    has_signals = False
    
    if analysis['buy_signals']:
        has_signals = True
        signal = analysis['buy_signals'][0]
        print(f"🟢 买入信号: {signal['type']}买点")
        print(f"   信号强度: {signal['strength']}/10")
        print(f"   信号价格: {signal['price']:.2f}")
        print(f"   发生时间: {signal['time_ago_minutes']:.0f}分钟前")
    
    if analysis['sell_signals']:
        has_signals = True
        signal = analysis['sell_signals'][0]
        print(f"🔴 卖出信号: {signal['type']}卖点")
        print(f"   信号强度: {signal['strength']}/10")
        print(f"   信号价格: {signal['price']:.2f}")
        print(f"   发生时间: {signal['time_ago_minutes']:.0f}分钟前")
    
    if not has_signals:
        print("⏳ 当前没有明确的买卖信号")
    
    # 操作建议
    advice = analysis['operation_advice']
    action_map = {'buy': '🟢 买入', 'sell': '🔴 卖出', 'wait': '⏸️ 等待'}
    
    print(f"\n💡 操作建议: {action_map[advice['action']]}")
    print(f"   信心度: {advice['confidence']}/10")
    print(f"   建议: {advice['description']}")
    
    if advice['action'] != 'wait':
        size_map = {'light': '轻仓', 'medium': '中仓', 'heavy': '重仓'}
        period_map = {'short': '短线', 'medium': '中线', 'long': '长线'}
        
        print(f"   建议仓位: {size_map[advice['position_size']]}")
        print(f"   持仓周期: {period_map[advice['holding_period']]}")
        print(f"   入场价格: {advice['entry_price']:.2f}")
        print(f"   止损价格: {advice['stop_loss']:.2f}")
        print(f"   保守止盈: {advice['take_profit_conservative']:.2f}")
        print(f"   积极止盈: {advice['take_profit_aggressive']:.2f}")
        
        # 计算风险收益比
        if advice['action'] == 'buy':
            risk = advice['entry_price'] - advice['stop_loss']
            reward_conservative = advice['take_profit_conservative'] - advice['entry_price']
            reward_aggressive = advice['take_profit_aggressive'] - advice['entry_price']
        else:
            risk = advice['stop_loss'] - advice['entry_price']
            reward_conservative = advice['entry_price'] - advice['take_profit_conservative']
            reward_aggressive = advice['entry_price'] - advice['take_profit_aggressive']
        
        if risk > 0:
            ratio_conservative = reward_conservative / risk
            ratio_aggressive = reward_aggressive / risk
            print(f"   风险收益比: 1:{ratio_conservative:.1f} (保守) / 1:{ratio_aggressive:.1f} (积极)")
    
    # 等待条件
    if analysis['waiting_conditions']:
        print(f"\n⏰ 等待条件:")
        for condition in analysis['waiting_conditions'][:3]:  # 只显示前3个
            print(f"   • {condition}")
    
    # 风险提示
    print(f"\n⚠️ 风险提示:")
    for warning in analysis['risk_warning'][:3]:  # 只显示前3个
        print(f"   {warning}")

def main():
    """
    主函数
    """
    print("🚀 BTC/USDT 5分钟线缠论分析系统")
    print("="*60)
    
    try:
        # 分析当前市场
        analysis = analyze_current_market()
        
        print(f"\n✅ 分析完成！")
        print(f"📝 建议根据以上分析结果制定交易策略")
        print(f"⚠️ 请注意风险控制，严格执行止损")
        
    except Exception as e:
        logger.error(f"分析过程中出现错误: {e}")
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main() 