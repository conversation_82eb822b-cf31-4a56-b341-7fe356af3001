#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试增强的信号输出功能

验证新的可信度分析是否正确应用到实际的信号输出中
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from multi_level_analysis import MultiLevelAnalyzer

def test_enhanced_signal_output():
    """测试增强的信号输出功能"""
    print("="*80)
    print("增强信号输出功能测试")
    print("="*80)
    
    try:
        # 创建多级别分析器
        analyzer = MultiLevelAnalyzer(symbol="ETH/USDT")
        
        print("📊 正在获取ETH/USDT实时数据并分析...")
        
        # 测试不同时间级别
        timeframes = ['15m', '1h', '5m', '1m']
        
        for tf in timeframes:
            print(f"\n{'='*60}")
            print(f"🔍 测试 {tf} 级别信号分析")
            print(f"{'='*60}")
            
            try:
                # 获取实时信号
                result = analyzer.check_realtime_signals(timeframe=tf)
                
                print(f"⏰ 检查时间: {result['timestamp']}")
                print(f"推荐操作: {result['recommendation'].upper()}")
                print(f"信号强度: {result['signal_strength']}/10")
                print(f"信号说明: {result['message']}")
                
                # 检查买入信号
                if result['buy_signals']:
                    print("\n🟢 买入信号分析:")
                    for i, signal in enumerate(result['buy_signals'], 1):
                        bar_pos = "当前K线" if signal['bar_position'] == 0 else f"{signal['bar_position']}根K线前"
                        
                        # 基础信息
                        signal_priority = "主" if i == 1 else "次要" if signal.get('credibility_score', 5) > 6 else "重复"
                        print(f"  📍 信号 #{i}: {signal['type']} ({signal_priority}信号)")
                        print(f"     基础信息: 价格 {signal['price']:.4f} | 强度 {signal['strength']}/10 | {bar_pos}")
                        
                        # 可信度分析
                        if 'credibility_score' in signal:
                            print(f"     可信度评分: {signal['credibility_score']:.1f}/10 ({signal.get('confidence_level', '未知')})")
                            
                            if 'detailed_reason' in signal:
                                reason_short = signal['detailed_reason'][:80] + "..." if len(signal['detailed_reason']) > 80 else signal['detailed_reason']
                                print(f"     🔍 详细分析: {reason_short}")
                            
                            if 'risk_warnings' in signal and signal['risk_warnings']:
                                warnings_text = "; ".join(signal['risk_warnings'][:2])
                                print(f"     ⚠️ 风险提示: {warnings_text}")
                        else:
                            print("     可信度评分: 未分析")
                        
                        if 'description' in signal:
                            print(f"     说明: {signal['description']}")
                        if 'reason' in signal and signal['reason']:
                            print(f"     原因: {signal['reason']}")
                        print()
                
                # 检查卖出信号
                if result['sell_signals']:
                    print("\n🔴 卖出信号分析:")
                    for i, signal in enumerate(result['sell_signals'], 1):
                        bar_pos = "当前K线" if signal['bar_position'] == 0 else f"{signal['bar_position']}根K线前"
                        
                        # 基础信息
                        signal_priority = "主" if i == 1 else "次要" if signal.get('credibility_score', 5) > 6 else "重复"
                        print(f"  📍 信号 #{i}: {signal['type']} ({signal_priority}信号)")
                        print(f"     基础信息: 价格 {signal['price']:.4f} | 强度 {signal['strength']}/10 | {bar_pos}")
                        
                        # 可信度分析
                        if 'credibility_score' in signal:
                            print(f"     可信度评分: {signal['credibility_score']:.1f}/10 ({signal.get('confidence_level', '未知')})")
                            
                            if 'detailed_reason' in signal:
                                reason_short = signal['detailed_reason'][:80] + "..." if len(signal['detailed_reason']) > 80 else signal['detailed_reason']
                                print(f"     🔍 详细分析: {reason_short}")
                            
                            if 'risk_warnings' in signal and signal['risk_warnings']:
                                warnings_text = "; ".join(signal['risk_warnings'][:2])
                                print(f"     ⚠️ 风险提示: {warnings_text}")
                        else:
                            print("     可信度评分: 未分析")
                        
                        if 'description' in signal:
                            print(f"     说明: {signal['description']}")
                        if 'reason' in signal and signal['reason']:
                            print(f"     原因: {signal['reason']}")
                        print()
                
                # 检查是否有立即入场提醒
                if 'immediate_entry' in result:
                    print("\n🚨 立即入场提醒:")
                    entry_info = result['immediate_entry']
                    print(f"   {entry_info['message']}")
                
                if not result['buy_signals'] and not result['sell_signals']:
                    print(f"\n结果: {result['message']}")
                
            except Exception as e:
                print(f"❌ {tf} 级别分析失败: {str(e)}")
                import traceback
                traceback.print_exc()
        
        # 验证功能完整性
        print(f"\n{'='*80}")
        print("功能完整性验证")
        print(f"{'='*80}")
        
        verification_results = []
        
        for tf in timeframes:
            try:
                result = analyzer.check_realtime_signals(timeframe=tf)
                
                # 检查是否有信号
                has_signals = bool(result['buy_signals'] or result['sell_signals'])
                
                # 检查是否有可信度分析
                has_credibility = False
                if result['buy_signals']:
                    has_credibility = any('credibility_score' in signal for signal in result['buy_signals'])
                if not has_credibility and result['sell_signals']:
                    has_credibility = any('credibility_score' in signal for signal in result['sell_signals'])
                
                verification_results.append({
                    'timeframe': tf,
                    'has_signals': has_signals,
                    'has_credibility': has_credibility
                })
                
            except Exception as e:
                verification_results.append({
                    'timeframe': tf,
                    'has_signals': False,
                    'has_credibility': False,
                    'error': str(e)
                })
        
        print("验证结果:")
        for result in verification_results:
            tf = result['timeframe']
            if 'error' in result:
                print(f"  {tf}: ❌ 错误 - {result['error']}")
            else:
                signals_status = "✅ 有信号" if result['has_signals'] else "⚪ 无信号"
                credibility_status = "✅ 有可信度分析" if result['has_credibility'] else "❌ 无可信度分析"
                print(f"  {tf}: {signals_status} | {credibility_status}")
        
        # 总结
        total_with_signals = sum(1 for r in verification_results if r['has_signals'])
        total_with_credibility = sum(1 for r in verification_results if r.get('has_credibility', False))
        
        print(f"\n📊 总结:")
        print(f"  有信号的级别: {total_with_signals}/{len(timeframes)}")
        print(f"  有可信度分析的级别: {total_with_credibility}/{len(timeframes)}")
        
        if total_with_credibility > 0:
            print("🎉 增强信号分析功能已成功应用！")
            return True
        else:
            print("⚠️ 增强信号分析功能未生效，需要检查实现")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_comparison():
    """展示新旧输出格式对比"""
    print(f"\n{'='*80}")
    print("新旧输出格式对比")
    print(f"{'='*80}")
    
    print("🔴 旧格式（问题版本）:")
    print("  1. 第二类买点 - 价格: 2604.6200 - 强度: 10/10 - 位置: 17根K线前")
    print("  2. 第二类买点 - 价格: 2604.6200 - 强度: 6/10 - 位置: 17根K线前")
    print("  3. 中枢上沿买点 - 价格: 2612.9600 - 强度: 8/10 - 位置: 当前K线")
    
    print("\n❌ 旧格式的问题:")
    print("  - 重复信号，无法区分")
    print("  - 缺乏判断依据")
    print("  - 无可信度评估")
    print("  - 难以做出决策")
    
    print("\n🟢 新格式（解决方案）:")
    print("  📍 信号 #1: 第二类买点 (主信号)")
    print("     基础信息: 价格 2604.6200 | 强度 10/10 | 17根K线前")
    print("     可信度评分: 7.2/10 (高)")
    print("     🔍 详细分析: 信号类型: 第二类买点 | 技术形态: 良好 - 当前上涨趋势...")
    print("     ⚠️ 风险提示: 信号已过时，入场风险增加")
    print("     说明: 第二类买点 - 下跌中枢完成后的低点回调买入信号")
    print()
    print("  📍 信号 #2: 第二类买点 (重复信号)")
    print("     基础信息: 价格 2604.6200 | 强度 6/10 | 17根K线前")
    print("     可信度评分: 4.8/10 (中等)")
    print("     🔍 详细分析: 与主信号重复，强度较低，可信度一般")
    print("     说明: 第二类买点 - 下跌中枢完成后的低点回调买入信号")
    print()
    print("  📍 信号 #3: 中枢上沿买点 (次要信号)")
    print("     基础信息: 价格 2612.9600 | 强度 8/10 | 当前K线")
    print("     可信度评分: 8.5/10 (极高)")
    print("     🔍 详细分析: 信号类型: 中枢上沿买点 | 技术形态: 优秀 - 回调至支撑...")
    print("     说明: 中枢上沿买点 - 上涨突破中枢后回调至中枢上沿支撑位")
    
    print("\n✅ 新格式的优势:")
    print("  - 清晰的信号优先级（主/次要/重复）")
    print("  - 量化的可信度评分（0-10分）")
    print("  - 详细的分析理由")
    print("  - 明确的风险提示")
    print("  - 易于决策的结构化信息")

if __name__ == "__main__":
    try:
        print("增强信号输出功能测试")
        print("验证可信度分析是否正确应用")
        
        success = test_enhanced_signal_output()
        show_comparison()
        
        if success:
            print("\n🎉 测试成功！增强信号分析功能已正确应用")
            print("💡 现在用户可以获得详细的信号判断理由和可信度评估")
        else:
            print("\n⚠️ 测试发现问题，需要进一步检查实现")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
