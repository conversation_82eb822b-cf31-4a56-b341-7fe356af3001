#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试市场分析功能
"""
import pandas as pd
from datetime import datetime, timedelta
from chan_analysis import ChanAnalysis
from chart_plotter import ChartPlotter
from market_analyzer import MarketAnalyzer
from loguru import logger

def create_test_data():
    """
    创建测试数据
    """
    # 生成200个5分钟K线数据
    base_time = datetime.now() - timedelta(hours=16)
    timestamps = [base_time + timedelta(minutes=5*i) for i in range(200)]
    
    # 模拟价格走势：先上涨，然后震荡，再下跌
    prices = []
    base_price = 50000
    
    for i in range(200):
        if i < 50:  # 前50根K线上涨
            price = base_price + i * 100 + (i % 5) * 50
        elif i < 100:  # 中间50根K线震荡
            price = base_price + 5000 + (i % 10 - 5) * 200
        elif i < 150:  # 接下来50根K线下跌
            price = base_price + 5000 - (i - 100) * 80
        else:  # 最后50根K线小幅反弹
            price = base_price + 1000 + (i - 150) * 20
        
        prices.append(price)
    
    # 生成OHLC数据
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        # 简单模拟开高低收
        open_price = close + (i % 3 - 1) * 50
        high = max(open_price, close) + abs(i % 7) * 30
        low = min(open_price, close) - abs(i % 5) * 25
        volume = 1000 + (i % 10) * 100
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def test_market_analysis():
    """
    测试市场分析功能
    """
    print("🚀 开始测试市场分析功能...")
    
    # 1. 创建测试数据
    df = create_test_data()
    print(f"📊 生成了{len(df)}根K线数据")
    
    # 2. 进行缠论分析
    chan = ChanAnalysis()
    chan.set_klines(df)
    is_ended, reason = chan.analyze()
    
    # 获取分析结果
    fx_list = chan.fx_list
    bi_list = chan.bi_list
    xd_list = chan.xd_list
    
    print(f"✅ 缠论分析完成：识别到{len(fx_list)}个分型，{len(bi_list)}笔，{len(xd_list)}个线段")
    print(f"📊 线段终结状态：{is_ended}, 原因：{reason}")
    
    # 3. 识别中枢和买卖点
    plotter = ChartPlotter()
    
    # 识别中枢区域
    pivot_zones = plotter.identify_pivot_zones(bi_list, df)
    print(f"✅ 识别到{len(pivot_zones)}个中枢")
    
    # 识别第一类买卖点
    buy_points1, sell_points1 = plotter.identify_first_buy_sell_points(bi_list, df, pivot_zones)
    
    # 识别第二类买卖点
    buy_points2, sell_points2 = plotter.identify_second_buy_sell_points(bi_list, df, pivot_zones)
    
    # 识别第三类买卖点
    buy_points3, sell_points3 = plotter.identify_third_buy_sell_points(bi_list, df, pivot_zones)
    
    print(f"📈 买卖点识别完成：")
    print(f"   第一类：{len(buy_points1)}个买点，{len(sell_points1)}个卖点")
    print(f"   第二类：{len(buy_points2)}个买点，{len(sell_points2)}个卖点")
    print(f"   第三类：{len(buy_points3)}个买点，{len(sell_points3)}个卖点")
    
    # 4. 整理买卖点数据
    buy_points_all = {
        '1': (buy_points1, sell_points1),
        '2': (buy_points2, sell_points2),
        '3': (buy_points3, sell_points3)
    }
    
    sell_points_all = {
        '1': (buy_points1, sell_points1),
        '2': (buy_points2, sell_points2),
        '3': (buy_points3, sell_points3)
    }
    
    # 5. 进行市场分析
    market_analyzer = MarketAnalyzer()
    analysis = market_analyzer.analyze_market_status(
        df, bi_list, pivot_zones, buy_points_all, sell_points_all
    )
    
    # 6. 打印分析报告
    market_analyzer.print_analysis_report(analysis)
    
    # 7. 生成图表（可选）
    print(f"\n📊 正在生成分析图表...")
    try:
        plotter.plot_analysis(
            df, bi_list, pivot_zones,
            buy_points1, sell_points1,
            buy_points2, sell_points2,
            buy_points3, sell_points3,
            title="市场分析 - 当前买卖点判断",
            filename="market_analysis_current.png",
            show_ma=True,
            show_macd=True,
            show_first_points=True,
            show_second_points=True,
            show_third_points=True
        )
        print("✅ 图表已保存为 market_analysis_current.png")
    except Exception as e:
        print(f"⚠️ 图表生成失败：{e}")
    
    return analysis

def simulate_real_time_analysis():
    """
    模拟实时分析场景
    """
    print("\n" + "="*80)
    print("🔄 模拟实时分析场景")
    print("="*80)
    
    # 创建基础数据
    df = create_test_data()
    
    # 模拟不同时间点的分析
    time_points = [150, 170, 190, 199]  # 不同的数据截止点
    
    for i, end_idx in enumerate(time_points):
        print(f"\n📅 时间点 {i+1}: 分析前{end_idx+1}根K线数据")
        
        # 截取数据
        df_subset = df.iloc[:end_idx+1].copy()
        
        # 分析
        chan = ChanAnalysis()
        chan.set_klines(df_subset)
        is_ended, reason = chan.analyze()
        
        # 获取分析结果
        fx_list = chan.fx_list
        bi_list = chan.bi_list
        xd_list = chan.xd_list
        
        # 识别中枢和买卖点
        plotter = ChartPlotter()
        pivot_zones = plotter.identify_pivot_zones(bi_list, df_subset)
        
        buy_points1, sell_points1 = plotter.identify_first_buy_sell_points(bi_list, df_subset, pivot_zones)
        buy_points2, sell_points2 = plotter.identify_second_buy_sell_points(bi_list, df_subset, pivot_zones)
        buy_points3, sell_points3 = plotter.identify_third_buy_sell_points(bi_list, df_subset, pivot_zones)
        
        # 整理数据
        buy_points_all = {
            '1': (buy_points1, sell_points1),
            '2': (buy_points2, sell_points2),
            '3': (buy_points3, sell_points3)
        }
        
        sell_points_all = buy_points_all
        
        # 市场分析
        market_analyzer = MarketAnalyzer()
        analysis = market_analyzer.analyze_market_status(
            df_subset, bi_list, pivot_zones, buy_points_all, sell_points_all
        )
        
        # 简化报告
        print(f"💰 当前价格: {analysis['current_price']:.2f}")
        print(f"📈 趋势: {analysis['trend_analysis']['description']}")
        
        if analysis['buy_signals']:
            signal = analysis['buy_signals'][0]
            print(f"🟢 买入信号: {signal['type']}买点，强度{signal['strength']}/10")
        
        if analysis['sell_signals']:
            signal = analysis['sell_signals'][0]
            print(f"🔴 卖出信号: {signal['type']}卖点，强度{signal['strength']}/10")
        
        advice = analysis['operation_advice']
        action_desc = {'buy': '买入', 'sell': '卖出', 'wait': '等待'}
        print(f"💡 建议操作: {action_desc[advice['action']]} (信心度: {advice['confidence']}/10)")
        
        if advice['action'] != 'wait':
            print(f"   止损位: {advice['stop_loss']:.2f}")
            print(f"   止盈位: {advice['take_profit_conservative']:.2f} - {advice['take_profit_aggressive']:.2f}")

if __name__ == "__main__":
    try:
        # 基础测试
        analysis = test_market_analysis()
        
        # 实时分析模拟
        simulate_real_time_analysis()
        
        print(f"\n🎉 测试完成！")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc() 