#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合市场分析测试脚本
演示如何使用集成的MarketAnalyzer功能
"""

import sys
import os
from loguru import logger
from multi_level_analysis import MultiLevelAnalysis

def test_comprehensive_analysis():
    """
    测试综合市场分析功能
    """
    print("🚀 开始测试综合市场分析功能")
    print("="*80)
    
    # 设置日志
    logger.remove()
    logger.add(sys.stderr, level="INFO")
    logger.add("logs/test_comprehensive_{time}.log", rotation="10 MB")
    
    # 创建必要的目录
    os.makedirs("logs", exist_ok=True)
    os.makedirs("reports", exist_ok=True)
    os.makedirs("charts", exist_ok=True)
    
    try:
        # 初始化多级别分析器
        print("📊 初始化多级别分析器...")
        analyzer = MultiLevelAnalysis(symbol="BTC/USDT")
        
        # 执行综合市场分析
        print("🔍 执行综合市场分析...")
        result = analyzer.comprehensive_market_analysis(timeframe='15m', limit=200)
        
        if result is None:
            print("❌ 综合市场分析失败")
            return False
        
        # 打印综合分析报告
        print("📋 生成综合分析报告...")
        analyzer.print_comprehensive_report(result)
        
        # 输出关键信息摘要
        print("\n" + "="*80)
        print("📈 关键信息摘要")
        print("="*80)
        
        advice = result['comprehensive_advice']
        market = result['market_analysis']
        multi = result['multi_level_analysis']
        
        print("🏷️  Trading Pair: " + result['symbol'])
        print("💰 Current Price: " + str(round(result['current_price'], 4)))
        print("📊 Comprehensive Advice: " + advice['final_action'].upper())
        print("🎯 Confidence: " + str(advice['confidence']) + "/100")
        print("⚠️  Risk Level: " + advice['risk_level'])
        
        if advice['final_action'] != 'wait':
            print("💵 Entry Price: " + str(round(advice['entry_price'], 4)))
            if advice['stop_loss']:
                print("🛑 Stop Loss: " + str(round(advice['stop_loss'], 4)))
            if advice['take_profit']:
                print("🎯 Take Profit: " + str(round(advice['take_profit'], 4)))
        
        print("\n🔄 Multi-level Status: " + ("Segment Ended" if multi['final_judgment'] else "Segment Not Ended"))
        print("📊 Market Signals: Buy " + str(len(market['buy_signals'])) + ", Sell " + str(len(market['sell_signals'])))
        
        # 显示决策理由
        print("\n🧠 Main Decision Reasons:")
        for i, reason in enumerate(advice['reasoning'][:3], 1):
            print("   " + str(i) + ". " + reason)
        
        print("\n✅ Comprehensive market analysis test completed!")
        return True
        
    except Exception as e:
        print("❌ Error occurred during test: " + str(e))
        logger.error("Comprehensive market analysis test failed: " + str(e))
        return False

def test_multiple_symbols():
    """
    测试多个交易对的综合分析
    """
    print("\n🚀 Start testing multi-symbol comprehensive analysis")
    print("="*80)
    
    symbols = ["BTC/USDT", "ETH/USDT"]
    
    for symbol in symbols:
        try:
            print("\n📊 Analyzing " + symbol + "...")
            analyzer = MultiLevelAnalysis(symbol=symbol)
            result = analyzer.comprehensive_market_analysis(timeframe='15m')
            
            if result:
                advice = result['comprehensive_advice']
                print("   " + symbol + ": " + advice['final_action'].upper() + " (confidence: " + str(advice['confidence']) + "/100)")
            else:
                print("   " + symbol + ": Analysis failed")
                
        except Exception as e:
            print("   " + symbol + ": Error - " + str(e))
    
    print("\n✅ Multi-symbol analysis test completed!")

def main():
    """
    主测试函数
    """
    print("🧪 综合市场分析功能测试")
    print("="*80)
    
    # 测试1: 单个交易对综合分析
    success1 = test_comprehensive_analysis()
    
    # 测试2: 多个交易对分析
    if success1:
        test_multiple_symbols()
    
    print("\n🏁 所有测试完成!")
    
    # 提示如何使用
    print("\n💡 使用提示:")
    print("1. 运行一次性综合分析:")
    print("   python main.py --mode comprehensive --run-once --symbols BTC/USDT")
    print("\n2. 启动定时综合分析:")
    print("   python main.py --mode comprehensive --interval 10 --symbols BTC/USDT,ETH/USDT")
    print("\n3. 指定分析时间周期:")
    print("   python main.py --mode comprehensive --timeframe 30m --symbols BTC/USDT")

if __name__ == "__main__":
    main() 