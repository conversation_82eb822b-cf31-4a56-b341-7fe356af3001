#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from chan_analysis import ChanAnalysis

def generate_test_data():
    """生成测试数据"""
    np.random.seed(42)
    length = 100
    base_price = 100
    
    start_time = datetime.now() - timedelta(hours=length)
    timestamps = [start_time + timedelta(hours=i) for i in range(length)]
    
    prices = []
    current_price = base_price
    
    for i in range(length):
        if i < 30:
            change = np.random.normal(0.5, 1.0)
        elif i < 60:
            change = np.random.normal(0, 0.8)
        else:
            change = np.random.normal(-0.3, 1.2)
        
        current_price *= (1 + change / 100)
        prices.append(current_price)
    
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        open_price = prices[i-1] if i > 0 else close
        high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.5)) / 100)
        low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.5)) / 100)
        volume = np.random.randint(1000, 10000)
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def test_sell_strategies():
    """测试卖点策略"""
    print("="*60)
    print("测试优化后的卖点策略")
    print("="*60)
    
    df = generate_test_data()
    print("生成了", len(df), "条K线数据")
    print("价格范围:", round(df['low'].min(), 2), "-", round(df['high'].max(), 2))
    
    chan_analyzer = ChanAnalysis(df)
    chan_analyzer.pivot_zones = [
        (20, 40, 108, 102),
        (50, 70, 115, 110),
        (75, 90, 105, 100)
    ]
    
    print("\n测试第一类卖点")
    entry_point_1 = {'index': 35, 'price': 110.0, 'timestamp': '2024-01-01 11:00:00'}
    
    targets_1 = chan_analyzer.calculate_chan_profit_targets(
        entry_point=entry_point_1,
        signal_type='sell',
        buy_sell_point_type=1,
        related_pivot_idx=0,
        current_level='1h'
    )
    
    print("策略描述:", targets_1.get('strategy_description', 'N/A'))
    print("入场价格:", entry_point_1['price'])
    print("止损位:", targets_1.get('stop_loss'))
    
    profit_targets = targets_1.get('profit_targets', [])
    print("止盈目标数量:", len(profit_targets))
    for i, target in enumerate(profit_targets, 1):
        print("  目标", i, ":", target['price'], "- 减仓", target['percentage']*100, "%")
    
    print("\n测试第二类卖点")
    entry_point_2 = {'index': 55, 'price': 112.0, 'timestamp': '2024-01-01 15:00:00'}
    
    targets_2 = chan_analyzer.calculate_chan_profit_targets(
        entry_point=entry_point_2,
        signal_type='sell',
        buy_sell_point_type=2,
        related_pivot_idx=1,
        current_level='30m'
    )
    
    print("策略描述:", targets_2.get('strategy_description', 'N/A'))
    print("入场价格:", entry_point_2['price'])
    print("止损位:", targets_2.get('stop_loss'))
    
    profit_targets = targets_2.get('profit_targets', [])
    print("止盈目标数量:", len(profit_targets))
    for i, target in enumerate(profit_targets, 1):
        print("  目标", i, ":", target['price'], "- 减仓", target['percentage']*100, "%")
    
    print("\n测试第三类卖点")
    entry_point_3 = {'index': 80, 'price': 103.0, 'timestamp': '2024-01-01 20:00:00'}
    
    targets_3 = chan_analyzer.calculate_chan_profit_targets(
        entry_point=entry_point_3,
        signal_type='sell',
        buy_sell_point_type=3,
        related_pivot_idx=2,
        current_level='15m'
    )
    
    print("策略描述:", targets_3.get('strategy_description', 'N/A'))
    print("入场价格:", entry_point_3['price'])
    print("止损位:", targets_3.get('stop_loss'))
    
    profit_targets = targets_3.get('profit_targets', [])
    print("止盈目标数量:", len(profit_targets))
    for i, target in enumerate(profit_targets, 1):
        print("  目标", i, ":", target['price'], "- 减仓", target['percentage']*100, "%")

if __name__ == "__main__":
    try:
        test_sell_strategies()
        print("\n✅ 测试完成")
    except Exception as e:
        print("\n❌ 测试失败:", str(e))
        import traceback
        traceback.print_exc()
