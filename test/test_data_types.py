#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数据类型修复功能
"""

import pandas as pd
import numpy as np
from loguru import logger

def test_data_type_fix():
    """测试数据类型修复功能"""
    
    # 创建包含字符串类型数据的测试DataFrame
    test_data = {
        'timestamp': pd.date_range('2023-01-01', periods=10, freq='5min'),
        'open': ['100.1', '100.2', '100.3', '100.4', '100.5', '100.6', '100.7', '100.8', '100.9', '101.0'],
        'high': ['101.1', '101.2', '101.3', '101.4', '101.5', '101.6', '101.7', '101.8', '101.9', '102.0'],
        'low': ['99.1', '99.2', '99.3', '99.4', '99.5', '99.6', '99.7', '99.8', '99.9', '100.0'],
        'close': ['100.5', '100.6', '100.7', '100.8', '100.9', '101.0', '101.1', '101.2', '101.3', '101.4'],
        'volume': ['1000', '1100', '1200', '1300', '1400', '1500', '1600', '1700', '1800', '1900']
    }
    
    df = pd.DataFrame(test_data)
    
    print("原始数据类型:")
    for col in ['open', 'high', 'low', 'close', 'volume']:
        print(f"  {col}: {df[col].dtype}")
    
    # 强制转换为Unicode字符串类型（模拟问题）
    for col in ['open', 'high', 'low', 'close', 'volume']:
        df[col] = df[col].astype('<U10')  # Unicode字符串
    
    print("\n转换为Unicode字符串后的数据类型:")
    for col in ['open', 'high', 'low', 'close', 'volume']:
        print(f"  {col}: {df[col].dtype}")
    
    # 测试修复函数
    def fix_data_types(df):
        """修复数据类型"""
        df_fixed = df.copy()
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        
        for col in numeric_columns:
            if col in df_fixed.columns:
                current_dtype = str(df_fixed[col].dtype)
                
                if (df_fixed[col].dtype == 'object' or 
                    current_dtype.startswith('<U') or 
                    current_dtype.startswith('U') or
                    current_dtype.startswith('S')):
                    
                    print(f"修复 {col} 列，当前类型: {current_dtype}")
                    
                    try:
                        df_fixed[col] = pd.to_numeric(df_fixed[col], errors='coerce')
                    except:
                        try:
                            df_fixed[col] = pd.to_numeric(df_fixed[col].astype(str), errors='coerce')
                        except:
                            try:
                                df_fixed[col] = df_fixed[col].apply(lambda x: float(x) if x != '' and x is not None else np.nan)
                            except:
                                print(f"无法转换 {col} 列")
                                df_fixed[col] = 0.0
                
                if df_fixed[col].dtype != 'float64':
                    df_fixed[col] = df_fixed[col].astype('float64', errors='ignore')
        
        return df_fixed
    
    # 应用修复
    df_fixed = fix_data_types(df)
    
    print("\n修复后的数据类型:")
    for col in ['open', 'high', 'low', 'close', 'volume']:
        print(f"  {col}: {df_fixed[col].dtype}")
    
    print("\n修复后的数据样本:")
    print(df_fixed.head(3))
    
    # 测试数学运算
    try:
        result = df_fixed['close'] - df_fixed['open']
        print(f"\n数学运算测试成功: close - open = {result.iloc[0]}")
        return True
    except Exception as e:
        print(f"\n数学运算测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_data_type_fix()
    if success:
        print("\n✅ 数据类型修复测试通过")
    else:
        print("\n❌ 数据类型修复测试失败") 