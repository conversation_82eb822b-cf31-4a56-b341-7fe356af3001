#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试第二类买卖点识别功能
"""
from loguru import logger
from data_fetcher import DataFetcher
from chan_analysis import ChanAnalysis
from chart_plotter import ChartPlotter
import pandas as pd

# 设置日志
logger.add("logs/test_2nd_buysell_{time}.log", rotation="100 MB")

def test_second_buysell(symbol="BTC/USDT", timeframe="5m", limit=200):
    """
    测试第二类买卖点识别功能
    
    参数:
        symbol: 交易对，如 'BTC/USDT'
        timeframe: 时间周期，如 '5m', '15m', '1h'
        limit: 获取K线数量
    """
    try:
        # 获取数据
        fetcher = DataFetcher(symbols=symbol, timeframe=timeframe)
        df = fetcher.get_klines(limit=limit)
        if df is None or df.empty:
            logger.error(f"无法获取 {symbol} {timeframe} 的K线数据")
            return
        
        # 执行缠论分析
        analyzer = ChanAnalysis()
        # 先获取分析结果
        analyzer.analyze(df)
        # 然后从analyzer对象获取需要的属性
        fx_list = analyzer.fx_list
        bi_list = analyzer.bi_list
        xd_list = analyzer.xd_list
        
        # 创建绘图器
        plotter = ChartPlotter()
        
        # 识别中枢区域
        pivot_zones = plotter.identify_pivot_zones(bi_list, df)
        logger.info(f"识别到 {len(pivot_zones)} 个中枢")
        
        # 输出中枢信息
        for i, (start_idx, end_idx, zg, zd, start_date, end_date) in enumerate(pivot_zones):
            logger.info(f"中枢 #{i+1}: 上沿={zg:.2f}, 下沿={zd:.2f}, 起始={start_date}, 结束={end_date}")
        
        # 识别第二类买卖点
        buy_points, sell_points = plotter.identify_second_buy_sell_points(bi_list, df, pivot_zones)
        logger.info(f"识别到 {len(buy_points)} 个第二类买点, {len(sell_points)} 个第二类卖点")
        
        # 输出买卖点信息
        for i, (idx, price, strength, pivot_idx) in enumerate(buy_points):
            date = df['timestamp'].iloc[idx] if idx < len(df) else 'Unknown'
            logger.info(f"第二类买点 #{i+1}: 索引={idx}, 价格={price:.2f}, 强度={strength}/10, 关联中枢={pivot_idx+1}, 时间={date}")
        
        for i, (idx, price, strength, pivot_idx) in enumerate(sell_points):
            date = df['timestamp'].iloc[idx] if idx < len(df) else 'Unknown'
            logger.info(f"第二类卖点 #{i+1}: 索引={idx}, 价格={price:.2f}, 强度={strength}/10, 关联中枢={pivot_idx+1}, 时间={date}")
        
        # 检查在中枢后的笔序列
        logger.info(f"总共有 {len(bi_list)} 个笔")
        if pivot_zones:
            last_pivot_end_idx = pivot_zones[-1][1]
            post_pivot_bi = [bi for bi in bi_list if bi[0] >= last_pivot_end_idx]
            logger.info(f"最后一个中枢后有 {len(post_pivot_bi)} 个笔")
            
            # 详细记录这些笔的情况
            for i, (start_idx, end_idx, start_val, end_val, direction) in enumerate(post_pivot_bi):
                logger.info(f"笔 #{i+1}: 方向={direction}, 起始索引={start_idx}, 结束索引={end_idx}, 起始值={start_val:.2f}, 结束值={end_val:.2f}")
        
        # 绘制图表，显式地开启第二类买卖点显示
        chart_path = plotter.plot_chan_analysis(
            df=df,
            symbol=symbol,
            timeframe=timeframe,
            fx_list=fx_list,
            bi_list=bi_list,
            xd_list=xd_list,
            show_ma=True,
            show_macd=True,
            show_pivots=True,
            show_line=False,
            show_buy_sell_points=True,
            show_second_buy_sell_points=True,
            save_path=f"charts/{symbol.replace('/', '_')}_{timeframe}_2nd_test.png"
        )
        
        logger.info(f"测试图表已保存: {chart_path}")
        print(f"测试图表已保存: {chart_path}")
        
        # 返回结果摘要
        return {
            "pivot_count": len(pivot_zones),
            "second_buy_count": len(buy_points),
            "second_sell_count": len(sell_points),
            "chart_path": chart_path
        }
        
    except Exception as e:
        logger.exception(f"测试过程中发生错误: {str(e)}")
        return None

if __name__ == "__main__":
    # 测试不同周期
    for timeframe in ["5m", "15m", "1h", "4h"]:
        print(f"测试 BTC/USDT {timeframe} 周期")
        result = test_second_buysell(timeframe=timeframe, limit=300)
        if result:
            print(f"结果: 中枢数量={result['pivot_count']}, 第二类买点={result['second_buy_count']}, 第二类卖点={result['second_sell_count']}")
            print("-" * 50) 