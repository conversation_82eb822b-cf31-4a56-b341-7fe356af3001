#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from chan_analysis import ChanAnalysis

def test_multi_level_profit_targets():
    """测试多级别止盈目标的合理性"""
    print("="*80)
    print("多级别止盈目标合理性测试")
    print("="*80)
    
    # 生成ETH/USDT模拟数据
    np.random.seed(42)
    length = 100
    base_price = 2650.0
    
    start_time = datetime.now() - timedelta(hours=length)
    timestamps = [start_time + timedelta(hours=i) for i in range(length)]
    
    prices = []
    current_price = base_price
    
    for i in range(length):
        change = np.random.normal(0, 0.8)
        current_price *= (1 + change / 100)
        current_price = max(2400, min(2800, current_price))
        prices.append(current_price)
    
    # 确保最后价格接近基准价格
    prices[-1] = base_price
    
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        open_price = prices[i-1] if i > 0 else close
        high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.3)) / 100)
        low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.3)) / 100)
        volume = np.random.randint(5000, 15000)
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    
    print(f"生成了 {len(df)} 条K线数据")
    print(f"价格范围: {df['low'].min():.2f} - {df['high'].max():.2f}")
    print(f"当前价格: {df['close'].iloc[-1]:.2f}")
    
    # 创建缠论分析器
    chan_analyzer = ChanAnalysis(df)
    chan_analyzer.pivot_zones = [
        (30, 60, 2700, 2600),  # 主要中枢
        (70, 90, 2750, 2650),  # 更高级别中枢
    ]
    
    # 测试不同时间级别的止盈目标
    timeframes = ['5m', '15m', '30m', '1h']
    entry_point = {
        'index': len(df) - 1,
        'price': 2650.0,
        'timestamp': df['timestamp'].iloc[-1].strftime('%Y-%m-%d %H:%M:%S')
    }
    
    print("\n" + "="*80)
    print("多级别买入信号止盈目标对比")
    print("="*80)
    
    buy_results = {}
    for tf in timeframes:
        targets = chan_analyzer.calculate_chan_profit_targets(
            entry_point=entry_point,
            signal_type='buy',
            buy_sell_point_type=1,  # 第一类买点
            related_pivot_idx=0,
            current_level=tf
        )
        buy_results[tf] = targets
    
    # 显示买入信号对比表格
    print(f"{'时间级别':<8} {'止损位':<12} {'止盈1':<12} {'止盈2':<12} {'止盈3':<12} {'止盈1%':<8} {'止盈2%':<8} {'止盈3%':<8}")
    print("-" * 90)
    
    for tf in timeframes:
        targets = buy_results[tf]
        entry_price = entry_point['price']
        stop_loss = targets.get('stop_loss', 0)
        profit_targets = targets.get('profit_targets', [])
        
        tp1 = profit_targets[0]['price'] if len(profit_targets) > 0 else 0
        tp2 = profit_targets[1]['price'] if len(profit_targets) > 1 else 0
        tp3 = profit_targets[2]['price'] if len(profit_targets) > 2 else 0
        
        tp1_pct = (tp1 / entry_price - 1) * 100 if tp1 > 0 else 0
        tp2_pct = (tp2 / entry_price - 1) * 100 if tp2 > 0 else 0
        tp3_pct = (tp3 / entry_price - 1) * 100 if tp3 > 0 else 0
        
        print(f"{tf:<8} {stop_loss:<12.2f} {tp1:<12.2f} {tp2:<12.2f} {tp3:<12.2f} {tp1_pct:<8.1f} {tp2_pct:<8.1f} {tp3_pct:<8.1f}")
    
    print("\n" + "="*80)
    print("多级别卖出信号止盈目标对比")
    print("="*80)
    
    sell_results = {}
    for tf in timeframes:
        targets = chan_analyzer.calculate_chan_profit_targets(
            entry_point=entry_point,
            signal_type='sell',
            buy_sell_point_type=1,  # 第一类卖点
            related_pivot_idx=0,
            current_level=tf
        )
        sell_results[tf] = targets
    
    # 显示卖出信号对比表格
    print(f"{'时间级别':<8} {'止损位':<12} {'止盈1':<12} {'止盈2':<12} {'止盈3':<12} {'止盈1%':<8} {'止盈2%':<8} {'止盈3%':<8}")
    print("-" * 90)
    
    for tf in timeframes:
        targets = sell_results[tf]
        entry_price = entry_point['price']
        stop_loss = targets.get('stop_loss', 0)
        profit_targets = targets.get('profit_targets', [])
        
        tp1 = profit_targets[0]['price'] if len(profit_targets) > 0 else 0
        tp2 = profit_targets[1]['price'] if len(profit_targets) > 1 else 0
        tp3 = profit_targets[2]['price'] if len(profit_targets) > 2 else 0
        
        tp1_pct = (tp1 / entry_price - 1) * 100 if tp1 > 0 else 0
        tp2_pct = (tp2 / entry_price - 1) * 100 if tp2 > 0 else 0
        tp3_pct = (tp3 / entry_price - 1) * 100 if tp3 > 0 else 0
        
        print(f"{tf:<8} {stop_loss:<12.2f} {tp1:<12.2f} {tp2:<12.2f} {tp3:<12.2f} {tp1_pct:<8.1f} {tp2_pct:<8.1f} {tp3_pct:<8.1f}")
    
    print("\n" + "="*80)
    print("级别合理性验证")
    print("="*80)
    
    # 验证买入信号的合理性
    print("✅ 买入信号验证:")
    buy_tp1_values = [buy_results[tf]['profit_targets'][0]['price'] for tf in timeframes if buy_results[tf]['profit_targets']]
    buy_is_ascending = all(buy_tp1_values[i] <= buy_tp1_values[i+1] for i in range(len(buy_tp1_values)-1))
    
    if buy_is_ascending:
        print("  - 买入止盈目标随时间级别递增 ✅")
        print(f"  - 5m止盈1: {buy_tp1_values[0]:.2f}")
        print(f"  - 15m止盈1: {buy_tp1_values[1]:.2f} (+{(buy_tp1_values[1]/buy_tp1_values[0]-1)*100:.1f}%)")
        print(f"  - 30m止盈1: {buy_tp1_values[2]:.2f} (+{(buy_tp1_values[2]/buy_tp1_values[0]-1)*100:.1f}%)")
        print(f"  - 1h止盈1: {buy_tp1_values[3]:.2f} (+{(buy_tp1_values[3]/buy_tp1_values[0]-1)*100:.1f}%)")
    else:
        print("  - 买入止盈目标级别关系不合理 ❌")
    
    # 验证卖出信号的合理性
    print("\n✅ 卖出信号验证:")
    sell_tp1_values = [sell_results[tf]['profit_targets'][0]['price'] for tf in timeframes if sell_results[tf]['profit_targets']]
    sell_is_descending = all(sell_tp1_values[i] >= sell_tp1_values[i+1] for i in range(len(sell_tp1_values)-1))
    
    if sell_is_descending:
        print("  - 卖出止盈目标随时间级别递减 ✅")
        print(f"  - 5m止盈1: {sell_tp1_values[0]:.2f}")
        print(f"  - 15m止盈1: {sell_tp1_values[1]:.2f} ({(sell_tp1_values[1]/sell_tp1_values[0]-1)*100:.1f}%)")
        print(f"  - 30m止盈1: {sell_tp1_values[2]:.2f} ({(sell_tp1_values[2]/sell_tp1_values[0]-1)*100:.1f}%)")
        print(f"  - 1h止盈1: {sell_tp1_values[3]:.2f} ({(sell_tp1_values[3]/sell_tp1_values[0]-1)*100:.1f}%)")
    else:
        print("  - 卖出止盈目标级别关系不合理 ❌")
    
    print("\n" + "="*80)
    print("模拟原问题场景")
    print("="*80)
    
    # 模拟原问题：5m vs 15m的对比
    print("原问题场景：")
    print("🚨 ETH/USDT 5m 买卖点信号提醒 🚨")
    print("推荐操作: BUY")
    print("信号强度: 8/10")
    
    targets_5m = buy_results['5m']
    profit_targets_5m = targets_5m.get('profit_targets', [])
    stop_loss_5m = targets_5m.get('stop_loss', 0)
    
    print(f"信号说明: 买卖信号同时存在，买点更新鲜，中枢上沿买点，强度: 8/10，止损: {stop_loss_5m:.4f}，止盈1: {profit_targets_5m[0]['price']:.3f}，止盈2: {profit_targets_5m[1]['price']:.3f}，止盈3: {profit_targets_5m[2]['price']:.3f}")
    
    print("\n🚨 ETH/USDT 15m 买卖点信号提醒 🚨")
    print("推荐操作: BUY")
    print("信号强度: 10/10")
    
    targets_15m = buy_results['15m']
    profit_targets_15m = targets_15m.get('profit_targets', [])
    stop_loss_15m = targets_15m.get('stop_loss', 0)
    
    print(f"信号说明: 买卖信号同时存在，买点综合评分显著更高，第一类买点，强度: 10/10，止损: {stop_loss_15m:.4f}，止盈1: {profit_targets_15m[0]['price']:.2f}，止盈2: {profit_targets_15m[1]['price']:.2f}，止盈3: {profit_targets_15m[2]['price']:.2f}")
    
    # 验证修复效果
    is_fixed = profit_targets_15m[0]['price'] > profit_targets_5m[0]['price']
    
    print(f"\n修复验证:")
    print(f"15m级别止盈1 ({profit_targets_15m[0]['price']:.2f}) > 5m级别止盈1 ({profit_targets_5m[0]['price']:.2f}): {'✅ 已修复' if is_fixed else '❌ 仍有问题'}")
    
    return buy_results, sell_results

if __name__ == "__main__":
    try:
        buy_results, sell_results = test_multi_level_profit_targets()
        print("\n✅ 多级别止盈目标测试完成")
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
