#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试买卖点止盈策略
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from chan_analysis import ChanAnalysis
from chart_plotter import ChartPlotter

def create_mock_data():
    """
    创建模拟数据用于测试
    """
    # 生成200个数据点
    dates = [datetime.now() - timedelta(minutes=5*i) for i in range(200, 0, -1)]
    
    # 生成模拟价格数据（包含明显的趋势和中枢）
    base_price = 100000
    prices = []
    
    # 第一段：上涨趋势
    for i in range(50):
        price = base_price + i * 20 + np.random.normal(0, 50)
        prices.append(price)
    
    # 第二段：中枢震荡
    pivot_center = prices[-1]
    for i in range(60):
        price = pivot_center + np.sin(i * 0.3) * 200 + np.random.normal(0, 30)
        prices.append(price)
    
    # 第三段：下跌趋势
    start_price = prices[-1]
    for i in range(50):
        price = start_price - i * 15 + np.random.normal(0, 40)
        prices.append(price)
    
    # 第四段：反弹
    start_price = prices[-1]
    for i in range(40):
        price = start_price + i * 25 + np.random.normal(0, 35)
        prices.append(price)
    
    # 创建OHLC数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        high = close + np.random.uniform(10, 100)
        low = close - np.random.uniform(10, 100)
        open_price = close + np.random.uniform(-50, 50)
        
        data.append({
            'timestamp': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': np.random.uniform(1000, 5000)
        })
    
    return pd.DataFrame(data)

def test_profit_targets():
    """
    测试止盈策略
    """
    print("=== 缠论买卖点止盈策略测试 ===\n")
    
    # 创建模拟数据
    df = create_mock_data()
    print(f"生成了 {len(df)} 条模拟K线数据")
    
    # 执行缠论分析
    analyzer = ChanAnalysis()
    analyzer.analyze(df)
    
    fx_list = analyzer.fx_list
    bi_list = analyzer.bi_list
    xd_list = analyzer.xd_list
    
    print(f"分析结果：")
    print(f"- 分型数量: {len(fx_list)}")
    print(f"- 笔数量: {len(bi_list)}")
    print(f"- 线段数量: {len(xd_list)}")
    
    # 创建绘图器
    plotter = ChartPlotter()
    
    # 识别中枢区域
    pivot_zones = plotter.identify_pivot_zones(bi_list, df)
    print(f"- 中枢数量: {len(pivot_zones)}")
    
    # 识别各类买卖点
    buy_points1, sell_points1 = plotter.identify_first_buy_sell_points(bi_list, df, pivot_zones)
    buy_points2, sell_points2 = plotter.identify_second_buy_sell_points(bi_list, df, pivot_zones)
    buy_points3, sell_points3 = plotter.identify_third_buy_sell_points(bi_list, df, pivot_zones)
    
    print(f"\n买卖点识别结果：")
    print(f"- 第一类买点: {len(buy_points1)}, 卖点: {len(sell_points1)}")
    print(f"- 第二类买点: {len(buy_points2)}, 卖点: {len(sell_points2)}")
    print(f"- 第三类买点: {len(buy_points3)}, 卖点: {len(sell_points3)}")
    
    # 计算各类买卖点的止盈目标
    print("\n" + "="*60)
    print("第一类买卖点止盈策略")
    print("="*60)
    
    if buy_points1 or sell_points1:
        targets1 = plotter.calculate_profit_targets(bi_list, df, pivot_zones, buy_points1, sell_points1, 1)
        
        for i, target_info in enumerate(targets1['buy_targets'][:3]):  # 只显示前3个
            point = target_info['point']
            targets = target_info['targets']
            idx, price, strength = point[:3]
            timestamp = df['timestamp'].iloc[idx]
            
            print(f"\n第一类买点 {i+1}:")
            print(f"  时间: {timestamp}")
            print(f"  价格: {price:.2f}")
            print(f"  强度: {strength}")
            print(f"  策略: {targets['description']}")
            print(f"  保守止盈: {targets['conservative']:.2f} (涨幅: {(targets['conservative']/price-1)*100:.1f}%)")
            print(f"  积极止盈: {targets['aggressive']:.2f} (涨幅: {(targets['aggressive']/price-1)*100:.1f}%)")
            print(f"  止损位: {targets['stop_loss']:.2f} (跌幅: {(1-targets['stop_loss']/price)*100:.1f}%)")
        
        for i, target_info in enumerate(targets1['sell_targets'][:3]):  # 只显示前3个
            point = target_info['point']
            targets = target_info['targets']
            idx, price, strength = point[:3]
            timestamp = df['timestamp'].iloc[idx]
            
            print(f"\n第一类卖点 {i+1}:")
            print(f"  时间: {timestamp}")
            print(f"  价格: {price:.2f}")
            print(f"  强度: {strength}")
            print(f"  策略: {targets['description']}")
            print(f"  保守止盈: {targets['conservative']:.2f} (跌幅: {(1-targets['conservative']/price)*100:.1f}%)")
            print(f"  积极止盈: {targets['aggressive']:.2f} (跌幅: {(1-targets['aggressive']/price)*100:.1f}%)")
            print(f"  止损位: {targets['stop_loss']:.2f} (涨幅: {(targets['stop_loss']/price-1)*100:.1f}%)")
    
    print("\n" + "="*60)
    print("第二类买卖点止盈策略")
    print("="*60)
    
    if buy_points2 or sell_points2:
        targets2 = plotter.calculate_profit_targets(bi_list, df, pivot_zones, buy_points2, sell_points2, 2)
        
        for i, target_info in enumerate(targets2['buy_targets'][:3]):
            point = target_info['point']
            targets = target_info['targets']
            idx, price, strength, pivot_idx = point[:4]
            timestamp = df['timestamp'].iloc[idx]
            
            print(f"\n第二类买点 {i+1}:")
            print(f"  时间: {timestamp}")
            print(f"  价格: {price:.2f}")
            print(f"  强度: {strength}")
            print(f"  关联中枢: {pivot_idx+1}")
            print(f"  策略: {targets['description']}")
            print(f"  保守止盈: {targets['conservative']:.2f} (涨幅: {(targets['conservative']/price-1)*100:.1f}%)")
            print(f"  积极止盈: {targets['aggressive']:.2f} (涨幅: {(targets['aggressive']/price-1)*100:.1f}%)")
            print(f"  止损位: {targets['stop_loss']:.2f} (跌幅: {(1-targets['stop_loss']/price)*100:.1f}%)")
        
        for i, target_info in enumerate(targets2['sell_targets'][:3]):
            point = target_info['point']
            targets = target_info['targets']
            idx, price, strength, pivot_idx = point[:4]
            timestamp = df['timestamp'].iloc[idx]
            
            print(f"\n第二类卖点 {i+1}:")
            print(f"  时间: {timestamp}")
            print(f"  价格: {price:.2f}")
            print(f"  强度: {strength}")
            print(f"  关联中枢: {pivot_idx+1}")
            print(f"  策略: {targets['description']}")
            print(f"  保守止盈: {targets['conservative']:.2f} (跌幅: {(1-targets['conservative']/price)*100:.1f}%)")
            print(f"  积极止盈: {targets['aggressive']:.2f} (跌幅: {(1-targets['aggressive']/price)*100:.1f}%)")
            print(f"  止损位: {targets['stop_loss']:.2f} (涨幅: {(targets['stop_loss']/price-1)*100:.1f}%)")
    
    print("\n" + "="*60)
    print("第三类买卖点止盈策略")
    print("="*60)
    
    if buy_points3 or sell_points3:
        targets3 = plotter.calculate_profit_targets(bi_list, df, pivot_zones, buy_points3, sell_points3, 3)
        
        for i, target_info in enumerate(targets3['buy_targets'][:5]):  # 第三类买点较多，显示前5个
            point = target_info['point']
            targets = target_info['targets']
            idx, price, strength, pivot_idx, reason = point
            timestamp = df['timestamp'].iloc[idx]
            
            print(f"\n第三类买点 {i+1}:")
            print(f"  时间: {timestamp}")
            print(f"  价格: {price:.2f}")
            print(f"  强度: {strength}")
            print(f"  关联中枢: {pivot_idx+1}")
            print(f"  原因: {reason}")
            print(f"  策略: {targets['description']}")
            print(f"  保守止盈: {targets['conservative']:.2f} (涨幅: {(targets['conservative']/price-1)*100:.1f}%)")
            print(f"  积极止盈: {targets['aggressive']:.2f} (涨幅: {(targets['aggressive']/price-1)*100:.1f}%)")
            print(f"  止损位: {targets['stop_loss']:.2f} (跌幅: {(1-targets['stop_loss']/price)*100:.1f}%)")
        
        for i, target_info in enumerate(targets3['sell_targets'][:5]):
            point = target_info['point']
            targets = target_info['targets']
            idx, price, strength, pivot_idx, reason = point
            timestamp = df['timestamp'].iloc[idx]
            
            print(f"\n第三类卖点 {i+1}:")
            print(f"  时间: {timestamp}")
            print(f"  价格: {price:.2f}")
            print(f"  强度: {strength}")
            print(f"  关联中枢: {pivot_idx+1}")
            print(f"  原因: {reason}")
            print(f"  策略: {targets['description']}")
            print(f"  保守止盈: {targets['conservative']:.2f} (跌幅: {(1-targets['conservative']/price)*100:.1f}%)")
            print(f"  积极止盈: {targets['aggressive']:.2f} (跌幅: {(1-targets['aggressive']/price)*100:.1f}%)")
            print(f"  止损位: {targets['stop_loss']:.2f} (涨幅: {(targets['stop_loss']/price-1)*100:.1f}%)")
    
    print("\n" + "="*60)
    print("止盈策略总结")
    print("="*60)
    print("""
1. 第一类买卖点（趋势转折点）：
   - 止盈目标较大，适合中长线持有
   - 止损相对宽松，给趋势发展空间
   - 风险收益比最佳

2. 第二类买卖点（中枢突破点）：
   - 止盈目标中等，以突破前高/前低为主要目标
   - 止损设在中枢边界，风险可控
   - 成功率较高，适合稳健操作

3. 第三类买卖点（中枢震荡点）：
   - 止盈目标较小，快进快出
   - 止损很紧，一旦跌破/突破中枢边界立即止损
   - 操作频率高，适合短线交易

建议：
- 根据自己的风险偏好选择合适的买卖点类型
- 严格执行止损，保护本金
- 可以分批止盈，先保守后积极
- 结合多个时间周期确认信号
    """)

if __name__ == "__main__":
    test_profit_targets() 