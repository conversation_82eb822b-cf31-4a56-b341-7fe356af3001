#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
缠论分析图表生成工具
支持单一时间周期和多时间周期分析图表的生成
"""

import os
import sys
import argparse
from loguru import logger

from data_fetcher import DataFetcher
from chan_analysis import ChanAnalysis
from chart_plotter import ChartPlotter
from multi_level_analysis import MultiLevelAnalysis


def generate_single_chart(symbol, timeframe="5m", exchange=None, limit=200, save_path=None):
    """
    生成单一时间周期的缠论分析图表
    
    参数:
        symbol: 交易对，如 BTC/USDT
        timeframe: 时间周期，如 5m, 15m, 1h
        exchange: 交易所ID，如 gate
        limit: 获取的K线数量
        save_path: 保存路径，如果为None则自动生成
    
    返回:
        str: 图表保存路径
    """
    try:
        # 获取K线数据
        fetcher = DataFetcher(symbols=symbol, timeframe=timeframe, exchange_id=exchange)
        df = fetcher.get_klines(limit=limit)
        
        if df.empty:
            logger.error(f"无法获取 {symbol} {timeframe} 的K线数据")
            return None
        
        # 为DataFrame添加交易对和时间周期信息，方便绘图
        df.attrs['symbol'] = symbol
        df.attrs['timeframe'] = timeframe
        
        # 执行缠论分析
        analyzer = ChanAnalysis()
        analyzer.analyze(df)
        
        # 创建绘图器
        plotter = ChartPlotter()
        
        # 绘制分析图表
        chart_path = plotter.plot_chan_analysis(
            df=df,
            symbol=symbol,
            timeframe=timeframe,
            fx_list=analyzer.fx_list,
            bi_list=analyzer.bi_list,
            xd_list=analyzer.xd_list,
            save_path=save_path
        )
        
        logger.info(f"已生成 {symbol} {timeframe} 缠论分析图表: {chart_path}")
        return chart_path
        
    except Exception as e:
        logger.error(f"生成 {symbol} {timeframe} 缠论分析图表时出错: {str(e)}")
        return None


def generate_multi_charts(symbol, timeframes=None, exchange=None, limit=200, save_path=None):
    """
    生成多时间周期的缠论分析图表
    
    参数:
        symbol: 交易对，如 BTC/USDT
        timeframes: 时间周期列表，如 ['5m', '15m', '1h']
        exchange: 交易所ID，如 gate
        limit: 获取的K线数量
        save_path: 保存路径，如果为None则自动生成
    
    返回:
        dict: 包含各个时间周期图表路径的字典
    """
    if timeframes is None:
        timeframes = ['1m', '5m', '15m', '30m', '1h']
    
    try:
        # 初始化多级别分析器
        analyzer = MultiLevelAnalysis(symbol=symbol, exchange_id=exchange)
        
        # 生成多时间周期联合分析图表
        multi_chart_path = analyzer.plot_multi_timeframe_analysis(save_path=save_path)
        
        # 生成各个时间周期的单独图表
        chart_paths = {'multi': multi_chart_path}
        
        for tf in timeframes:
            single_chart_path = generate_single_chart(
                symbol=symbol,
                timeframe=tf,
                exchange=exchange,
                limit=limit
            )
            if single_chart_path:
                chart_paths[tf] = single_chart_path
        
        return chart_paths
        
    except Exception as e:
        logger.error(f"生成 {symbol} 多时间周期缠论分析图表时出错: {str(e)}")
        return {}


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="缠论分析图表生成工具")
    parser.add_argument("--symbols", type=str, default="BTC/USDT", 
                        help="要分析的交易对，多个交易对用逗号分隔，例如：BTC/USDT,ETH/USDT")
    parser.add_argument("--timeframes", type=str, default="5m,15m,1h", 
                        help="要分析的时间周期，多个时间周期用逗号分隔，例如：5m,15m,1h")
    parser.add_argument("--exchange", type=str, default="gate", 
                        help="交易所ID，默认为gate")
    parser.add_argument("--limit", type=int, default=200, 
                        help="获取的K线数量，默认为200")
    parser.add_argument("--output-dir", type=str, default="charts", 
                        help="图表保存目录，默认为charts")
    parser.add_argument("--mode", type=str, default="multi", choices=["single", "multi", "both"], 
                        help="生成模式：single-仅生成单一时间周期图表，multi-仅生成多时间周期图表，both-同时生成")
    parser.add_argument("--log-level", type=str, default="INFO", 
                        choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
                        help="日志级别，默认为INFO")
    
    args = parser.parse_args()
    
    # 设置日志
    logger.remove()
    logger.add(sys.stderr, level=args.log_level)
    logger.add(f"logs/generate_charts_{args.log_level.lower()}.log", rotation="100 MB", level=args.log_level)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 解析交易对和时间周期
    symbols = [s.strip() for s in args.symbols.split(",")]
    timeframes = [tf.strip() for tf in args.timeframes.split(",")]
    
    # 生成图表
    for symbol in symbols:
        logger.info(f"开始为 {symbol} 生成缠论分析图表")
        
        if args.mode in ["single", "both"]:
            # 生成单一时间周期图表
            for tf in timeframes:
                save_path = os.path.join(args.output_dir, f"{symbol.replace('/', '_')}_{tf}.png")
                chart_path = generate_single_chart(
                    symbol=symbol,
                    timeframe=tf,
                    exchange=args.exchange,
                    limit=args.limit,
                    save_path=save_path
                )
                if chart_path:
                    logger.info(f"已生成 {symbol} {tf} 缠论分析图表: {chart_path}")
        
        if args.mode in ["multi", "both"]:
            # 生成多时间周期图表
            save_path = os.path.join(args.output_dir, f"{symbol.replace('/', '_')}_multi.png")
            chart_paths = generate_multi_charts(
                symbol=symbol,
                timeframes=timeframes,
                exchange=args.exchange,
                limit=args.limit,
                save_path=save_path
            )
            
            if chart_paths:
                logger.info(f"已为 {symbol} 生成以下图表:")
                for tf, path in chart_paths.items():
                    if path:
                        logger.info(f"  {tf}: {path}")
    
    logger.info("所有图表生成完成")


if __name__ == "__main__":
    main() 