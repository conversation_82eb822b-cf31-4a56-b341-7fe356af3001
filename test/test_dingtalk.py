#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试钉钉通知功能
"""

from notifier import Notifier
from loguru import logger

def main():
    """测试钉钉通知"""
    logger.info("开始测试钉钉通知")
    
    # 创建通知器，默认只启用钉钉通知
    notifier = Notifier(notification_methods=['dingtalk'])
    
    # 输出当前钉钉配置
    token_status = "已设置" if notifier.dingtalk_token else "未设置"
    secret_status = "已设置" if notifier.dingtalk_secret else "未设置"
    
    if notifier.dingtalk_token:
        token_display = notifier.dingtalk_token[:10] + "..."
    else:
        token_display = "无"
    
    if notifier.dingtalk_secret:
        secret_display = notifier.dingtalk_secret[:10] + "..."
    else:
        secret_display = "无"
    
    logger.info("钉钉Token: {} ({})".format(token_display, token_status))
    logger.info("钉钉Secret: {} ({})".format(secret_display, secret_status))
    
    # 发送测试消息
    test_message = "🧪 这是一条钉钉测试消息 🧪\n"
    test_message += "时间: 2025-06-04 18:00:00\n"
    test_message += "测试内容: ETH/USDT 买卖点信号测试\n"
    test_message += "系统运行正常!"
    
    logger.info("发送测试消息...")
    notifier.send(test_message)
    
    logger.info("测试完成")

if __name__ == "__main__":
    main() 