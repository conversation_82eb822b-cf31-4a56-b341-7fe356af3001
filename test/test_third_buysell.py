#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试第三类买卖点识别功能
"""
from loguru import logger
from data_fetcher import DataFetcher
from chan_analysis import ChanAnalysis
from chart_plotter import ChartPlotter
import pandas as pd

def test_third_buysell():
    """
    测试第三类买卖点识别功能
    """
    try:
        # 获取数据
        fetcher = DataFetcher(symbols="BTC/USDT", timeframe="5m")
        df = fetcher.get_klines(limit=300)  # 获取更多数据以便找到第三类买卖点
        
        # 执行缠论分析
        analyzer = ChanAnalysis()
        analyzer.analyze(df)
        fx_list = analyzer.fx_list
        bi_list = analyzer.bi_list
        xd_list = analyzer.xd_list
        
        print(f"分析结果：")
        print(f"- 分型数量: {len(fx_list)}")
        print(f"- 笔数量: {len(bi_list)}")
        print(f"- 线段数量: {len(xd_list)}")
        
        # 创建绘图器
        plotter = ChartPlotter()
        
        # 识别中枢区域
        pivot_zones = plotter.identify_pivot_zones(bi_list, df)
        print(f"- 中枢数量: {len(pivot_zones)}")
        
        # 识别第一类买卖点
        buy_points1, sell_points1 = plotter.identify_first_buy_sell_points(bi_list, df, pivot_zones)
        print(f"- 第一类买点: {len(buy_points1)}")
        print(f"- 第一类卖点: {len(sell_points1)}")
        
        # 识别第二类买卖点
        buy_points2, sell_points2 = plotter.identify_second_buy_sell_points(bi_list, df, pivot_zones)
        print(f"- 第二类买点: {len(buy_points2)}")
        print(f"- 第二类卖点: {len(sell_points2)}")
        
        # 识别第三类买卖点
        buy_points3, sell_points3 = plotter.identify_third_buy_sell_points(bi_list, df, pivot_zones)
        print(f"- 第三类买点: {len(buy_points3)}")
        print(f"- 第三类卖点: {len(sell_points3)}")
        
        # 详细显示第三类买卖点信息
        if buy_points3:
            print("\n第三类买点详情:")
            for i, (idx, price, strength, pivot_idx, reason) in enumerate(buy_points3):
                timestamp = df['timestamp'].iloc[idx]
                print(f"  买点{i+1}: 时间={timestamp}, 价格={price:.2f}, 强度={strength}, 中枢={pivot_idx+1}, 原因={reason}")
        
        if sell_points3:
            print("\n第三类卖点详情:")
            for i, (idx, price, strength, pivot_idx, reason) in enumerate(sell_points3):
                timestamp = df['timestamp'].iloc[idx]
                print(f"  卖点{i+1}: 时间={timestamp}, 价格={price:.2f}, 强度={strength}, 中枢={pivot_idx+1}, 原因={reason}")
        
        # 绘制包含第三类买卖点的图表
        chart_path = plotter.plot_chan_analysis(
            df=df,
            symbol="BTC/USDT",
            timeframe="5m",
            fx_list=fx_list,
            bi_list=bi_list,
            xd_list=xd_list,
            show_ma=False,  # 不显示均线
            show_macd=False,  # 不显示MACD
            show_pivots=True,  # 显示中枢（用于参考）
            show_line=True,  # 使用线图更简洁
            show_buy_sell_points=False,  # 不显示第一类买卖点
            show_second_buy_sell_points=False,  # 不显示第二类买卖点
            show_third_buy_sell_points=True,  # 只显示第三类买卖点
            title="BTC/USDT 5m - 仅显示第三类买卖点"
        )
        
        print(f"\n图表已保存: {chart_path}")
        
        # 分析中枢后的笔序列（调试用）
        if pivot_zones:
            print(f"\n中枢分析:")
            for i, (start_idx, end_idx, zg, zd, start_date, end_date) in enumerate(pivot_zones):
                print(f"中枢{i+1}: 上沿={zg:.2f}, 下沿={zd:.2f}, 高度={zg-zd:.2f}")
                
                # 查找中枢后的笔
                post_pivot_bi = [bi for bi in bi_list if bi[0] >= end_idx]
                print(f"  中枢后笔数量: {len(post_pivot_bi)}")
                
                if len(post_pivot_bi) >= 3:
                    print(f"  前3笔:")
                    for j, (bi_start, bi_end, bi_start_val, bi_end_val, direction) in enumerate(post_pivot_bi[:3]):
                        if direction == 'up':
                            high, low = bi_end_val, bi_start_val
                        else:
                            high, low = bi_start_val, bi_end_val
                        print(f"    笔{j+1}: {direction}, 高={high:.2f}, 低={low:.2f}")
                        
                        # 检查与中枢的关系
                        if direction == 'up':
                            if low > zg:
                                print(f"      -> 在中枢上方运行")
                            elif high < zd:
                                print(f"      -> 在中枢下方运行")
                            else:
                                print(f"      -> 与中枢重叠")
                        else:
                            if high < zd:
                                print(f"      -> 在中枢下方运行")
                            elif low > zg:
                                print(f"      -> 在中枢上方运行")
                            else:
                                print(f"      -> 与中枢重叠")
        
    except Exception as e:
        logger.exception(f"测试过程中发生错误: {str(e)}")
        print(f"错误: {str(e)}")

if __name__ == "__main__":
    test_third_buysell() 