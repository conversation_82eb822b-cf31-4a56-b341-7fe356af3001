#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
交易系统完整演示
展示如何将市场分析与实际交易执行结合
"""

from datetime import datetime, timedelta
import pandas as pd
import time
from trading_system import TradingSystem

def create_market_data_sequence():
    """
    创建一系列模拟市场数据，模拟价格变化过程
    """
    base_time = datetime.now()
    
    # 模拟一个完整的交易场景：下跌 -> 筑底 -> 反弹
    scenarios = [
        # 场景1: 下跌趋势中
        {
            'name': '下跌趋势中',
            'prices': [2650, 2645, 2640, 2635, 2630, 2625, 2620, 2615, 2610, 2605],
            'segment_analysis': {
                'direction': 'down',
                'confidence': 75,
                'end_confirmed': False
            }
        },
        # 场景2: 下跌线段终结
        {
            'name': '下跌线段终结',
            'prices': [2605, 2600, 2598, 2595, 2593, 2590, 2588, 2585, 2583, 2580],
            'segment_analysis': {
                'direction': 'down',
                'confidence': 95,
                'end_confirmed': True
            }
        },
        # 场景3: 反弹开始
        {
            'name': '反弹开始',
            'prices': [2580, 2585, 2590, 2595, 2600, 2605, 2610, 2615, 2620, 2625],
            'segment_analysis': {
                'direction': 'up',
                'confidence': 70,
                'end_confirmed': False
            }
        },
        # 场景4: 上涨线段终结
        {
            'name': '上涨线段终结',
            'prices': [2625, 2630, 2635, 2640, 2645, 2650, 2655, 2660, 2665, 2670],
            'segment_analysis': {
                'direction': 'up',
                'confidence': 88,
                'end_confirmed': True
            }
        },
        # 场景5: 回调整理
        {
            'name': '回调整理',
            'prices': [2670, 2665, 2660, 2655, 2650, 2645, 2640, 2635, 2630, 2625],
            'segment_analysis': {
                'direction': 'down',
                'confidence': 60,
                'end_confirmed': False
            }
        }
    ]
    
    return scenarios

def create_df_from_prices(prices, base_time, interval_minutes=5):
    """
    根据价格序列创建K线数据
    """
    timestamps = [base_time + timedelta(minutes=i*interval_minutes) for i in range(len(prices))]
    
    data = {
        'timestamp': timestamps,
        'open': [],
        'high': [],
        'low': [],
        'close': prices,
        'volume': [1000 + i*100 for i in range(len(prices))]
    }
    
    # 生成开高低价
    for i, close_price in enumerate(prices):
        if i == 0:
            open_price = close_price + 2
        else:
            open_price = prices[i-1]
        
        high_price = max(open_price, close_price) + abs(close_price - open_price) * 0.3
        low_price = min(open_price, close_price) - abs(close_price - open_price) * 0.3
        
        data['open'].append(open_price)
        data['high'].append(high_price)
        data['low'].append(low_price)
    
    return pd.DataFrame(data)

def create_sample_analysis_data(df):
    """
    创建示例的分析数据（笔、中枢、买卖点）
    """
    timestamps = df['timestamp'].tolist()
    
    # 模拟笔列表
    bi_list = [
        ('down', timestamps[0], df['high'].iloc[0], timestamps[4], df['low'].iloc[4]),
        ('up', timestamps[4], df['low'].iloc[4], timestamps[8], df['high'].iloc[8])
    ]
    
    # 模拟中枢
    mid_price = (df['high'].max() + df['low'].min()) / 2
    pivot_zones = [
        (timestamps[2], timestamps[7], mid_price + 10, mid_price - 10, mid_price + 15, mid_price - 15, 'type1')
    ]
    
    # 模拟买卖点（通常为空，让线段分析来生成）
    buy_points_all = {'1': ([], []), '2': ([], []), '3': ([], [])}
    sell_points_all = {'1': ([], []), '2': ([], []), '3': ([], [])}
    
    return bi_list, pivot_zones, buy_points_all, sell_points_all

def run_trading_simulation():
    """
    运行完整的交易模拟
    """
    print("🚀 启动交易系统演示")
    print("="*80)
    
    # 1. 初始化交易系统
    trading_system = TradingSystem(
        initial_capital=100000,  # 10万初始资金
        max_risk_per_trade=0.02,  # 单笔最大风险2%
        max_total_risk=0.10       # 总风险敞口10%
    )
    
    # 2. 获取市场数据序列
    scenarios = create_market_data_sequence()
    base_time = datetime.now()
    
    # 3. 逐个场景进行交易
    for i, scenario in enumerate(scenarios):
        print("\n" + "🎬 场景 {}: {}".format(i+1, scenario['name']))
        print("="*80)
        
        # 创建当前场景的市场数据
        current_time = base_time + timedelta(hours=i)
        df = create_df_from_prices(scenario['prices'], current_time)
        bi_list, pivot_zones, buy_points_all, sell_points_all = create_sample_analysis_data(df)
        
        # 执行分析和交易
        analysis, decisions = trading_system.analyze_and_trade(
            df, bi_list, pivot_zones, buy_points_all, sell_points_all, 
            scenario['segment_analysis']
        )
        
        # 模拟价格变化，触发止损止盈
        if trading_system.positions:
            print("\n📊 模拟价格变化...")
            # 模拟价格向有利方向变化
            if scenario['segment_analysis']['direction'] == 'down' and scenario['segment_analysis']['confidence'] >= 80:
                # 下跌线段终结，价格应该反弹
                new_price = df['close'].iloc[-1] * 1.02  # 上涨2%
                print("   价格上涨至: {:.2f}".format(new_price))
                trading_system._update_positions(new_price)
                trading_system._risk_check(new_price)
            elif scenario['segment_analysis']['direction'] == 'up' and scenario['segment_analysis']['confidence'] >= 80:
                # 上涨线段终结，价格应该下跌
                new_price = df['close'].iloc[-1] * 0.98  # 下跌2%
                print("   价格下跌至: {:.2f}".format(new_price))
                trading_system._update_positions(new_price)
                trading_system._risk_check(new_price)
        
        # 暂停一下，模拟实际交易间隔
        print("\n⏳ 等待下一个交易周期...")
        time.sleep(1)  # 实际使用中可能是几分钟或几小时
    
    # 4. 生成最终绩效报告
    print("\n" + "🏁 交易模拟完成")
    trading_system.print_performance_report()
    
    # 5. 显示详细交易历史
    print("\n📋 交易历史详情:")
    print("-" * 80)
    for trade in trading_system.trade_history:
        print("{} | {} | {} | 数量:{} | 价格:{:.2f} | {}".format(
            trade['timestamp'].strftime('%H:%M:%S'),
            trade['action'].upper(),
            trade['symbol'],
            trade['quantity'],
            trade['price'],
            trade['reason']
        ))
    
    return trading_system

def run_price_monitoring_demo():
    """
    演示价格监控功能
    """
    print("\n" + "🔔 价格监控演示")
    print("="*80)
    
    # 初始化交易系统
    trading_system = TradingSystem(initial_capital=50000)
    
    # 设置监控目标
    target_price = 2620.88
    current_price = 2625.0
    monitoring_active = True
    check_count = 0
    max_checks = 5  # 最多检查5次
    
    print("🎯 开始监控价格，目标点位: {:.2f}".format(target_price))
    print("💰 当前价格: {:.2f}".format(current_price))
    
    while monitoring_active and check_count < max_checks:
        check_count += 1
        
        # 模拟价格变化
        if check_count <= 3:
            current_price -= 1.5  # 逐步下跌
        else:
            current_price -= 0.5  # 接近目标
        
        print("\n🔍 第{}次检查 - 当前价格: {:.2f}".format(check_count, current_price))
        
        # 检查是否达到目标价格
        if abs(current_price - target_price) <= 0.5:  # 容差0.5
            print("🔔 价格达到目标！触发分析...")
            
            # 创建市场数据
            base_time = datetime.now()
            prices = [current_price + i*0.2 for i in range(-5, 5)]  # 围绕当前价格的序列
            df = create_df_from_prices(prices, base_time)
            bi_list, pivot_zones, buy_points_all, sell_points_all = create_sample_analysis_data(df)
            
            # 模拟强烈的买入信号
            segment_analysis = {
                'direction': 'down',
                'confidence': 92,
                'end_confirmed': True
            }
            
            # 执行分析和交易
            analysis, decisions = trading_system.analyze_and_trade(
                df, bi_list, pivot_zones, buy_points_all, sell_points_all, segment_analysis
            )
            
            monitoring_active = False
            print("✅ 监控完成，已执行交易决策")
        else:
            distance = abs(current_price - target_price)
            print("   距离目标还有: {:.2f}，继续监控...".format(distance))
            time.sleep(0.5)  # 模拟检查间隔
    
    if check_count >= max_checks:
        print("⏰ 达到最大检查次数，停止监控")
    
    return trading_system

def main():
    """
    主演示函数
    """
    print("🎯 交易系统完整演示")
    print("="*100)
    
    # 演示1: 完整交易流程
    print("\n📈 演示1: 完整交易流程模拟")
    trading_system1 = run_trading_simulation()
    
    # 演示2: 价格监控
    print("\n📊 演示2: 价格监控功能")
    trading_system2 = run_price_monitoring_demo()
    
    print("\n🎉 所有演示完成！")
    print("="*100)
    
    # 总结
    print("\n📋 演示总结:")
    print("1. ✅ 市场分析与交易决策集成")
    print("2. ✅ 自动仓位管理和风险控制")
    print("3. ✅ 止损止盈自动执行")
    print("4. ✅ 价格监控和触发机制")
    print("5. ✅ 完整的交易记录和绩效统计")
    
    return trading_system1, trading_system2

if __name__ == "__main__":
    main() 