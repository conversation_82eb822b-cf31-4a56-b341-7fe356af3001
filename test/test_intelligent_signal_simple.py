#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

class MockMultiLevelAnalysis:
    """模拟MultiLevelAnalysis类，用于测试智能信号验证系统"""
    
    def _analyze_market_context(self, df):
        """分析市场上下文"""
        current_price = df['close'].iloc[-1]
        
        # 趋势强度分析
        ma_short = df['close'].iloc[-5:].mean()
        ma_long = df['close'].iloc[-20:].mean()
        
        trend_strength = abs(ma_short - ma_long) / ma_long
        trend_direction = 'up' if ma_short > ma_long else 'down'
        is_trending = trend_strength > 0.02
        
        # 波动率分析
        returns = df['close'].pct_change().iloc[-20:]
        volatility = returns.std()
        is_high_vol = volatility > 0.03
        
        # 价格位置分析
        recent_high = df['high'].iloc[-20:].max()
        recent_low = df['low'].iloc[-20:].min()
        price_position = (current_price - recent_low) / (recent_high - recent_low) if recent_high != recent_low else 0.5
        
        # 成交量分析
        avg_volume = df['volume'].iloc[-20:].mean()
        current_volume = df['volume'].iloc[-1]
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
        
        return {
            'current_price': current_price,
            'trend_strength': trend_strength,
            'trend_direction': trend_direction,
            'is_trending': is_trending,
            'volatility': volatility,
            'is_high_vol': is_high_vol,
            'price_position': price_position,
            'volume_ratio': volume_ratio,
            'ma_short': ma_short,
            'ma_long': ma_long
        }
    
    def _calculate_profit_potential(self, signal, df, market_context):
        """计算利润空间潜力"""
        current_price = market_context['current_price']
        
        take_profit1 = signal.get('take_profit1')
        stop_loss = signal.get('stop_loss')
        
        profit_analysis = {
            'has_targets': False,
            'profit_potential': 0,
            'risk_reward_ratio': 0,
            'remaining_space': 0,
            'is_profitable': False,
            'next_level_suggestion': None
        }
        
        if take_profit1 and stop_loss:
            profit_analysis['has_targets'] = True
            
            if signal['type'].startswith('买') or 'buy' in signal['type'].lower():
                profit_pct1 = (take_profit1 - current_price) / current_price * 100
                risk_pct = (current_price - stop_loss) / current_price * 100
                
                profit_analysis['profit_potential'] = profit_pct1
                profit_analysis['remaining_space'] = profit_pct1
                profit_analysis['risk_reward_ratio'] = profit_pct1 / risk_pct if risk_pct > 0 else 0
                profit_analysis['is_profitable'] = profit_pct1 > 1.0
                
                if profit_pct1 > 3.0:
                    profit_analysis['next_level_suggestion'] = f"利润空间充足({profit_pct1:.1f}%)，可等待次级别优化进场点"
                elif profit_pct1 > 1.5:
                    profit_analysis['next_level_suggestion'] = f"利润空间一般({profit_pct1:.1f}%)，建议次级别确认后进场"
                else:
                    profit_analysis['next_level_suggestion'] = f"利润空间有限({profit_pct1:.1f}%)，建议等待更好时机"
            else:
                profit_pct1 = (current_price - take_profit1) / current_price * 100
                risk_pct = (stop_loss - current_price) / current_price * 100
                
                profit_analysis['profit_potential'] = profit_pct1
                profit_analysis['remaining_space'] = profit_pct1
                profit_analysis['risk_reward_ratio'] = profit_pct1 / risk_pct if risk_pct > 0 else 0
                profit_analysis['is_profitable'] = profit_pct1 > 1.0
                
                if profit_pct1 > 3.0:
                    profit_analysis['next_level_suggestion'] = f"下跌空间充足({profit_pct1:.1f}%)，可等待次级别优化进场点"
                elif profit_pct1 > 1.5:
                    profit_analysis['next_level_suggestion'] = f"下跌空间一般({profit_pct1:.1f}%)，建议次级别确认后进场"
                else:
                    profit_analysis['next_level_suggestion'] = f"下跌空间有限({profit_pct1:.1f}%)，建议等待更好时机"
        
        return profit_analysis
    
    def _calculate_context_adjusted_validity(self, signal, market_context, timeframe):
        """根据上下文调整信号有效期"""
        base_validity = {
            '1m': 8, '5m': 12, '15m': 18, '30m': 25, '1h': 35
        }.get(timeframe, 12)
        
        adjusted_validity = base_validity
        
        if signal['strength'] >= 9:
            adjusted_validity *= 1.5
        elif signal['strength'] >= 7:
            adjusted_validity *= 1.2
        elif signal['strength'] <= 5:
            adjusted_validity *= 0.8
        
        if '第一类' in signal['type']:
            adjusted_validity *= 1.3
        elif '第三类' in signal['type']:
            adjusted_validity *= 0.9
        
        if market_context['is_trending']:
            if '第一类' in signal['type'] or '线段终结' in signal['type']:
                adjusted_validity *= 1.4
        else:
            if '中枢' in signal['type']:
                adjusted_validity *= 1.2
        
        if market_context['is_high_vol']:
            adjusted_validity *= 0.7
        
        return int(adjusted_validity)
    
    def _calculate_signal_relevance(self, signal, market_context):
        """计算信号在当前市场状态下的相关性"""
        relevance_score = signal['strength']
        
        if signal['type'].startswith('买') or 'buy' in signal['type'].lower():
            if market_context['price_position'] < 0.3:
                relevance_score *= 1.2
            elif market_context['price_position'] > 0.7:
                relevance_score *= 0.8
        else:
            if market_context['price_position'] > 0.7:
                relevance_score *= 1.2
            elif market_context['price_position'] < 0.3:
                relevance_score *= 0.8
        
        if market_context['is_trending']:
            if '第一类' in signal['type']:
                relevance_score *= 1.3
        
        if market_context['volume_ratio'] > 1.5:
            relevance_score *= 1.1
        elif market_context['volume_ratio'] < 0.7:
            relevance_score *= 0.9
        
        return min(10.0, relevance_score)
    
    def _make_intelligent_recommendation(self, signal, market_context, profit_analysis, time_validity, timeframe):
        """综合判断是否推荐信号"""
        current_bar_position = signal['bar_position']
        
        if current_bar_position > time_validity:
            if profit_analysis['is_profitable'] and profit_analysis['remaining_space'] > 3.0:
                next_level_map = {'1h': '30m', '30m': '15m', '15m': '5m', '5m': '1m'}
                next_level = next_level_map.get(timeframe, '更小级别')
                return False, f"信号过时({current_bar_position}>{time_validity}根K线)，但{profit_analysis['next_level_suggestion']}，建议关注{next_level}级别进场时机"
            else:
                return False, f"信号已过时({current_bar_position}>{time_validity}根K线)且利润空间不足({profit_analysis['remaining_space']:.1f}%)"
        
        if not profit_analysis['is_profitable']:
            return False, f"利润空间不足({profit_analysis['remaining_space']:.1f}%)，建议等待更好时机"
        
        if profit_analysis['risk_reward_ratio'] < 1.5:
            next_level_map = {'1h': '30m', '30m': '15m', '15m': '5m', '5m': '1m'}
            next_level = next_level_map.get(timeframe, '更小级别')
            return False, f"风险收益比不佳({profit_analysis['risk_reward_ratio']:.1f}:1)，{profit_analysis['next_level_suggestion']}，关注{next_level}级别"
        
        signal_relevance = self._calculate_signal_relevance(signal, market_context)
        if signal_relevance < 6.5:
            return False, f"当前市场状态下信号相关性不足({signal_relevance:.1f}/10)，{profit_analysis['next_level_suggestion']}"
        
        reason_parts = []
        reason_parts.append(f"利润空间{profit_analysis['remaining_space']:.1f}%")
        reason_parts.append(f"风险收益比{profit_analysis['risk_reward_ratio']:.1f}:1")
        reason_parts.append(f"相关性{signal_relevance:.1f}/10")
        
        if profit_analysis['remaining_space'] > 5.0:
            reason_parts.append("建议直接进场")
        elif profit_analysis['remaining_space'] > 3.0:
            next_level_map = {'1h': '30m', '30m': '15m', '15m': '5m', '5m': '1m'}
            next_level = next_level_map.get(timeframe, '更小级别')
            reason_parts.append(f"可直接进场或等待{next_level}级别优化")
        else:
            next_level_map = {'1h': '30m', '30m': '15m', '15m': '5m', '5m': '1m'}
            next_level = next_level_map.get(timeframe, '更小级别')
            reason_parts.append(f"建议等待{next_level}级别确认")
        
        return True, "智能推荐: " + ", ".join(reason_parts)
    
    def _intelligent_signal_validation(self, signal, df, timeframe, signal_type):
        """智能上下文感知系统"""
        try:
            market_context = self._analyze_market_context(df)
            profit_analysis = self._calculate_profit_potential(signal, df, market_context)
            time_validity = self._calculate_context_adjusted_validity(signal, market_context, timeframe)
            
            return self._make_intelligent_recommendation(
                signal, market_context, profit_analysis, time_validity, timeframe
            )
        except Exception as e:
            is_valid = signal['bar_position'] <= 10
            reason = f"简单时效性检查: {'有效' if is_valid else '已过时'}"
            return is_valid, reason

def create_test_dataframe(current_price, market_state, price_position=0.5):
    """创建测试数据"""
    np.random.seed(42)
    length = 100
    
    start_time = datetime.now() - timedelta(hours=length)
    timestamps = [start_time + timedelta(hours=i) for i in range(length)]
    
    prices = []
    base_price = current_price
    
    for i in range(length):
        if market_state == 'trending_up':
            change = np.random.normal(0.2, 0.8)
        elif market_state == 'trending_down':
            change = np.random.normal(-0.2, 0.8)
        else:
            change = np.random.normal(0, 0.6)
        
        base_price *= (1 + change / 100)
        prices.append(base_price)
    
    prices[-1] = current_price
    
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        open_price = prices[i-1] if i > 0 else close
        high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.3)) / 100)
        low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.3)) / 100)
        volume = np.random.randint(5000, 15000)
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def test_intelligent_signal_system():
    """测试智能上下文感知系统"""
    print("="*80)
    print("智能上下文感知系统测试")
    print("="*80)
    
    # 测试场景
    scenarios = [
        {
            'name': '强信号充足利润空间',
            'signal': {
                'type': '第一类买点',
                'strength': 9,
                'bar_position': 5,
                'stop_loss': 2600.0,
                'take_profit1': 2750.0,
            },
            'current_price': 2650.0,
            'market_state': 'trending_up',
            'expected_result': True
        },
        {
            'name': '过时信号但利润充足',
            'signal': {
                'type': '第二类买点',
                'strength': 7,
                'bar_position': 20,
                'stop_loss': 2600.0,
                'take_profit1': 2730.0,
            },
            'current_price': 2650.0,
            'market_state': 'trending_up',
            'expected_result': False
        },
        {
            'name': '强信号利润空间不足',
            'signal': {
                'type': '第一类买点',
                'strength': 9,
                'bar_position': 3,
                'stop_loss': 2640.0,
                'take_profit1': 2660.0,
            },
            'current_price': 2650.0,
            'market_state': 'sideways',
            'expected_result': False
        }
    ]
    
    analyzer = MockMultiLevelAnalysis()
    results = []
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"【场景 {i}：{scenario['name']}】")
        print("-" * 50)
        
        df = create_test_dataframe(scenario['current_price'], scenario['market_state'])
        
        signal = scenario['signal']
        current_price = scenario['current_price']
        profit_space = (signal['take_profit1'] - current_price) / current_price * 100
        risk_space = abs(signal['stop_loss'] - current_price) / current_price * 100
        
        print(f"信号类型: {signal['type']}")
        print(f"信号强度: {signal['strength']}/10")
        print(f"信号位置: {signal['bar_position']}根K线前")
        print(f"利润空间: {profit_space:.1f}%")
        print(f"风险空间: {risk_space:.1f}%")
        
        should_recommend, reason = analyzer._intelligent_signal_validation(
            signal, df, '15m', 'buy'
        )
        
        print(f"智能系统判断: {'推荐' if should_recommend else '不推荐'}")
        print(f"判断理由: {reason}")
        
        is_correct = should_recommend == scenario['expected_result']
        print(f"测试结果: {'✅ 通过' if is_correct else '❌ 失败'}")
        
        results.append({
            'scenario': scenario['name'],
            'correct': is_correct
        })
        
        print()
    
    # 总结
    passed = sum(1 for r in results if r['correct'])
    total = len(results)
    
    print("="*80)
    print("测试结果总结")
    print("="*80)
    print(f"通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    print("\n✅ 智能系统优势:")
    print("1. 综合考虑利润空间、风险收益比、市场状态")
    print("2. 为过时但有价值的信号提供次级别建议")
    print("3. 动态调整有效期，避免机械截止")
    print("4. 提供具体的操作建议和理由")
    
    return results

if __name__ == "__main__":
    test_intelligent_signal_system()
