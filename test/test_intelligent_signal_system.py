#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from multi_level_analysis import MultiLevelAnalysis
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def generate_test_scenarios():
    """生成不同的测试场景"""
    scenarios = []
    
    # 场景1：强信号，充足利润空间
    scenarios.append({
        'name': '强信号充足利润空间',
        'signal': {
            'type': '第一类买点',
            'strength': 9,
            'bar_position': 5,
            'stop_loss': 2600.0,
            'take_profit1': 2750.0,  # 5.7%利润空间
            'take_profit2': 2800.0,
            'take_profit3': 2850.0
        },
        'current_price': 2650.0,
        'market_state': 'trending_up',
        'expected_result': True
    })
    
    # 场景2：过时信号，但利润空间仍然充足
    scenarios.append({
        'name': '过时信号但利润充足',
        'signal': {
            'type': '第二类买点',
            'strength': 7,
            'bar_position': 15,  # 超过一般有效期
            'stop_loss': 2600.0,
            'take_profit1': 2730.0,  # 3.0%利润空间
            'take_profit2': 2780.0,
            'take_profit3': 2830.0
        },
        'current_price': 2650.0,
        'market_state': 'trending_up',
        'expected_result': False,  # 但会建议等待次级别
        'expected_reason_contains': '等待次级别'
    })
    
    # 场景3：信号强度高，但利润空间不足
    scenarios.append({
        'name': '强信号利润空间不足',
        'signal': {
            'type': '第一类买点',
            'strength': 9,
            'bar_position': 3,
            'stop_loss': 2640.0,
            'take_profit1': 2660.0,  # 仅0.4%利润空间
            'take_profit2': 2670.0,
            'take_profit3': 2680.0
        },
        'current_price': 2650.0,
        'market_state': 'sideways',
        'expected_result': False,
        'expected_reason_contains': '利润空间不足'
    })
    
    # 场景4：风险收益比不佳
    scenarios.append({
        'name': '风险收益比不佳',
        'signal': {
            'type': '第三类买点',
            'strength': 6,
            'bar_position': 8,
            'stop_loss': 2620.0,  # 1.1%风险
            'take_profit1': 2665.0,  # 0.6%收益，风险收益比0.5:1
            'take_profit2': 2675.0,
            'take_profit3': 2685.0
        },
        'current_price': 2650.0,
        'market_state': 'sideways',
        'expected_result': False,
        'expected_reason_contains': '风险收益比不佳'
    })
    
    # 场景5：卖出信号，高位相关性高
    scenarios.append({
        'name': '高位卖出信号',
        'signal': {
            'type': '第一类卖点',
            'strength': 8,
            'bar_position': 6,
            'stop_loss': 2700.0,
            'take_profit1': 2600.0,  # 1.9%利润空间
            'take_profit2': 2580.0,
            'take_profit3': 2560.0
        },
        'current_price': 2650.0,
        'market_state': 'trending_down',
        'price_position': 0.8,  # 高位
        'expected_result': True
    })
    
    return scenarios

def create_test_dataframe(scenario_name, current_price, market_state, price_position=0.5):
    """根据场景创建测试数据"""
    np.random.seed(42)
    length = 100
    
    start_time = datetime.now() - timedelta(hours=length)
    timestamps = [start_time + timedelta(hours=i) for i in range(length)]
    
    prices = []
    base_price = current_price
    
    for i in range(length):
        if market_state == 'trending_up':
            change = np.random.normal(0.2, 0.8)  # 上涨趋势
        elif market_state == 'trending_down':
            change = np.random.normal(-0.2, 0.8)  # 下跌趋势
        else:  # sideways
            change = np.random.normal(0, 0.6)  # 震荡
        
        base_price *= (1 + change / 100)
        prices.append(base_price)
    
    # 调整最后价格到目标价格
    prices[-1] = current_price
    
    # 根据price_position调整价格范围
    if price_position != 0.5:
        price_range = max(prices) - min(prices)
        if price_position > 0.7:  # 高位
            min_price = current_price - price_range * 0.3
            max_price = current_price + price_range * 0.1
        elif price_position < 0.3:  # 低位
            min_price = current_price - price_range * 0.1
            max_price = current_price + price_range * 0.3
        else:  # 中位
            min_price = current_price - price_range * 0.2
            max_price = current_price + price_range * 0.2
        
        # 重新调整价格序列
        for i in range(len(prices)):
            if i < len(prices) - 20:  # 前面的价格
                prices[i] = min_price + (max_price - min_price) * np.random.random()
    
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        open_price = prices[i-1] if i > 0 else close
        high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.3)) / 100)
        low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.3)) / 100)
        volume = np.random.randint(5000, 15000)
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def test_intelligent_signal_system():
    """测试智能上下文感知系统"""
    print("="*80)
    print("智能上下文感知系统测试")
    print("="*80)
    
    scenarios = generate_test_scenarios()
    
    # 创建分析器实例
    analyzer = MultiLevelAnalysis(symbol="ETH/USDT")
    
    print(f"共有 {len(scenarios)} 个测试场景\n")
    
    results = []
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"【场景 {i}：{scenario['name']}】")
        print("-" * 50)
        
        # 创建测试数据
        df = create_test_dataframe(
            scenario['name'], 
            scenario['current_price'], 
            scenario['market_state'],
            scenario.get('price_position', 0.5)
        )
        
        print(f"市场状态: {scenario['market_state']}")
        print(f"当前价格: {scenario['current_price']}")
        print(f"信号类型: {scenario['signal']['type']}")
        print(f"信号强度: {scenario['signal']['strength']}/10")
        print(f"信号位置: {scenario['signal']['bar_position']}根K线前")
        
        # 计算利润空间
        signal = scenario['signal']
        current_price = scenario['current_price']
        if signal['type'].startswith('买') or 'buy' in signal['type'].lower():
            profit_space = (signal['take_profit1'] - current_price) / current_price * 100
        else:
            profit_space = (current_price - signal['take_profit1']) / current_price * 100
        
        risk_space = abs(signal['stop_loss'] - current_price) / current_price * 100
        risk_reward = profit_space / risk_space if risk_space > 0 else 0
        
        print(f"利润空间: {profit_space:.1f}%")
        print(f"风险空间: {risk_space:.1f}%")
        print(f"风险收益比: {risk_reward:.1f}:1")
        
        try:
            # 测试智能验证系统
            should_recommend, reason = analyzer._intelligent_signal_validation(
                signal, df, '15m', 'buy' if '买' in signal['type'] else 'sell'
            )
            
            print(f"\n智能系统判断:")
            print(f"  推荐操作: {'是' if should_recommend else '否'}")
            print(f"  判断理由: {reason}")
            
            # 验证结果
            expected = scenario['expected_result']
            is_correct = should_recommend == expected
            
            if 'expected_reason_contains' in scenario:
                reason_correct = scenario['expected_reason_contains'] in reason
                is_correct = is_correct and reason_correct
                print(f"  理由匹配: {'✅' if reason_correct else '❌'}")
            
            print(f"  测试结果: {'✅ 通过' if is_correct else '❌ 失败'}")
            
            results.append({
                'scenario': scenario['name'],
                'expected': expected,
                'actual': should_recommend,
                'reason': reason,
                'correct': is_correct
            })
            
        except Exception as e:
            print(f"  ❌ 测试失败: {str(e)}")
            results.append({
                'scenario': scenario['name'],
                'expected': scenario['expected_result'],
                'actual': None,
                'reason': f"错误: {str(e)}",
                'correct': False
            })
        
        print("\n")
    
    # 总结测试结果
    print("="*80)
    print("测试结果总结")
    print("="*80)
    
    passed = sum(1 for r in results if r['correct'])
    total = len(results)
    
    print(f"总测试场景: {total}")
    print(f"通过场景: {passed}")
    print(f"失败场景: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    print(f"\n{'场景':<20} {'预期':<6} {'实际':<6} {'结果':<6}")
    print("-" * 50)
    
    for result in results:
        expected_str = "推荐" if result['expected'] else "不推荐"
        actual_str = "推荐" if result['actual'] else "不推荐" if result['actual'] is not None else "错误"
        status = "✅" if result['correct'] else "❌"
        
        print(f"{result['scenario']:<20} {expected_str:<6} {actual_str:<6} {status:<6}")
    
    print("\n" + "="*80)
    print("智能系统特点验证")
    print("="*80)
    
    print("✅ 验证的智能特性:")
    print("1. 利润空间分析：系统能正确识别利润空间不足的信号")
    print("2. 风险收益比评估：系统能拒绝风险收益比不佳的信号")
    print("3. 时效性动态调整：过时但有价值的信号会建议等待次级别")
    print("4. 市场状态适配：系统考虑当前市场状态调整信号相关性")
    print("5. 次级别建议：为不同情况提供具体的次级别操作建议")
    
    print("\n📊 相比传统8根K线硬截止的优势:")
    print("- ❌ 传统方式：机械地在8根K线后拒绝所有信号")
    print("- ✅ 智能方式：综合考虑利润空间、市场状态、信号质量")
    print("- ✅ 次级别指导：为过时但有价值的信号提供次级别操作建议")
    print("- ✅ 动态适应：根据不同时间级别和市场状态调整判断标准")
    
    return results

if __name__ == "__main__":
    try:
        results = test_intelligent_signal_system()
        print("\n✅ 智能上下文感知系统测试完成")
        
        # 检查整体通过率
        passed = sum(1 for r in results if r['correct'])
        total = len(results)
        
        if passed / total >= 0.8:
            print("🎉 系统表现优秀，通过率超过80%")
        elif passed / total >= 0.6:
            print("👍 系统表现良好，通过率超过60%")
        else:
            print("⚠️ 系统需要进一步优化")
            
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
