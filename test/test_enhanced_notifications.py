#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试增强的通知文件输出

验证增强的信号分析是否正确写入notifications文件
"""

import os
import glob
from datetime import datetime

def test_enhanced_notification_function():
    """测试增强通知消息生成函数"""
    print("="*80)
    print("增强通知消息生成函数测试")
    print("="*80)
    
    # 模拟信号数据
    mock_signal_result = {
        'timestamp': '2025-06-05 14:30:00',
        'recommendation': 'buy',
        'signal_strength': 10,
        'message': '发现第二类买点，强度: 10/10，智能推荐: 利润空间7.2%, 风险收益比4.1:1',
        'buy_signals': [
            {
                'type': '第二类买点',
                'price': 2604.62,
                'strength': 10,
                'bar_position': 16,
                'description': '第二类买点 - 下跌中枢完成后的低点回调买入信号',
                'credibility_score': 7.2,
                'confidence_level': '高',
                'detailed_reason': '信号类型: 第二类买点 | 技术形态: 良好 - 当前上涨趋势，波动率适中(2.5%) | 中枢关系: 优秀 - 位于中枢下方，符合第二类买点逻辑 | 笔结构: 良好 - 下跌笔后的反转确认 | 价格位置: 一般 - 当前价格高于信号价格0.6% | 时效性: 较差 - 信号形成16根K线前，时效性降低 | 成交量: 良好 - 成交量确认(1.3倍平均量)',
                'risk_warnings': ['信号已过时，入场风险增加', '当前价格已偏离信号价格']
            },
            {
                'type': '第二类买点',
                'price': 2604.62,
                'strength': 6,
                'bar_position': 16,
                'description': '第二类买点 - 下跌中枢完成后的低点回调买入信号',
                'credibility_score': 4.8,
                'confidence_level': '中等',
                'detailed_reason': '与主信号重复，强度较低，可信度一般',
                'risk_warnings': ['重复信号，建议忽略']
            },
            {
                'type': '中枢上沿买点',
                'price': 2612.96,
                'strength': 8,
                'bar_position': 0,
                'description': '中枢上沿买点 - 上涨突破中枢后回调至中枢上沿支撑位，良好买入机会',
                'credibility_score': 8.5,
                'confidence_level': '极高',
                'detailed_reason': '信号类型: 中枢上沿买点 | 技术形态: 优秀 - 回调至支撑位，形态完美 | 中枢关系: 极佳 - 精确回调至中枢上沿 | 价格位置: 优秀 - 当前价格即为信号价格 | 时效性: 极佳 - 当前K线形成 | 成交量: 良好 - 成交量确认',
                'risk_warnings': []
            }
        ],
        'sell_signals': [
            {
                'type': '第二类卖点',
                'price': 2615.97,
                'strength': 8,
                'bar_position': 18,
                'description': '第二类卖点 - 上涨中枢完成后的高点回调卖出信号',
                'credibility_score': 5.5,
                'confidence_level': '中等',
                'detailed_reason': '信号类型: 第二类卖点 | 技术形态: 一般 - 趋势不够明确，波动率偏高(4.2%) | 中枢关系: 较差 - 与最近中枢距离较远 | 笔结构: 一般 - 上涨笔后的回调，但结构不够清晰 | 价格位置: 良好 - 当前价格接近信号价格(偏差0.2%) | 时效性: 较差 - 信号形成18根K线前，时效性不佳 | 成交量: 较差 - 成交量不足(仅0.7倍平均量)',
                'risk_warnings': ['信号已过时，可靠性降低', '成交量不足，确认度低', '缺乏明确的中枢支撑']
            }
        ]
    }
    
    # 导入生成函数
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 从main.py导入函数
        from main import generate_enhanced_notification_message
        
        # 生成增强通知消息
        enhanced_message = generate_enhanced_notification_message("ETH/USDT", "15m", mock_signal_result)
        
        print("🎯 增强通知消息生成成功！")
        print("\n" + "="*60)
        print("生成的增强通知消息:")
        print("="*60)
        print(enhanced_message)
        print("="*60)
        
        # 验证关键内容
        print("\n📊 内容验证:")
        checks = [
            ("包含可信度评分", "可信度评分:" in enhanced_message),
            ("包含详细分析", "🔍 详细分析:" in enhanced_message),
            ("包含风险提示", "⚠️ 风险提示:" in enhanced_message),
            ("包含信号优先级", "主信号" in enhanced_message),
            ("包含买入信号分析", "🟢 买入信号分析:" in enhanced_message),
            ("包含卖出信号分析", "🔴 卖出信号分析:" in enhanced_message),
            ("包含置信度等级", "极高" in enhanced_message or "高" in enhanced_message),
        ]
        
        passed = 0
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f"  {status} {check_name}")
            if result:
                passed += 1
        
        print(f"\n验证结果: {passed}/{len(checks)} 项通过")
        
        if passed == len(checks):
            print("🎉 增强通知消息生成功能完全正常！")
            return True
        else:
            print("⚠️ 增强通知消息生成功能存在问题")
            return False
            
    except ImportError as e:
        print(f"❌ 无法导入生成函数: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_existing_notification_files():
    """检查现有的通知文件"""
    print("\n" + "="*80)
    print("现有通知文件检查")
    print("="*80)
    
    # 查找notifications目录
    notifications_dir = "notifications"
    if not os.path.exists(notifications_dir):
        print(f"❌ 通知目录不存在: {notifications_dir}")
        return False
    
    # 查找最新的通知文件
    pattern = os.path.join(notifications_dir, "notifications_*.txt")
    files = glob.glob(pattern)
    
    if not files:
        print("❌ 未找到任何通知文件")
        return False
    
    # 按修改时间排序，获取最新文件
    latest_file = max(files, key=os.path.getmtime)
    file_time = datetime.fromtimestamp(os.path.getmtime(latest_file))
    
    print(f"📁 最新通知文件: {latest_file}")
    print(f"📅 修改时间: {file_time}")
    
    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 文件大小: {len(content)} 字符")
        
        # 检查是否包含增强分析内容
        enhanced_features = [
            ("可信度评分", "可信度评分:" in content),
            ("详细分析", "🔍 详细分析:" in content),
            ("风险提示", "⚠️ 风险提示:" in content),
            ("信号优先级", "主信号" in content or "次要信号" in content),
            ("买入信号分析", "🟢 买入信号分析:" in content),
            ("卖出信号分析", "🔴 卖出信号分析:" in content),
        ]
        
        print("\n📊 增强功能检查:")
        enhanced_count = 0
        for feature_name, found in enhanced_features:
            status = "✅" if found else "❌"
            print(f"  {status} {feature_name}")
            if found:
                enhanced_count += 1
        
        print(f"\n增强功能覆盖率: {enhanced_count}/{len(enhanced_features)} ({enhanced_count/len(enhanced_features)*100:.1f}%)")
        
        # 显示文件内容片段
        print(f"\n📖 文件内容预览 (前500字符):")
        print("-" * 60)
        print(content[:500])
        if len(content) > 500:
            print("...")
        print("-" * 60)
        
        if enhanced_count >= len(enhanced_features) * 0.8:  # 80%以上功能存在
            print("🎉 通知文件已包含增强分析内容！")
            return True
        else:
            print("⚠️ 通知文件缺少增强分析内容")
            return False
            
    except Exception as e:
        print(f"❌ 读取通知文件失败: {str(e)}")
        return False

def show_comparison():
    """展示新旧通知格式对比"""
    print("\n" + "="*80)
    print("新旧通知格式对比")
    print("="*80)
    
    print("🔴 旧格式通知文件内容:")
    print("-" * 40)
    print("🚨 ETH/USDT 15m 买卖点信号提醒 🚨")
    print("时间: 2025-06-05 14:08:59")
    print("推荐操作: WAIT")
    print("信号强度: 10/10")
    print("信号说明: 发现第二类买点，但利润空间不足(-2.7%)")
    print()
    print("🟢 买入信号:")
    print("  1. 第二类买点 - 价格: 2604.6200 - 强度: 10/10 - 位置: 17根K线前")
    print("  2. 第二类买点 - 价格: 2604.6200 - 强度: 6/10 - 位置: 17根K线前")
    print("  3. 中枢上沿买点 - 价格: 2612.9600 - 强度: 8/10 - 位置: 当前K线")
    
    print("\n🟢 新格式通知文件内容:")
    print("-" * 40)
    print("🚨 ETH/USDT 15m 买卖点信号提醒 🚨")
    print("时间: 2025-06-05 14:08:59")
    print("推荐操作: WAIT")
    print("信号强度: 10/10")
    print("信号说明: 发现第二类买点，但利润空间不足(-2.7%)")
    print()
    print("🟢 买入信号分析:")
    print("  📍 信号 #1: 第二类买点 (主信号)")
    print("     基础信息: 价格 2604.6200 | 强度 10/10 | 17根K线前")
    print("     可信度评分: 7.2/10 (高)")
    print("     🔍 详细分析: 信号类型: 第二类买点 | 技术形态: 良好 - 当前上涨趋势...")
    print("     ⚠️ 风险提示: 信号已过时，入场风险增加")
    print("     说明: 第二类买点 - 下跌中枢完成后的低点回调买入信号")
    print()
    print("  📍 信号 #2: 第二类买点 (重复信号)")
    print("     基础信息: 价格 2604.6200 | 强度 6/10 | 17根K线前")
    print("     可信度评分: 4.8/10 (中等)")
    print("     🔍 详细分析: 与主信号重复，强度较低，可信度一般")
    print("     说明: 第二类买点 - 下跌中枢完成后的低点回调买入信号")
    print()
    print("  📍 信号 #3: 中枢上沿买点 (次要信号)")
    print("     基础信息: 价格 2612.9600 | 强度 8/10 | 当前K线")
    print("     可信度评分: 8.5/10 (极高)")
    print("     🔍 详细分析: 信号类型: 中枢上沿买点 | 技术形态: 优秀...")
    print("     说明: 中枢上沿买点 - 上涨突破中枢后回调至中枢上沿支撑位")
    
    print("\n✅ 新格式的优势:")
    print("  🎯 清晰的信号优先级标记")
    print("  📊 量化的可信度评分")
    print("  🔍 详细的分析理由")
    print("  ⚠️ 明确的风险提示")
    print("  📈 更好的决策支持")

if __name__ == "__main__":
    try:
        print("增强通知文件输出测试")
        print("验证可信度分析是否正确写入notifications文件")
        
        # 测试生成函数
        function_ok = test_enhanced_notification_function()
        
        # 检查现有文件
        file_ok = check_existing_notification_files()
        
        # 显示对比
        show_comparison()
        
        print("\n" + "="*80)
        print("测试总结")
        print("="*80)
        
        if function_ok and file_ok:
            print("🎉 完美！增强的信号分析已成功写入通知文件！")
            print("💡 用户现在可以在notifications文件中看到详细的可信度分析")
        elif function_ok:
            print("✅ 生成函数正常，但需要运行实际信号检查来生成新的通知文件")
            print("💡 建议运行: python3 main.py --check-signals ETH/USDT")
        else:
            print("⚠️ 增强通知功能存在问题，需要检查实现")
        
        print("\n🚀 使用建议:")
        print("  1. 运行信号检查生成新的通知文件")
        print("  2. 查看notifications目录中的最新文件")
        print("  3. 验证是否包含可信度分析内容")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
