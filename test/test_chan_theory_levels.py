#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from chan_analysis import ChanAnalysis

def test_chan_theory_multi_level_targets():
    """测试基于缠论理论的多级别止盈目标"""
    print("="*80)
    print("基于缠论理论的多级别止盈目标测试")
    print("="*80)
    
    # 生成ETH/USDT模拟数据
    np.random.seed(42)
    length = 100
    base_price = 2650.0
    
    start_time = datetime.now() - timedelta(hours=length)
    timestamps = [start_time + timedelta(hours=i) for i in range(length)]
    
    prices = []
    current_price = base_price
    
    for i in range(length):
        change = np.random.normal(0, 0.8)
        current_price *= (1 + change / 100)
        current_price = max(2400, min(2800, current_price))
        prices.append(current_price)
    
    # 确保最后价格接近基准价格
    prices[-1] = base_price
    
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        open_price = prices[i-1] if i > 0 else close
        high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.3)) / 100)
        low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.3)) / 100)
        volume = np.random.randint(5000, 15000)
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    
    print(f"生成了 {len(df)} 条K线数据")
    print(f"价格范围: {df['low'].min():.2f} - {df['high'].max():.2f}")
    print(f"当前价格: {df['close'].iloc[-1]:.2f}")
    
    # 创建缠论分析器
    chan_analyzer = ChanAnalysis(df)
    chan_analyzer.pivot_zones = [
        (30, 60, 2700, 2600),  # 主要中枢
        (70, 90, 2750, 2650),  # 更高级别中枢
    ]
    
    # 测试不同时间级别的止盈目标
    timeframes = ['5m', '15m', '30m', '1h']
    entry_point = {
        'index': len(df) - 1,
        'price': 2650.0,
        'timestamp': df['timestamp'].iloc[-1].strftime('%Y-%m-%d %H:%M:%S')
    }
    
    print("\n" + "="*80)
    print("基于缠论理论的多级别买入信号分析")
    print("="*80)
    
    buy_results = {}
    for tf in timeframes:
        print(f"\n【{tf}级别分析】")
        
        targets = chan_analyzer.calculate_chan_profit_targets(
            entry_point=entry_point,
            signal_type='buy',
            buy_sell_point_type=1,  # 第一类买点
            related_pivot_idx=0,
            current_level=tf
        )
        buy_results[tf] = targets
        
        # 显示级别特征
        level_info = chan_analyzer._get_level_specific_pivot_info(tf)
        if level_info:
            print(f"级别特征:")
            print(f"  - 中枢高度比例: {level_info['pivot_height_ratio']*100:.1f}%")
            print(f"  - 延伸比例: {level_info['extension_ratio']:.1f}")
            print(f"  - 波动率因子: {level_info['volatility_factor']:.1f}")
        
        # 显示止盈目标
        profit_targets = targets.get('profit_targets', [])
        print(f"止盈目标:")
        for i, target in enumerate(profit_targets, 1):
            print(f"  目标{i}: {target['price']:.2f} ({target['expected_return']:+.1f}%) - {target['description']}")
    
    print("\n" + "="*80)
    print("基于缠论理论的多级别卖出信号分析")
    print("="*80)
    
    sell_results = {}
    for tf in timeframes:
        print(f"\n【{tf}级别分析】")
        
        targets = chan_analyzer.calculate_chan_profit_targets(
            entry_point=entry_point,
            signal_type='sell',
            buy_sell_point_type=1,  # 第一类卖点
            related_pivot_idx=0,
            current_level=tf
        )
        sell_results[tf] = targets
        
        # 显示止盈目标
        profit_targets = targets.get('profit_targets', [])
        print(f"止盈目标:")
        for i, target in enumerate(profit_targets, 1):
            print(f"  目标{i}: {target['price']:.2f} ({target['expected_return']:+.1f}%) - {target['description']}")
    
    # 对比分析表格
    print("\n" + "="*80)
    print("多级别对比分析表")
    print("="*80)
    
    print(f"{'级别':<6} {'方向':<4} {'止盈1':<10} {'止盈2':<10} {'止盈3':<10} {'止盈1%':<8} {'止盈2%':<8} {'止盈3%':<8}")
    print("-" * 70)
    
    for tf in timeframes:
        # 买入信号
        buy_targets = buy_results[tf].get('profit_targets', [])
        if len(buy_targets) >= 3:
            tp1, tp2, tp3 = buy_targets[0]['price'], buy_targets[1]['price'], buy_targets[2]['price']
            tp1_pct = (tp1 / entry_point['price'] - 1) * 100
            tp2_pct = (tp2 / entry_point['price'] - 1) * 100
            tp3_pct = (tp3 / entry_point['price'] - 1) * 100
            print(f"{tf:<6} {'买':<4} {tp1:<10.2f} {tp2:<10.2f} {tp3:<10.2f} {tp1_pct:<8.1f} {tp2_pct:<8.1f} {tp3_pct:<8.1f}")
        
        # 卖出信号
        sell_targets = sell_results[tf].get('profit_targets', [])
        if len(sell_targets) >= 3:
            tp1, tp2, tp3 = sell_targets[0]['price'], sell_targets[1]['price'], sell_targets[2]['price']
            tp1_pct = (tp1 / entry_point['price'] - 1) * 100
            tp2_pct = (tp2 / entry_point['price'] - 1) * 100
            tp3_pct = (tp3 / entry_point['price'] - 1) * 100
            print(f"{tf:<6} {'卖':<4} {tp1:<10.2f} {tp2:<10.2f} {tp3:<10.2f} {tp1_pct:<8.1f} {tp2_pct:<8.1f} {tp3_pct:<8.1f}")
    
    print("\n" + "="*80)
    print("缠论理论验证")
    print("="*80)
    
    # 验证买入信号的级别递增关系
    print("✅ 买入信号级别关系验证:")
    buy_tp1_values = []
    for tf in timeframes:
        targets = buy_results[tf].get('profit_targets', [])
        if targets:
            buy_tp1_values.append(targets[0]['price'])
    
    if len(buy_tp1_values) == len(timeframes):
        is_ascending = all(buy_tp1_values[i] <= buy_tp1_values[i+1] for i in range(len(buy_tp1_values)-1))
        print(f"  - 买入止盈目标随级别递增: {'✅' if is_ascending else '❌'}")
        
        for i, tf in enumerate(timeframes):
            if i == 0:
                print(f"  - {tf}: {buy_tp1_values[i]:.2f} (基准)")
            else:
                change = (buy_tp1_values[i] / buy_tp1_values[0] - 1) * 100
                print(f"  - {tf}: {buy_tp1_values[i]:.2f} (+{change:.1f}%)")
    
    # 验证卖出信号的级别递减关系
    print("\n✅ 卖出信号级别关系验证:")
    sell_tp1_values = []
    for tf in timeframes:
        targets = sell_results[tf].get('profit_targets', [])
        if targets:
            sell_tp1_values.append(targets[0]['price'])
    
    if len(sell_tp1_values) == len(timeframes):
        is_descending = all(sell_tp1_values[i] >= sell_tp1_values[i+1] for i in range(len(sell_tp1_values)-1))
        print(f"  - 卖出止盈目标随级别递减: {'✅' if is_descending else '❌'}")
        
        for i, tf in enumerate(timeframes):
            if i == 0:
                print(f"  - {tf}: {sell_tp1_values[i]:.2f} (基准)")
            else:
                change = (sell_tp1_values[i] / sell_tp1_values[0] - 1) * 100
                print(f"  - {tf}: {sell_tp1_values[i]:.2f} ({change:+.1f}%)")
    
    print("\n" + "="*80)
    print("缠论理论特点分析")
    print("="*80)
    
    print("📊 级别递归关系:")
    print("- 5m级别：基础级别，中枢高度约2%，延伸比例1.0")
    print("- 15m级别：中级别，中枢高度约3.5%，延伸比例1.5")
    print("- 30m级别：高级别，中枢高度约5%，延伸比例2.0")
    print("- 1h级别：更高级别，中枢高度约8%，延伸比例2.5")
    
    print("\n🎯 止盈目标设计:")
    print("- 第一目标：当前级别中枢突破")
    print("- 第二目标：基于中枢高度的测量目标")
    print("- 第三目标：趋势延续目标")
    
    print("\n⚖️ 与简单倍数法的区别:")
    print("- ❌ 简单倍数法：机械地乘以固定倍数，失去理论基础")
    print("- ✅ 缠论理论法：基于级别特征和中枢结构，保持理论一致性")
    print("- ✅ 动态调整：根据实际中枢情况动态计算目标")
    print("- ✅ 风险控制：目标幅度有合理上限，避免过度激进")
    
    return buy_results, sell_results

if __name__ == "__main__":
    try:
        buy_results, sell_results = test_chan_theory_multi_level_targets()
        print("\n✅ 基于缠论理论的多级别测试完成")
        print("现在的止盈目标计算基于缠论的级别递归理论，而不是简单的倍数相乘")
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
