#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MarketAnalyzer 使用示例
展示如何传入线段分析结果来获取买卖点信号
"""

from datetime import datetime, timedelta
import pandas as pd
from market_analyzer import MarketAnalyzer

def create_sample_data():
    """创建示例数据"""
    # 模拟K线数据
    timestamps = [datetime.now() - timedelta(minutes=i*5) for i in range(10)]
    timestamps.reverse()
    
    df = pd.DataFrame({
        'timestamp': timestamps,
        'open': [2625, 2623, 2621, 2619, 2617, 2615, 2618, 2620, 2622, 2624],
        'high': [2627, 2625, 2623, 2621, 2619, 2617, 2620, 2622, 2624, 2626],
        'low': [2623, 2621, 2619, 2617, 2615, 2613, 2616, 2618, 2620, 2622],
        'close': [2624, 2622, 2620, 2618, 2616, 2614, 2617, 2619, 2621, 2623],
        'volume': [1000, 1200, 1100, 1300, 1500, 1400, 1200, 1100, 1000, 900]
    })
    
    # 模拟笔列表
    bi_list = [
        ('down', timestamps[0], 2625, timestamps[4], 2614),
        ('up', timestamps[4], 2614, timestamps[9], 2623)
    ]
    
    # 模拟中枢
    pivot_zones = [
        (timestamps[2], timestamps[7], 2620.88, 2616.5, 2622, 2615, 'type1')
    ]
    
    # 模拟买卖点（空的，让线段分析来生成）
    buy_points_all = {'1': ([], []), '2': ([], []), '3': ([], [])}
    sell_points_all = {'1': ([], []), '2': ([], []), '3': ([], [])}
    
    return df, bi_list, pivot_zones, buy_points_all, sell_points_all

def main():
    """主函数"""
    print("=== MarketAnalyzer 线段终结分析示例 ===\n")
    
    # 创建分析器
    analyzer = MarketAnalyzer()
    
    # 获取示例数据
    df, bi_list, pivot_zones, buy_points_all, sell_points_all = create_sample_data()
    
    print("场景1: 下跌线段终结，置信度95%")
    print("-" * 50)
    
    # 模拟线段分析结果：下跌线段终结，置信度95%
    segment_analysis_1 = {
        'direction': 'down',      # 下跌线段
        'confidence': 95,         # 95%置信度
        'end_confirmed': True     # 终结确认
    }
    
    # 进行分析
    analysis_1 = analyzer.analyze_market_status(
        df, bi_list, pivot_zones, buy_points_all, sell_points_all, segment_analysis_1
    )
    
    # 打印报告
    analyzer.print_analysis_report(analysis_1)
    
    print("\n" + "="*80 + "\n")
    
    print("场景2: 上涨线段终结，置信度88%")
    print("-" * 50)
    
    # 模拟线段分析结果：上涨线段终结，置信度88%
    segment_analysis_2 = {
        'direction': 'up',        # 上涨线段
        'confidence': 88,         # 88%置信度
        'end_confirmed': True     # 终结确认
    }
    
    # 进行分析
    analysis_2 = analyzer.analyze_market_status(
        df, bi_list, pivot_zones, buy_points_all, sell_points_all, segment_analysis_2
    )
    
    # 打印报告
    analyzer.print_analysis_report(analysis_2)
    
    print("\n" + "="*80 + "\n")
    
    print("场景3: 下跌线段可能终结，置信度65%（弱信号）")
    print("-" * 50)
    
    # 模拟线段分析结果：弱信号
    segment_analysis_3 = {
        'direction': 'down',      # 下跌线段
        'confidence': 65,         # 65%置信度（弱信号）
        'end_confirmed': True     # 终结确认
    }
    
    # 进行分析
    analysis_3 = analyzer.analyze_market_status(
        df, bi_list, pivot_zones, buy_points_all, sell_points_all, segment_analysis_3
    )
    
    # 打印报告
    analyzer.print_analysis_report(analysis_3)
    
    print("\n" + "="*80 + "\n")
    
    print("场景4: 没有线段终结信号")
    print("-" * 50)
    
    # 不传入线段分析结果
    analysis_4 = analyzer.analyze_market_status(
        df, bi_list, pivot_zones, buy_points_all, sell_points_all
    )
    
    # 打印报告
    analyzer.print_analysis_report(analysis_4)

if __name__ == "__main__":
    main() 