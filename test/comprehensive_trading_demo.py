#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合交易系统演示
展示第一类、第二类、第三类买卖点与线段终结信号的结合使用
"""

from datetime import datetime, timedelta
import pandas as pd
import time
from trading_system import TradingSystem

def create_comprehensive_scenarios():
    """
    创建包含各类买卖点的综合场景
    """
    scenarios = [
        # 场景1: 第一类买点 + 线段终结
        {
            'name': '第一类买点 + 下跌线段终结',
            'prices': [2700, 2680, 2660, 2640, 2620, 2600, 2580, 2560, 2540, 2520],
            'buy_points': {
                '1': [
                    (8, 2540, 9, 0, "第一类买点：前一中枢ZG被向上突破后回调不跌破ZG"),  # 第一类买点
                ],
                '2': [],
                '3': []
            },
            'sell_points': {'1': [], '2': [], '3': []},
            'segment_analysis': {
                'direction': 'down',
                'confidence': 92,
                'end_confirmed': True
            }
        },
        
        # 场景2: 第二类买点
        {
            'name': '第二类买点',
            'prices': [2520, 2530, 2540, 2535, 2530, 2525, 2520, 2515, 2510, 2505],
            'buy_points': {
                '1': [],
                '2': [
                    (7, 2515, 8, 0, "第二类买点：盘整背驰后的买点"),  # 第二类买点
                ],
                '3': []
            },
            'sell_points': {'1': [], '2': [], '3': []},
            'segment_analysis': {
                'direction': 'down',
                'confidence': 65,
                'end_confirmed': False
            }
        },
        
        # 场景3: 第三类买点 + 弱线段信号
        {
            'name': '第三类买点 + 弱线段信号',
            'prices': [2505, 2510, 2515, 2520, 2525, 2530, 2535, 2540, 2545, 2550],
            'buy_points': {
                '1': [],
                '2': [],
                '3': [
                    (3, 2515, 7, 0, "第三类买点：向上突破前高"),  # 第三类买点
                ]
            },
            'sell_points': {'1': [], '2': [], '3': []},
            'segment_analysis': {
                'direction': 'down',
                'confidence': 70,
                'end_confirmed': True
            }
        },
        
        # 场景4: 第一类卖点 + 上涨线段终结
        {
            'name': '第一类卖点 + 上涨线段终结',
            'prices': [2550, 2570, 2590, 2610, 2630, 2650, 2670, 2690, 2710, 2730],
            'buy_points': {'1': [], '2': [], '3': []},
            'sell_points': {
                '1': [
                    (8, 2710, 9, 0, "第一类卖点：前一中枢ZD被向下突破后反弹不升破ZD"),  # 第一类卖点
                ],
                '2': [],
                '3': []
            },
            'segment_analysis': {
                'direction': 'up',
                'confidence': 94,
                'end_confirmed': True
            }
        },
        
        # 场景5: 多信号冲突
        {
            'name': '多信号冲突场景',
            'prices': [2730, 2725, 2720, 2715, 2710, 2705, 2700, 2695, 2690, 2685],
            'buy_points': {
                '1': [],
                '2': [],
                '3': [
                    (6, 2700, 6, 0, "第三类买点：弱信号"),  # 弱第三类买点
                ]
            },
            'sell_points': {
                '1': [],
                '2': [
                    (7, 2695, 7, 0, "第二类卖点：中等强度"),  # 第二类卖点
                ],
                '3': []
            },
            'segment_analysis': {
                'direction': 'down',
                'confidence': 75,
                'end_confirmed': False
            }
        }
    ]
    
    return scenarios

def create_df_from_scenario(scenario, base_time):
    """
    根据场景创建K线数据
    """
    prices = scenario['prices']
    timestamps = [base_time + timedelta(minutes=i*5) for i in range(len(prices))]
    
    data = {
        'timestamp': timestamps,
        'open': [],
        'high': [],
        'low': [],
        'close': prices,
        'volume': [1000 + i*100 for i in range(len(prices))]
    }
    
    # 生成开高低价
    for i, close_price in enumerate(prices):
        if i == 0:
            open_price = close_price + 2
        else:
            open_price = prices[i-1]
        
        high_price = max(open_price, close_price) + abs(close_price - open_price) * 0.3
        low_price = min(open_price, close_price) - abs(close_price - open_price) * 0.3
        
        data['open'].append(open_price)
        data['high'].append(high_price)
        data['low'].append(low_price)
    
    return pd.DataFrame(data)

def create_analysis_data_from_scenario(scenario, df):
    """
    根据场景创建分析数据
    """
    timestamps = df['timestamp'].tolist()
    
    # 模拟笔列表
    bi_list = [
        ('down', timestamps[0], df['high'].iloc[0], timestamps[4], df['low'].iloc[4]),
        ('up', timestamps[4], df['low'].iloc[4], timestamps[8], df['high'].iloc[8])
    ]
    
    # 模拟中枢
    mid_price = (df['high'].max() + df['low'].min()) / 2
    pivot_zones = [
        (timestamps[2], timestamps[7], mid_price + 10, mid_price - 10, mid_price + 15, mid_price - 15, 'type1')
    ]
    
    # 修正买卖点格式：每个类型应该是(buy_points, sell_points)元组
    buy_points_all = {
        '1': (scenario['buy_points']['1'], scenario['sell_points']['1']),
        '2': (scenario['buy_points']['2'], scenario['sell_points']['2']),
        '3': (scenario['buy_points']['3'], scenario['sell_points']['3'])
    }
    
    # 为了兼容原有接口，也创建sell_points_all
    sell_points_all = {
        '1': (scenario['sell_points']['1'], scenario['buy_points']['1']),
        '2': (scenario['sell_points']['2'], scenario['buy_points']['2']),
        '3': (scenario['sell_points']['3'], scenario['buy_points']['3'])
    }
    
    return bi_list, pivot_zones, buy_points_all, sell_points_all

def run_comprehensive_demo():
    """
    运行综合交易演示
    """
    print("🎯 综合交易系统演示")
    print("展示第一类、第二类、第三类买卖点与线段终结信号的结合")
    print("="*100)
    
    # 初始化交易系统
    trading_system = TradingSystem(
        initial_capital=100000,
        max_risk_per_trade=0.02,
        max_total_risk=0.15  # 稍微提高风险敞口以便演示
    )
    
    scenarios = create_comprehensive_scenarios()
    base_time = datetime.now()
    
    for i, scenario in enumerate(scenarios):
        print("\n" + "🎬 场景 {}: {}".format(i+1, scenario['name']))
        print("="*100)
        
        # 创建市场数据
        current_time = base_time + timedelta(hours=i*2)
        df = create_df_from_scenario(scenario, current_time)
        bi_list, pivot_zones, buy_points_all, sell_points_all = create_analysis_data_from_scenario(scenario, df)
        
        # 显示场景信息
        print("📊 场景信息:")
        print("   价格区间: {:.0f} - {:.0f}".format(min(scenario['prices']), max(scenario['prices'])))
        
        # 统计各类买卖点
        total_buy_points = sum(len(points) for points in buy_points_all.values())
        total_sell_points = sum(len(points) for points in sell_points_all.values())
        print("   传统买点: {}个".format(total_buy_points))
        print("   传统卖点: {}个".format(total_sell_points))
        print("   线段信号: {} (置信度{}%)".format(
            scenario['segment_analysis']['direction'], 
            scenario['segment_analysis']['confidence']
        ))
        
        # 执行分析和交易
        analysis, decisions = trading_system.analyze_and_trade(
            df, bi_list, pivot_zones, buy_points_all, sell_points_all, 
            scenario['segment_analysis']
        )
        
        # 模拟价格变化
        if trading_system.positions:
            print("\n📈 模拟价格变化...")
            # 根据信号类型模拟不同的价格变化
            last_price = df['close'].iloc[-1]
            
            if any('第一类' in pos['signal_type'] for pos in trading_system.positions):
                # 第一类买点通常有较大涨幅
                new_price = last_price * 1.08
                print("   第一类信号：价格大幅上涨至 {:.2f} (+8%)".format(new_price))
            elif any('第二类' in pos['signal_type'] for pos in trading_system.positions):
                # 第二类买点中等涨幅
                new_price = last_price * 1.05
                print("   第二类信号：价格中幅上涨至 {:.2f} (+5%)".format(new_price))
            elif any('第三类' in pos['signal_type'] for pos in trading_system.positions):
                # 第三类买点小幅涨幅
                new_price = last_price * 1.02
                print("   第三类信号：价格小幅上涨至 {:.2f} (+2%)".format(new_price))
            else:
                # 线段终结信号
                new_price = last_price * 1.03
                print("   线段信号：价格上涨至 {:.2f} (+3%)".format(new_price))
            
            trading_system._update_positions(new_price)
            trading_system._risk_check(new_price)
        
        print("\n⏳ 等待下一个场景...")
        time.sleep(1)
    
    # 最终报告
    print("\n" + "🏁 综合演示完成")
    trading_system.print_performance_report()
    
    # 详细交易分析
    print("\n📋 交易信号分析:")
    print("-" * 100)
    signal_stats = {}
    
    for trade in trading_system.trade_history:
        if trade['action'] in ['buy', 'sell']:
            signal_type = trade.get('signal_type', '未知')
            if signal_type not in signal_stats:
                signal_stats[signal_type] = {'count': 0, 'total_confidence': 0}
            signal_stats[signal_type]['count'] += 1
            signal_stats[signal_type]['total_confidence'] += trade.get('confidence', 0)
    
    for signal_type, stats in signal_stats.items():
        avg_confidence = stats['total_confidence'] / stats['count'] if stats['count'] > 0 else 0
        print("{}：{}次交易，平均置信度 {:.1f}/10".format(
            signal_type, stats['count'], avg_confidence
        ))
    
    return trading_system

def analyze_signal_priority():
    """
    分析信号优先级演示
    """
    print("\n" + "🔍 信号优先级分析演示")
    print("="*80)
    
    # 模拟多个信号同时出现的情况
    mock_signals = [
        {'type': '第三类', 'strength': 8, 'action': 'buy'},
        {'type': '线段终结买点', 'strength': 9, 'action': 'buy'},
        {'type': '第二类', 'strength': 7, 'action': 'buy'},
        {'type': '第一类', 'strength': 6, 'action': 'buy'},
    ]
    
    # 创建临时交易系统来测试信号选择
    temp_system = TradingSystem()
    
    print("📊 模拟信号列表:")
    for signal in mock_signals:
        print("   {} - 强度: {}/10".format(signal['type'], signal['strength']))
    
    # 测试信号选择逻辑
    best_signal = temp_system._select_best_signal(mock_signals, 'buy')
    
    print("\n🎯 系统选择结果:")
    print("   最佳信号: {} (强度: {}/10)".format(best_signal['type'], best_signal['strength']))
    print("\n💡 选择原理:")
    print("   1. 第一类买卖点优先级最高（即使强度较低）")
    print("   2. 线段终结信号次之")
    print("   3. 第二类买卖点第三")
    print("   4. 第三类买卖点最后")
    print("   5. 相同类型按强度排序")

if __name__ == "__main__":
    # 运行综合演示
    trading_system = run_comprehensive_demo()
    
    # 分析信号优先级
    analyze_signal_priority()
    
    print("\n🎉 所有演示完成！")
    print("\n📋 系统特点总结:")
    print("✅ 1. 智能信号优先级：第一类 > 线段终结 > 第二类 > 第三类")
    print("✅ 2. 差异化仓位管理：第一类重仓，第二类中仓，第三类轻仓")
    print("✅ 3. 自适应止损止盈：根据信号类型设置不同的风险收益比")
    print("✅ 4. 多重确认机制：弱信号需要趋势和位置确认")
    print("✅ 5. 综合决策系统：传统买卖点与线段分析完美结合") 