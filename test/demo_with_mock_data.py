#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模拟数据演示版本 - 避免API频率限制
使用模拟的比特币数据进行交易分析演示
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

from trading_system import TradingSystem
from market_analyzer import MarketAnalyzer

class MockDataTradingSystem:
    """
    模拟数据交易系统
    """
    
    def __init__(self, symbol="BTC-USD", initial_capital=100000):
        """
        初始化模拟数据交易系统
        """
        self.symbol = symbol
        self.trading_system = TradingSystem(initial_capital=initial_capital)
        self.analyzer = MarketAnalyzer()
        
        print("🚀 模拟数据交易系统已初始化")
        print("   交易品种: {} (模拟数据)".format(symbol))
        print("   初始资金: {:,.2f}".format(initial_capital))
    
    def generate_realistic_crypto_data(self, days=30, interval_hours=1):
        """
        生成逼真的加密货币价格数据
        """
        print("📊 正在生成模拟的{}数据...".format(self.symbol))
        
        # 基础参数
        base_price = 45000 if "BTC" in self.symbol else 3000  # BTC或ETH基础价格
        volatility = 0.02  # 2%波动率
        trend = 0.0001  # 轻微上涨趋势
        
        # 时间序列
        periods = days * 24 // interval_hours
        timestamps = [datetime.now() - timedelta(hours=i*interval_hours) for i in range(periods, 0, -1)]
        
        # 生成价格数据（使用几何布朗运动）
        np.random.seed(42)  # 固定随机种子，确保结果可重现
        
        prices = []
        current_price = base_price
        
        for i in range(periods):
            # 添加一些周期性波动
            cycle_factor = 1 + 0.05 * np.sin(i * 2 * np.pi / 24)  # 24小时周期
            
            # 随机游走
            random_change = np.random.normal(trend, volatility)
            current_price *= (1 + random_change) * cycle_factor
            
            # 添加一些突发事件
            if i % 50 == 0 and np.random.random() > 0.7:
                event_impact = np.random.choice([-0.1, 0.1])  # ±10%突发事件
                current_price *= (1 + event_impact)
            
            prices.append(current_price)
        
        # 生成OHLCV数据
        data = []
        for i, close in enumerate(prices):
            if i == 0:
                open_price = close
            else:
                open_price = prices[i-1]
            
            # 生成高低价
            daily_range = abs(close - open_price) * 1.5
            high = max(open_price, close) + daily_range * np.random.random() * 0.5
            low = min(open_price, close) - daily_range * np.random.random() * 0.5
            
            # 生成成交量
            volume = np.random.uniform(1000000, 5000000)
            
            data.append({
                'timestamp': timestamps[i],
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        
        print("✅ 模拟数据生成成功")
        print("   数据条数: {}".format(len(df)))
        print("   时间范围: {} 至 {}".format(
            df['timestamp'].iloc[0].strftime('%Y-%m-%d %H:%M'),
            df['timestamp'].iloc[-1].strftime('%Y-%m-%d %H:%M')
        ))
        print("   当前价格: {:.2f}".format(df['close'].iloc[-1]))
        
        return df
    
    def analyze_technical_patterns(self, df):
        """
        分析技术形态，生成笔、中枢和买卖点
        """
        print("🔍 正在分析技术形态...")
        
        # 简化的笔识别
        bi_list = self._identify_bi_patterns(df)
        
        # 简化的中枢识别
        pivot_zones = self._identify_pivot_zones(df, bi_list)
        
        # 简化的买卖点识别
        buy_points_all, sell_points_all = self._identify_trading_points(df, bi_list, pivot_zones)
        
        print("   识别到 {} 个笔".format(len(bi_list)))
        print("   识别到 {} 个中枢".format(len(pivot_zones)))
        
        return bi_list, pivot_zones, buy_points_all, sell_points_all
    
    def _identify_bi_patterns(self, df, window=5):
        """
        简化的笔识别算法
        """
        bi_list = []
        
        # 寻找局部高点和低点
        highs = []
        lows = []
        
        for i in range(window, len(df) - window):
            # 局部高点
            if all(df['high'].iloc[i] >= df['high'].iloc[i-j] for j in range(1, window+1)) and \
               all(df['high'].iloc[i] >= df['high'].iloc[i+j] for j in range(1, window+1)):
                highs.append((i, df['high'].iloc[i], df['timestamp'].iloc[i]))
            
            # 局部低点
            if all(df['low'].iloc[i] <= df['low'].iloc[i-j] for j in range(1, window+1)) and \
               all(df['low'].iloc[i] <= df['low'].iloc[i+j] for j in range(1, window+1)):
                lows.append((i, df['low'].iloc[i], df['timestamp'].iloc[i]))
        
        # 构建笔序列
        all_points = []
        for idx, price, time in highs:
            all_points.append((idx, price, time, 'high'))
        for idx, price, time in lows:
            all_points.append((idx, price, time, 'low'))
        
        # 按时间排序
        all_points.sort(key=lambda x: x[0])
        
        # 生成笔列表
        for i in range(len(all_points) - 1):
            current = all_points[i]
            next_point = all_points[i + 1]
            
            if current[3] == 'high' and next_point[3] == 'low':
                direction = 'down'
            elif current[3] == 'low' and next_point[3] == 'high':
                direction = 'up'
            else:
                continue
            
            bi_list.append((
                direction,
                current[2],  # start_time
                current[1],  # start_price
                next_point[2],  # end_time
                next_point[1]   # end_price
            ))
        
        return bi_list[-10:]  # 返回最近10个笔
    
    def _identify_pivot_zones(self, df, bi_list):
        """
        简化的中枢识别
        """
        if len(bi_list) < 3:
            return []
        
        pivot_zones = []
        
        # 寻找重叠区间作为中枢
        for i in range(len(bi_list) - 2):
            bi1 = bi_list[i]
            bi2 = bi_list[i + 1]
            bi3 = bi_list[i + 2]
            
            # 获取三段笔的价格范围
            prices = [bi1[2], bi1[4], bi2[2], bi2[4], bi3[2], bi3[4]]
            
            # 计算重叠区间
            zg = min(max(prices), max(prices) * 0.98)  # 中枢上沿
            zd = max(min(prices), min(prices) * 1.02)  # 中枢下沿
            
            if zg > zd:
                pivot_zones.append((
                    bi1[1],  # start_time
                    bi3[3],  # end_time
                    zg,      # 中枢上沿
                    zd,      # 中枢下沿
                    zg * 1.02,  # 中枢上沿扩展
                    zd * 0.98,  # 中枢下沿扩展
                    'auto'   # 类型
                ))
        
        return pivot_zones[-3:]  # 返回最近3个中枢
    
    def _identify_trading_points(self, df, bi_list, pivot_zones):
        """
        智能买卖点识别
        """
        buy_points_all = {'1': ([], []), '2': ([], []), '3': ([], [])}
        sell_points_all = {'1': ([], []), '2': ([], []), '3': ([], [])}
        
        if len(df) < 20:
            return buy_points_all, sell_points_all
        
        current_price = df['close'].iloc[-1]
        
        # 基于价格位置和趋势生成买卖点
        recent_low = df['low'].iloc[-50:].min()
        recent_high = df['high'].iloc[-50:].max()
        
        # 计算相对位置
        price_position = (current_price - recent_low) / (recent_high - recent_low)
        
        # 计算多个周期的移动平均
        ma5 = df['close'].iloc[-5:].mean()
        ma10 = df['close'].iloc[-10:].mean()
        ma20 = df['close'].iloc[-20:].mean()
        
        # 计算价格动量
        momentum = (current_price - df['close'].iloc[-10]) / df['close'].iloc[-10]
        
        # 生成买卖点
        if price_position < 0.2 and current_price > ma5 > ma10 and momentum > 0.02:
            # 低位反转 - 第一类买点
            strength = min(9, int(8 + (0.2 - price_position) * 20))
            buy_points_all['1'][0].append((
                len(df) - 1, current_price, strength, 0, 
                "第一类买点：低位强势反转"
            ))
        
        elif 0.2 <= price_position <= 0.4 and current_price > ma10 > ma20:
            # 低位整理突破 - 第二类买点
            strength = min(8, int(6 + momentum * 100))
            buy_points_all['2'][0].append((
                len(df) - 1, current_price, strength, 0,
                "第二类买点：低位整理后突破"
            ))
        
        elif 0.4 <= price_position <= 0.6 and current_price > ma5:
            # 中位追涨 - 第三类买点
            strength = min(7, int(5 + momentum * 50))
            if strength >= 6:  # 只有强度足够才生成
                buy_points_all['3'][0].append((
                    len(df) - 1, current_price, strength, 0,
                    "第三类买点：中位趋势跟进"
                ))
        
        elif price_position > 0.8 and current_price < ma5 < ma10 and momentum < -0.02:
            # 高位反转 - 第一类卖点
            strength = min(9, int(8 + (price_position - 0.8) * 20))
            sell_points_all['1'][0].append((
                len(df) - 1, current_price, strength, 0,
                "第一类卖点：高位反转信号"
            ))
        
        elif 0.6 <= price_position <= 0.8 and current_price < ma10 < ma20:
            # 高位整理破位 - 第二类卖点
            strength = min(8, int(6 + abs(momentum) * 100))
            sell_points_all['2'][0].append((
                len(df) - 1, current_price, strength, 0,
                "第二类卖点：高位整理后破位"
            ))
        
        return buy_points_all, sell_points_all
    
    def generate_segment_analysis(self, df, bi_list):
        """
        生成线段分析结果
        """
        if len(bi_list) < 2:
            return None
        
        # 分析最近的趋势
        recent_bi = bi_list[-3:] if len(bi_list) >= 3 else bi_list[-2:]
        
        # 计算趋势方向和强度
        up_moves = sum(1 for bi in recent_bi if bi[0] == 'up')
        down_moves = sum(1 for bi in recent_bi if bi[0] == 'down')
        
        if up_moves > down_moves:
            direction = 'up'
        elif down_moves > up_moves:
            direction = 'down'
        else:
            direction = 'sideways'
        
        # 计算置信度
        current_price = df['close'].iloc[-1]
        recent_range = df['high'].iloc[-20:].max() - df['low'].iloc[-20:].min()
        volatility = df['close'].iloc[-20:].std()
        
        # 基于多个因素计算置信度
        range_factor = (recent_range / current_price) * 1000
        volatility_factor = (volatility / current_price) * 1000
        
        confidence = min(95, max(60, int(70 + range_factor + volatility_factor)))
        
        # 判断是否终结
        end_confirmed = confidence > 85
        
        return {
            'direction': direction,
            'confidence': confidence,
            'end_confirmed': end_confirmed
        }
    
    def run_analysis_cycle(self):
        """
        运行一次完整的分析周期
        """
        print("\n" + "="*80)
        print("🔄 开始新的分析周期")
        print("="*80)
        
        # 1. 生成模拟数据
        df = self.generate_realistic_crypto_data()
        
        # 2. 技术分析
        bi_list, pivot_zones, buy_points_all, sell_points_all = self.analyze_technical_patterns(df)
        
        # 3. 生成线段分析
        segment_analysis = self.generate_segment_analysis(df, bi_list)
        
        # 4. 执行交易分析
        if segment_analysis:
            analysis, decisions = self.trading_system.analyze_and_trade(
                df, bi_list, pivot_zones, buy_points_all, sell_points_all, segment_analysis
            )
            
            # 5. 显示分析结果
            print("\n📊 技术分析摘要:")
            print("   当前价格: {:.2f}".format(df['close'].iloc[-1]))
            print("   识别笔数: {}".format(len(bi_list)))
            print("   中枢数量: {}".format(len(pivot_zones)))
            print("   线段方向: {}".format(segment_analysis['direction']))
            print("   置信度: {}%".format(segment_analysis['confidence']))
            
            return True
        else:
            print("❌ 分析数据不足，跳过本次周期")
            return False

def main():
    """
    主函数
    """
    print("🎯 模拟数据交易系统演示")
    print("="*50)
    
    # 选择演示模式
    print("\n🔧 请选择演示模式:")
    print("1. 比特币单次分析")
    print("2. 以太坊单次分析")
    print("3. 比特币多次演示")
    
    try:
        # Python 2/3 兼容性处理
        try:
            user_input = raw_input("\n请输入选择 (1-3，默认1): ")
        except NameError:
            user_input = input("\n请输入选择 (1-3，默认1): ")
        
        choice = str(user_input).strip() or "1"
    except:
        choice = "1"
    
    if choice == "1":
        # 比特币分析
        system = MockDataTradingSystem(symbol="BTC-USD", initial_capital=100000)
        system.run_analysis_cycle()
        system.trading_system.print_performance_report()
    
    elif choice == "2":
        # 以太坊分析
        system = MockDataTradingSystem(symbol="ETH-USD", initial_capital=100000)
        system.run_analysis_cycle()
        system.trading_system.print_performance_report()
    
    elif choice == "3":
        # 多次演示
        system = MockDataTradingSystem(symbol="BTC-USD", initial_capital=50000)
        
        print("\n🚀 开始3次分析演示...")
        for i in range(3):
            print("\n🔔 第{}次分析".format(i+1))
            success = system.run_analysis_cycle()
            if success:
                system.trading_system._print_account_status()
            time.sleep(1)  # 短暂暂停
        
        system.trading_system.print_performance_report()
    
    else:
        print("❌ 无效选择，执行默认分析")
        system = MockDataTradingSystem(symbol="BTC-USD", initial_capital=100000)
        system.run_analysis_cycle()
        system.trading_system.print_performance_report()

if __name__ == "__main__":
    main() 