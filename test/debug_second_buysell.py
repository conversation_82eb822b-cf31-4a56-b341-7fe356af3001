#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试第二类买卖点识别功能
"""
from loguru import logger
from data_fetcher import DataFetcher
from chan_analysis import ChanAnalysis
from chart_plotter import ChartPlotter
import pandas as pd

def debug_second_buysell():
    """
    调试第二类买卖点识别功能
    """
    try:
        # 获取数据
        fetcher = DataFetcher(symbols="BTC/USDT", timeframe="5m")
        df = fetcher.get_klines(limit=200)
        
        # 执行缠论分析
        analyzer = ChanAnalysis()
        analyzer.analyze(df)
        fx_list = analyzer.fx_list
        bi_list = analyzer.bi_list
        xd_list = analyzer.xd_list
        
        # 创建绘图器
        plotter = ChartPlotter()
        
        # 识别中枢区域
        pivot_zones = plotter.identify_pivot_zones(bi_list, df)
        print(f"识别到 {len(pivot_zones)} 个中枢")
        
        # 详细分析每个中枢后的笔序列
        for pivot_idx, (pivot_start_idx, pivot_end_idx, zg, zd, pivot_start_date, pivot_end_date) in enumerate(pivot_zones):
            print(f"\n=== 中枢 #{pivot_idx+1} ===")
            print(f"中枢范围: 上沿={zg:.2f}, 下沿={zd:.2f}")
            print(f"中枢时间: {pivot_start_date} 到 {pivot_end_date}")
            print(f"中枢索引: {pivot_start_idx} 到 {pivot_end_idx}")
            
            # 查找中枢形成后的笔
            post_pivot_bi = [bi for bi in bi_list if bi[0] >= pivot_end_idx]
            print(f"中枢后有 {len(post_pivot_bi)} 个笔")
            
            if len(post_pivot_bi) < 2:
                print("中枢后笔数不足，无法形成第二类买卖点")
                continue
                
            # 分析中枢后的连续两笔
            for i in range(len(post_pivot_bi) - 1):
                first_bi = post_pivot_bi[i]      # 第一笔
                second_bi = post_pivot_bi[i+1]   # 第二笔
                
                print(f"\n  --- 分析笔对 {i+1} ---")
                print(f"  第一笔: 索引{first_bi[0]}-{first_bi[1]}, 方向={first_bi[4]}, 价格{first_bi[2]:.2f}-{first_bi[3]:.2f}")
                print(f"  第二笔: 索引{second_bi[0]}-{second_bi[1]}, 方向={second_bi[4]}, 价格{second_bi[2]:.2f}-{second_bi[3]:.2f}")
                
                first_direction = first_bi[4]
                second_direction = second_bi[4]
                
                # 提取笔的价格
                if first_direction == 'up':
                    first_high, first_low = first_bi[3], first_bi[2]
                else:
                    first_high, first_low = first_bi[2], first_bi[3]
                    
                if second_direction == 'up':
                    second_high, second_low = second_bi[3], second_bi[2]
                else:
                    second_high, second_low = second_bi[2], second_bi[3]
                
                print(f"  第一笔: 高点={first_high:.2f}, 低点={first_low:.2f}")
                print(f"  第二笔: 高点={second_high:.2f}, 低点={second_low:.2f}")
                
                # 检查第二类买点条件：下跌笔回调到中枢但未跌破中枢下沿，然后上涨笔突破中枢上沿
                if first_direction == 'down' and second_direction == 'up':
                    print(f"  检查第二类买点条件:")
                    print(f"    第一笔是否回调到中枢但未跌破下沿: first_low({first_low:.2f}) >= zd({zd:.2f}) = {first_low >= zd}")
                    print(f"    第一笔是否在中枢内: first_high({first_high:.2f}) <= zg({zg:.2f}) = {first_high <= zg}")
                    print(f"    第二笔是否突破中枢上沿: second_high({second_high:.2f}) > zg({zg:.2f}) = {second_high > zg}")
                    
                    if first_low >= zd and first_high <= zg:
                        if second_high > zg:
                            print(f"  ✓ 找到第二类买点！")
                        else:
                            print(f"  ✗ 第二笔未突破中枢上沿")
                    else:
                        print(f"  ✗ 第一笔未满足回调条件")
                
                # 检查第二类卖点条件：上涨笔反弹到中枢但未突破中枢上沿，然后下跌笔跌破中枢下沿
                if first_direction == 'up' and second_direction == 'down':
                    print(f"  检查第二类卖点条件:")
                    print(f"    第一笔是否反弹到中枢但未突破上沿: first_high({first_high:.2f}) <= zg({zg:.2f}) = {first_high <= zg}")
                    print(f"    第一笔是否在中枢内: first_low({first_low:.2f}) >= zd({zd:.2f}) = {first_low >= zd}")
                    print(f"    第二笔是否跌破中枢下沿: second_low({second_low:.2f}) < zd({zd:.2f}) = {second_low < zd}")
                    
                    if first_high <= zg and first_low >= zd:
                        if second_low < zd:
                            print(f"  ✓ 找到第二类卖点！")
                        else:
                            print(f"  ✗ 第二笔未跌破中枢下沿")
                    else:
                        print(f"  ✗ 第一笔未满足反弹条件")
        
        # 使用原始方法验证
        print(f"\n=== 使用原始方法验证 ===")
        buy_points, sell_points = plotter.identify_second_buy_sell_points(bi_list, df, pivot_zones)
        print(f"原始方法识别结果: {len(buy_points)} 个第二类买点, {len(sell_points)} 个第二类卖点")
        
    except Exception as e:
        print(f"调试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_second_buysell() 