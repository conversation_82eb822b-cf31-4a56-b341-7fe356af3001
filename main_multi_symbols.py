#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
主入口文件：启动多级别联立线段终结判断系统
支持同时监控多个交易对
"""

import os
import sys
import argparse
import datetime
import time
from loguru import logger
import pandas as pd

from multi_level_analysis import MultiLevelAnalysis
from notifier import Notifier
from scheduler import Scheduler, SegmentMonitor


class MultiLevelSegmentDetector:
    """
    多级别线段终结判断系统
    基于缠论理论的多级别联立分析，提高判断准确率
    """
    def __init__(self, symbol="BTC/USDT", exchange=None, log_level="INFO"):
        """
        初始化多级别线段终结判断系统
        """
        # 创建必要的目录
        os.makedirs("logs", exist_ok=True)
        os.makedirs("data", exist_ok=True)
        os.makedirs("reports", exist_ok=True)
        os.makedirs("charts", exist_ok=True)
        
        # 设置日志
        logger.remove()  # 移除默认处理器
        logger.add(sys.stderr, level=log_level)  # 添加标准错误输出
        logger.add("logs/multi_level_detector_{time}.log", rotation="100 MB", level="INFO")  # 添加文件输出
        
        logger.info(f"初始化多级别线段终结判断系统，交易对: {symbol}")
        
        # 初始化组件
        self.symbol = symbol
        self.multi_analyzer = MultiLevelAnalysis(symbol=symbol, exchange_id=exchange)
        self.notifier = Notifier()
        self.scheduler = None
        
        # 记录上一次的判断结果
        self.last_judgment = {
            'final_judgment': False,
            'confidence': 0,
            'timestamp': None
        }
        
        logger.info("多级别系统初始化完成")
    
    def run_once(self):
        """
        运行一次多级别线段终结判断
        """
        logger.info(f"开始执行 {self.symbol} 多级别线段终结判断")
        
        try:
            # 执行多级别联立分析
            judgment_result = self.multi_analyzer.multi_level_judgment()
            
            # 获取当前判断状态
            current_judgment = judgment_result['final_judgment']
            current_confidence = judgment_result['confidence']
            
            # 检查状态是否发生变化
            if current_judgment and not self.last_judgment['final_judgment']:
                # 从未终结变为终结，需要发送通知
                message = self._format_notification(judgment_result)
                self.notifier.send(message)
                logger.warning(f"{self.symbol} 多级别线段终结信号，置信度: {current_confidence}%")
                
                # 保存详细分析报告
                self._save_analysis_report(judgment_result)
                
                # 生成多时间周期分析图表
                chart_path = self.multi_analyzer.plot_multi_timeframe_analysis()
                if chart_path:
                    logger.info(f"已生成多时间周期分析图表: {chart_path}")
                
            elif not current_judgment and self.last_judgment['final_judgment']:
                # 从终结变为未终结
                logger.info(f"{self.symbol} 线段终结信号消失，开始新的走势")
            elif current_judgment:
                # 持续终结状态，检查置信度变化
                confidence_change = current_confidence - self.last_judgment['confidence']
                if abs(confidence_change) >= 10:  # 置信度变化超过10%
                    logger.info(f"{self.symbol} 线段终结信号置信度变化: {confidence_change:+.1f}% (当前: {current_confidence}%)")
            
            # 更新上一次状态
            self.last_judgment = {
                'final_judgment': current_judgment,
                'confidence': current_confidence,
                'timestamp': pd.Timestamp.now()
            }
            
            # 输出当前状态摘要
            self._log_status_summary(judgment_result)
            
            return current_judgment
            
        except Exception as e:
            logger.error(f"执行多级别线段终结判断时出错: {str(e)}")
            return False
    
    def _format_notification(self, judgment_result):
        """
        格式化通知消息
        """
        message = f"🚨 {self.symbol} 多级别线段终结信号 🚨\n"
        message += f"时间: {pd.Timestamp.now()}\n"
        message += f"置信度: {judgment_result['confidence']}%\n"
        message += f"判断理由: {judgment_result['reason']}\n"
        message += f"趋势一致性: {'是' if judgment_result['trend_consistency'] else '否'}\n"
        
        # 添加各级别详细信息
        message += "\n📊 各级别分析结果:\n"
        for tf, result in judgment_result['level_results'].items():
            status = "✅ 终结" if result['segment_ended'] else "❌ 未终结"
            trend_emoji = "📈" if result['current_trend'] == 'up' else "📉" if result['current_trend'] == 'down' else "➡️"
            message += f"  {tf}: {status} {trend_emoji} {result['current_trend']} (背驰强度: {result['divergence']['strength']})\n"
        
        # 添加交易建议
        suggestion = self.multi_analyzer.get_trading_suggestion(judgment_result)
        action_emoji = "🔴" if suggestion['action'] == 'sell' else "🟢" if suggestion['action'] == 'buy' else "🟡"
        message += f"\n💡 交易建议: {action_emoji} {suggestion['action'].upper()}\n"
        message += f"风险等级: {suggestion['risk_level']}\n"
        message += f"建议理由: {suggestion['reasoning']}\n"
        
        return message
    
    def _save_analysis_report(self, judgment_result):
        """
        保存详细分析报告到文件
        """
        try:
            timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
            filename = f"reports/analysis_report_{self.symbol.replace('/', '_')}_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"多级别线段终结分析报告\n")
                f.write(f"=" * 50 + "\n")
                f.write(f"交易对: {self.symbol}\n")
                f.write(f"分析时间: {pd.Timestamp.now()}\n")
                f.write(f"最终判断: {'线段终结' if judgment_result['final_judgment'] else '线段未终结'}\n")
                f.write(f"置信度: {judgment_result['confidence']}%\n")
                f.write(f"判断理由: {judgment_result['reason']}\n")
                f.write(f"趋势一致性: {'是' if judgment_result['trend_consistency'] else '否'}\n\n")
                
                f.write("各级别详细分析:\n")
                f.write("-" * 30 + "\n")
                for tf, result in judgment_result['level_results'].items():
                    f.write(f"\n{tf} 级别:\n")
                    f.write(f"  线段终结: {'是' if result['segment_ended'] else '否'}\n")
                    f.write(f"  当前趋势: {result['current_trend']}\n")
                    f.write(f"  最新价格: {result['last_price']}\n")
                    f.write(f"  数据质量: {result['data_quality']} 条K线\n")
                    f.write(f"  背驰信息:\n")
                    for key, value in result['divergence'].items():
                        f.write(f"    {key}: {value}\n")
                    f.write(f"  分析详情: {result['details']}\n")
                
                # 添加交易建议
                suggestion = self.multi_analyzer.get_trading_suggestion(judgment_result)
                f.write(f"\n交易建议:\n")
                f.write("-" * 30 + "\n")
                f.write(f"建议操作: {suggestion['action']}\n")
                f.write(f"风险等级: {suggestion['risk_level']}\n")
                f.write(f"建议理由: {suggestion['reasoning']}\n")
            
            logger.info(f"分析报告已保存: {filename}")
            
        except Exception as e:
            logger.error(f"保存分析报告失败: {str(e)}")
    
    def _log_status_summary(self, judgment_result):
        """
        记录状态摘要
        """
        ended_count = sum(1 for r in judgment_result['level_results'].values() if r['segment_ended'])
        total_count = len(judgment_result['level_results'])
        
        logger.info(f"状态摘要: {ended_count}/{total_count} 个级别显示终结信号, "
                   f"置信度: {judgment_result['confidence']}%, "
                   f"趋势一致: {'是' if judgment_result['trend_consistency'] else '否'}")
    
    def start_scheduler(self, interval=5):
        """
        启动定时调度器
        
        Args:
            interval: 检查间隔，单位为分钟
        """
        logger.info(f"启动多级别定时调度器，间隔: {interval}分钟")
        
        self.scheduler = Scheduler(interval=interval)
        self.scheduler.add_job(self.run_once)
        self.scheduler.start()
    
    def generate_charts(self):
        """
        生成缠论分析图表
        
        Returns:
            dict: 包含各个时间周期图表路径的字典
        """
        logger.info(f"开始为 {self.symbol} 生成缠论分析图表")
        chart_paths = {}
        
        try:
            # 生成多时间周期联合分析图表
            multi_chart_path = self.multi_analyzer.plot_multi_timeframe_analysis()
            if multi_chart_path:
                chart_paths['multi'] = multi_chart_path
                logger.info(f"已生成多时间周期联合分析图表: {multi_chart_path}")
            
            # 获取多时间周期数据
            multi_data = self.multi_analyzer.get_multi_level_data(limit=200)
            
            # 为每个时间周期生成单独的分析图表
            for tf, df in multi_data.items():
                if df is not None and not df.empty:
                    analyzer = self.multi_analyzer.analyzers[tf]
                    analyzer.analyze(df)
                    
                    # 生成图表
                    from chart_plotter import ChartPlotter
                    plotter = ChartPlotter()
                    
                    # 为DataFrame添加交易对和时间周期信息，方便绘图
                    df.attrs['symbol'] = self.symbol
                    df.attrs['timeframe'] = tf
                    
                    chart_path = plotter.plot_chan_analysis(
                        df=df,
                        symbol=self.symbol,
                        timeframe=tf,
                        fx_list=analyzer.fx_list,
                        bi_list=analyzer.bi_list,
                        xd_list=analyzer.xd_list,
                        show_ma=True,
                        show_macd=True
                    )
                    
                    if chart_path:
                        chart_paths[tf] = chart_path
                        logger.info(f"已生成 {tf} 时间周期分析图表: {chart_path}")
            
            return chart_paths
            
        except Exception as e:
            logger.error(f"生成缠论分析图表时出错: {str(e)}")
            return chart_paths


def main():
    """主入口函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="多级别线段终结判断系统")
    parser.add_argument("--symbols", type=str, default="BTC/USDT,ETH/USDT", 
                        help="要监控的交易对，多个交易对用逗号分隔，例如：BTC/USDT,ETH/USDT")
    parser.add_argument("--exchange", type=str, default=None, help="交易所ID")
    parser.add_argument("--interval", type=int, default=5, help="检查间隔，单位为分钟")
    parser.add_argument("--mode", type=str, default="multi", choices=["multi", "simple"], 
                        help="运行模式：multi-多级别联立分析，simple-简单线段终结判断")
    parser.add_argument("--log-level", type=str, default="INFO", 
                        choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
                        help="日志级别")
    parser.add_argument("--generate-charts", action="store_true",
                        help="是否生成缠论分析图表，默认为否")
    parser.add_argument("--plot-only", action="store_true",
                        help="仅生成图表而不启动监控系统，默认为否")
    
    args = parser.parse_args()
    
    # 将交易对字符串转换为列表
    symbols = [s.strip() for s in args.symbols.split(",")]
    
    # 仅生成图表模式
    if args.plot_only:
        for symbol in symbols:
            try:
                logger.info(f"正在为 {symbol} 生成缠论分析图表...")
                detector = MultiLevelSegmentDetector(
                    symbol=symbol, 
                    exchange=args.exchange,
                    log_level=args.log_level
                )
                chart_paths = detector.generate_charts()
                
                if chart_paths:
                    logger.info(f"已为 {symbol} 生成以下图表:")
                    for tf, path in chart_paths.items():
                        logger.info(f"  {tf}: {path}")
                else:
                    logger.warning(f"未能为 {symbol} 生成任何图表")
            except Exception as e:
                logger.error(f"为 {symbol} 生成图表时出错: {str(e)}")
        
        return
    
    # 根据模式选择不同的启动方式
    if args.mode == "multi":
        # 多级别联立分析模式，每个交易对独立运行一个多级别分析器
        for symbol in symbols:
            try:
                detector = MultiLevelSegmentDetector(
                    symbol=symbol, 
                    exchange=args.exchange,
                    log_level=args.log_level
                )
                
                # 如果需要生成图表
                if args.generate_charts:
                    chart_paths = detector.generate_charts()
                    if chart_paths:
                        logger.info(f"已为 {symbol} 生成图表")
                
                detector.start_scheduler(interval=args.interval)
                logger.info(f"已启动 {symbol} 的多级别线段终结判断")
            except Exception as e:
                logger.error(f"启动 {symbol} 的多级别线段终结判断失败: {str(e)}")
    
    else:
        # 简单线段终结判断模式，使用单一级别监控器
        try:
            monitor = SegmentMonitor(symbols=symbols, exchange_id=args.exchange)
            
            # 如果需要生成图表
            if args.generate_charts:
                from chart_plotter import ChartPlotter
                plotter = ChartPlotter()
                
                # 为每个交易对生成5分钟K线图表
                for symbol in symbols:
                    try:
                        # 获取数据
                        klines = monitor.data_fetcher.get_klines(symbol=symbol)
                        if not klines.empty:
                            # 分析K线
                            analyzer = monitor.analyzers[symbol]
                            analyzer.analyze(klines)
                            
                            # 生成图表
                            klines.attrs['symbol'] = symbol
                            klines.attrs['timeframe'] = '5m'
                            
                            chart_path = plotter.plot_chan_analysis(
                                df=klines,
                                symbol=symbol,
                                timeframe='5m',
                                fx_list=analyzer.fx_list,
                                bi_list=analyzer.bi_list,
                                xd_list=analyzer.xd_list
                            )
                            
                            if chart_path:
                                logger.info(f"已为 {symbol} 生成5分钟K线图表: {chart_path}")
                    except Exception as e:
                        logger.error(f"为 {symbol} 生成图表时出错: {str(e)}")
            
            monitor.start_schedule()
            logger.info(f"已启动简单线段终结监控，交易对: {symbols}")
        except Exception as e:
            logger.error(f"启动简单线段终结监控失败: {str(e)}")
    
    try:
        # 保持主线程运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("用户中断，正在退出...")
    except Exception as e:
        logger.error(f"程序运行中发生错误: {str(e)}")


if __name__ == "__main__":
    main() 