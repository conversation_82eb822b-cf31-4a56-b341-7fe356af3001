#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
钉钉机器人配置工具
用于设置钉钉机器人的token和secret，并测试通知功能
"""

import os
import sys
import json
from notifier import Notifier

def setup_dingtalk():
    """设置钉钉机器人并测试"""
    print("=== 钉钉机器人配置工具 ===")
    
    # 查看是否有现有配置
    config_file = "dingtalk_config.json"
    token = "45dc69e51eec9d5b881dc45b3108de35748ad86360e9dccb71ffefdfd21d6e23"
    secret = "SEC5544ae846c78ce005d3cceb620904a1bc652ad59ea6684800c404f35e1c32c4c"
    
    if os.path.exists(config_file):
        try:
            with open(config_file, "r", encoding="utf-8") as f:
                config = json.load(f)
                token = config.get("token", "")
                secret = config.get("secret", "")
            print(f"读取到现有配置: token={token[:4]}***{token[-4:] if len(token) > 8 else ''}")
        except Exception as e:
            print(f"读取配置失败: {e}")
    
    # 输入新配置
    print("\n请输入钉钉机器人配置信息（直接回车保留现有值）:")
    
    # 输入token
    new_token = input(f"访问Token [{token[:4]}***{token[-4:] if len(token) > 8 else ''}]: ")
    if new_token:
        token = new_token
    
    if not token:
        print("错误: 必须提供访问Token")
        return
    
    # 输入secret
    new_secret = input(f"安全密钥 (可选) [{secret[:2]}***{secret[-2:] if len(secret) > 4 else ''}]: ")
    if new_secret:
        secret = new_secret
    
    # 保存配置
    try:
        with open(config_file, "w", encoding="utf-8") as f:
            json.dump({"token": token, "secret": secret}, f, indent=2)
        print(f"配置已保存到 {config_file}")
        
        # 设置环境变量
        os.environ["DINGTALK_TOKEN"] = token
        os.environ["DINGTALK_SECRET"] = secret
        
    except Exception as e:
        print(f"保存配置失败: {e}")
        return
    
    # 测试通知
    test = input("\n是否测试钉钉通知功能? (y/n): ")
    if test.lower() == 'y':
        try:
            notifier = Notifier(["log"])
            notifier.set_dingtalk_config(token, secret)
            
            test_message = "这是一条测试消息，来自交易信号检测系统"
            print(f"发送测试消息: {test_message}")
            
            notifier.send(test_message)
            print("测试消息已发送，请查看您的钉钉")
            
        except Exception as e:
            print(f"测试失败: {e}")
    
    print("\n配置完成！现在您可以运行以下命令接收钉钉通知:")
    print("- 检查ETH买卖点信号: ./run_once_check.sh")
    print("- 持续监控ETH买卖点信号: ./monitor_eth_signals.sh")
    
    # 修改运行脚本，添加钉钉通知配置
    update_scripts = input("\n是否更新运行脚本，使其自动加载钉钉配置? (y/n): ")
    if update_scripts.lower() == 'y':
        try:
            # 更新monitor_eth_signals.sh
            with open("monitor_eth_signals.sh", "r+", encoding="utf-8") as f:
                content = f.read()
                f.seek(0, 0)
                f.write(f"""#!/bin/bash

# 加载钉钉配置
if [ -f "dingtalk_config.json" ]; then
    echo "加载钉钉配置..."
    export DINGTALK_TOKEN=$(cat dingtalk_config.json | python -c "import sys, json; print(json.load(sys.stdin)['token'])")
    export DINGTALK_SECRET=$(cat dingtalk_config.json | python -c "import sys, json; print(json.load(sys.stdin)['secret'])")
fi

# 运行买卖点信号监控，检查ETH/USDT的5分钟、15分钟和1小时周期
python main.py --monitor-signals --symbols ETH/USDT --check-timeframes 5m,15m,1h --monitor-interval 3 --log-level INFO
""")
            
            # 更新run_once_check.sh
            with open("run_once_check.sh", "r+", encoding="utf-8") as f:
                content = f.read()
                f.seek(0, 0)
                f.write(f"""#!/bin/bash

# 加载钉钉配置
if [ -f "dingtalk_config.json" ]; then
    echo "加载钉钉配置..."
    export DINGTALK_TOKEN=$(cat dingtalk_config.json | python -c "import sys, json; print(json.load(sys.stdin)['token'])")
    export DINGTALK_SECRET=$(cat dingtalk_config.json | python -c "import sys, json; print(json.load(sys.stdin)['secret'])")
fi

# 立即检查ETH/USDT当前的买卖点信号
python main.py --check-signals --symbols ETH/USDT --check-timeframes 5m,15m,1h --log-level INFO
""")
            
            print("运行脚本已更新，将自动加载钉钉配置")
            
        except Exception as e:
            print(f"更新脚本失败: {e}")

if __name__ == "__main__":
    setup_dingtalk() 