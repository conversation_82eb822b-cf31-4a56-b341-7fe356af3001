#!/bin/bash

# 加载钉钉配置
if [ -f "dingtalk_config.json" ]; then
    echo "加载钉钉配置..."
    export DINGTALK_TOKEN=$(cat dingtalk_config.json | python -c "import sys, json; print(json.load(sys.stdin)['token'])")
    export DINGTALK_SECRET=$(cat dingtalk_config.json | python -c "import sys, json; print(json.load(sys.stdin)['secret'])")
fi

# 立即检查ETH/USDT当前的买卖点信号
python main.py --check-signals --symbols ETH/USDT --check-timeframes 5m,15m,1h --log-level INFO
