#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
低频率实时监控系统 - 避免API频率限制
每10分钟检查一次，适合长期监控
"""

import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
import time
import warnings
warnings.filterwarnings('ignore')

from trading_system import TradingSystem
from market_analyzer import MarketAnalyzer

class LowFrequencyMonitor:
    """
    低频率监控系统
    """
    
    def __init__(self, symbol="BTC-USD", initial_capital=100000):
        """
        初始化低频率监控系统
        """
        self.symbol = symbol
        self.trading_system = TradingSystem(initial_capital=initial_capital)
        self.analyzer = MarketAnalyzer()
        
        # 数据缓存
        self.df = None
        self.last_update = None
        self.last_analysis = None
        
        # 监控配置
        self.check_interval_minutes = 10  # 10分钟检查一次
        self.cache_duration_minutes = 8   # 8分钟缓存
        
        print("🚀 低频率实时监控系统已初始化")
        print("   交易品种: {}".format(symbol))
        print("   初始资金: {:,.2f}".format(initial_capital))
        print("   检查间隔: {}分钟".format(self.check_interval_minutes))
    
    def fetch_data_with_cache(self):
        """
        带缓存的数据获取
        """
        # 检查缓存
        if self.df is not None and self.last_update is not None:
            time_since_update = (datetime.now() - self.last_update).total_seconds() / 60
            
            if time_since_update < self.cache_duration_minutes:
                print("📋 使用缓存数据 ({:.1f}分钟前更新)".format(time_since_update))
                return self.df
        
        # 获取新数据
        try:
            print("📡 正在获取 {} 的最新数据...".format(self.symbol))
            
            ticker = yf.Ticker(self.symbol)
            
            # 使用较长的时间间隔减少API压力
            if "USD" in self.symbol or "BTC" in self.symbol or "ETH" in self.symbol:
                period = "5d"
                interval = "1h"
            elif self.symbol.endswith(".SZ") or self.symbol.endswith(".SS"):
                period = "5d"
                interval = "30m"
            else:
                period = "5d"
                interval = "30m"
            
            print("   📊 请求参数: period={}, interval={}".format(period, interval))
            
            # 添加延迟避免频率限制
            time.sleep(2)
            
            data = ticker.history(period=period, interval=interval)
            
            if data.empty:
                print("❌ 无法获取数据，使用缓存数据")
                return self.df
            
            # 转换数据格式
            df = pd.DataFrame({
                'timestamp': data.index,
                'open': pd.to_numeric(data['Open'], errors='coerce'),
                'high': pd.to_numeric(data['High'], errors='coerce'),
                'low': pd.to_numeric(data['Low'], errors='coerce'),
                'close': pd.to_numeric(data['Close'], errors='coerce'),
                'volume': pd.to_numeric(data['Volume'], errors='coerce')
            })
            
            # 删除包含NaN的行
            df = df.dropna()
            
            df = df.reset_index(drop=True)
            
            # 验证数据
            if len(df) == 0:
                print("❌ 数据转换后为空，使用缓存数据")
                return self.df
            
            self.df = df
            self.last_update = datetime.now()
            
            print("✅ 数据获取成功")
            print("   数据条数: {}".format(len(df)))
            print("   当前价格: {:.2f}".format(df['close'].iloc[-1]))
            
            return df
            
        except Exception as e:
            print("❌ 数据获取失败: {}".format(str(e)))
            if self.df is not None:
                print("📋 使用上次缓存的数据")
                return self.df
            return None
    
    def analyze_technical_patterns(self, df):
        """
        技术形态分析
        """
        print("🔍 正在分析技术形态...")
        
        # 简化的笔识别
        bi_list = self._identify_bi_patterns(df)
        
        # 简化的中枢识别
        pivot_zones = self._identify_pivot_zones(df, bi_list)
        
        # 买卖点识别
        buy_points_all, sell_points_all = self._identify_trading_points(df, bi_list, pivot_zones)
        
        print("   识别到 {} 个笔".format(len(bi_list)))
        print("   识别到 {} 个中枢".format(len(pivot_zones)))
        
        return bi_list, pivot_zones, buy_points_all, sell_points_all
    
    def _identify_bi_patterns(self, df, window=3):
        """
        笔识别算法（适合低频数据）
        """
        bi_list = []
        
        if len(df) < window * 2:
            return bi_list
        
        # 寻找局部高点和低点
        highs = []
        lows = []
        
        for i in range(window, len(df) - window):
            # 局部高点
            if all(df['high'].iloc[i] >= df['high'].iloc[i-j] for j in range(1, window+1)) and \
               all(df['high'].iloc[i] >= df['high'].iloc[i+j] for j in range(1, window+1)):
                highs.append((i, df['high'].iloc[i], df['timestamp'].iloc[i]))
            
            # 局部低点
            if all(df['low'].iloc[i] <= df['low'].iloc[i-j] for j in range(1, window+1)) and \
               all(df['low'].iloc[i] <= df['low'].iloc[i+j] for j in range(1, window+1)):
                lows.append((i, df['low'].iloc[i], df['timestamp'].iloc[i]))
        
        # 构建笔序列
        all_points = []
        for idx, price, time in highs:
            all_points.append((idx, price, time, 'high'))
        for idx, price, time in lows:
            all_points.append((idx, price, time, 'low'))
        
        all_points.sort(key=lambda x: x[0])
        
        # 生成笔列表
        for i in range(len(all_points) - 1):
            current = all_points[i]
            next_point = all_points[i + 1]
            
            if current[3] == 'high' and next_point[3] == 'low':
                direction = 'down'
            elif current[3] == 'low' and next_point[3] == 'high':
                direction = 'up'
            else:
                continue
            
            bi_list.append((
                direction,
                current[2],  # start_time
                current[1],  # start_price
                next_point[2],  # end_time
                next_point[1]   # end_price
            ))
        
        return bi_list[-8:]  # 返回最近8个笔
    
    def _identify_pivot_zones(self, df, bi_list):
        """
        中枢识别
        """
        if len(bi_list) < 3:
            return []
        
        pivot_zones = []
        
        for i in range(len(bi_list) - 2):
            bi1 = bi_list[i]
            bi2 = bi_list[i + 1]
            bi3 = bi_list[i + 2]
            
            prices = [bi1[2], bi1[4], bi2[2], bi2[4], bi3[2], bi3[4]]
            
            zg = min(max(prices), max(prices) * 0.98)
            zd = max(min(prices), min(prices) * 1.02)
            
            if zg > zd:
                pivot_zones.append((
                    bi1[1],  # start_time
                    bi3[3],  # end_time
                    zg,      # 中枢上沿
                    zd,      # 中枢下沿
                    zg * 1.02,  # 中枢上沿扩展
                    zd * 0.98,  # 中枢下沿扩展
                    'auto'   # 类型
                ))
        
        return pivot_zones[-2:]  # 返回最近2个中枢
    
    def _identify_trading_points(self, df, bi_list, pivot_zones):
        """
        买卖点识别
        """
        buy_points_all = {'1': ([], []), '2': ([], []), '3': ([], [])}
        sell_points_all = {'1': ([], []), '2': ([], []), '3': ([], [])}
        
        if len(df) < 20:
            return buy_points_all, sell_points_all
        
        current_price = df['close'].iloc[-1]
        
        # 基于价格位置和趋势生成买卖点
        recent_low = df['low'].iloc[-30:].min()
        recent_high = df['high'].iloc[-30:].max()
        
        price_position = (current_price - recent_low) / (recent_high - recent_low)
        
        # 移动平均
        ma5 = df['close'].iloc[-5:].mean()
        ma10 = df['close'].iloc[-10:].mean()
        ma20 = df['close'].iloc[-20:].mean()
        
        # 价格动量
        momentum = (current_price - df['close'].iloc[-5]) / df['close'].iloc[-5]
        
        # 生成买卖点
        if price_position < 0.25 and current_price > ma5 > ma10 and momentum > 0.01:
            strength = min(9, int(7 + (0.25 - price_position) * 8))
            buy_points_all['1'][0].append((
                len(df) - 1, current_price, strength, 0, 
                "第一类买点：低位反转信号"
            ))
        
        elif 0.25 <= price_position <= 0.5 and current_price > ma10:
            strength = min(8, int(5 + momentum * 50))
            if strength >= 6:
                buy_points_all['2'][0].append((
                    len(df) - 1, current_price, strength, 0,
                    "第二类买点：中位突破信号"
                ))
        
        elif price_position > 0.75 and current_price < ma5 < ma10 and momentum < -0.01:
            strength = min(9, int(7 + (price_position - 0.75) * 8))
            sell_points_all['1'][0].append((
                len(df) - 1, current_price, strength, 0,
                "第一类卖点：高位反转信号"
            ))
        
        elif 0.5 <= price_position <= 0.75 and current_price < ma10:
            strength = min(8, int(5 + abs(momentum) * 50))
            if strength >= 6:
                sell_points_all['2'][0].append((
                    len(df) - 1, current_price, strength, 0,
                    "第二类卖点：中位破位信号"
                ))
        
        return buy_points_all, sell_points_all
    
    def generate_segment_analysis(self, df, bi_list):
        """
        线段分析
        """
        if len(bi_list) < 2:
            return None
        
        recent_bi = bi_list[-2:]
        
        up_moves = sum(1 for bi in recent_bi if bi[0] == 'up')
        down_moves = sum(1 for bi in recent_bi if bi[0] == 'down')
        
        if up_moves > down_moves:
            direction = 'up'
        elif down_moves > up_moves:
            direction = 'down'
        else:
            direction = 'sideways'
        
        current_price = df['close'].iloc[-1]
        recent_range = df['high'].iloc[-10:].max() - df['low'].iloc[-10:].min()
        volatility = df['close'].iloc[-10:].std()
        
        range_factor = (recent_range / current_price) * 500
        volatility_factor = (volatility / current_price) * 500
        
        confidence = min(95, max(65, int(75 + range_factor + volatility_factor)))
        end_confirmed = confidence > 80
        
        return {
            'direction': direction,
            'confidence': confidence,
            'end_confirmed': end_confirmed
        }
    
    def run_single_check(self):
        """
        执行单次检查
        """
        print("\n" + "="*80)
        print("🔄 执行监控检查 - {}".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        print("="*80)
        
        # 获取数据
        df = self.fetch_data_with_cache()
        if df is None:
            print("❌ 无法获取数据，跳过本次检查")
            return False
        
        # 技术分析
        bi_list, pivot_zones, buy_points_all, sell_points_all = self.analyze_technical_patterns(df)
        segment_analysis = self.generate_segment_analysis(df, bi_list)
        
        # 执行交易分析
        if segment_analysis:
            analysis, decisions = self.trading_system.analyze_and_trade(
                df, bi_list, pivot_zones, buy_points_all, sell_points_all, segment_analysis
            )
            
            # 检查是否有新的重要信号
            has_strong_signal = False
            
            # 检查买卖信号强度
            if analysis.get('buy_signals'):
                max_buy_strength = max(signal['strength'] for signal in analysis['buy_signals'])
                if max_buy_strength >= 8:
                    has_strong_signal = True
                    print("🚨 发现强买入信号！强度: {}/10".format(max_buy_strength))
            
            if analysis.get('sell_signals'):
                max_sell_strength = max(signal['strength'] for signal in analysis['sell_signals'])
                if max_sell_strength >= 8:
                    has_strong_signal = True
                    print("🚨 发现强卖出信号！强度: {}/10".format(max_sell_strength))
            
            # 检查线段信号
            if analysis.get('segment_signals'):
                for signal in analysis['segment_signals']:
                    if signal['strength'] >= 8:
                        has_strong_signal = True
                        print("🚨 发现强线段信号！{}，强度: {}/10".format(
                            signal['type'], signal['strength']
                        ))
            
            # 显示摘要
            print("\n📊 监控摘要:")
            print("   当前价格: {:.2f}".format(df['close'].iloc[-1]))
            print("   线段方向: {}".format(segment_analysis['direction']))
            print("   置信度: {}%".format(segment_analysis['confidence']))
            
            if has_strong_signal:
                print("   🎯 状态: 发现重要信号")
                # 显示详细分析
                self.analyzer.print_analysis_report(analysis)
            else:
                print("   ⏳ 状态: 等待信号")
            
            # 保存本次分析结果
            self.last_analysis = analysis
            
            return True
        else:
            print("❌ 分析数据不足")
            return False
    
    def start_monitoring(self, max_cycles=100):
        """
        开始监控
        """
        print("🎯 开始低频率实时监控")
        print("   监控品种: {}".format(self.symbol))
        print("   检查间隔: {}分钟".format(self.check_interval_minutes))
        print("   最大周期: {}次".format(max_cycles))
        print("   按 Ctrl+C 停止监控")
        
        cycle_count = 0
        
        try:
            while cycle_count < max_cycles:
                cycle_count += 1
                print("\n🔔 第{}次监控检查".format(cycle_count))
                
                # 执行检查
                success = self.run_single_check()
                
                if success:
                    # 显示账户状态
                    self.trading_system._print_account_status()
                
                # 等待下一个周期
                if cycle_count < max_cycles:
                    print("\n⏰ 等待{}分钟后进行下一次检查...".format(self.check_interval_minutes))
                    time.sleep(self.check_interval_minutes * 60)
                
        except KeyboardInterrupt:
            print("\n\n⏹️ 用户中断，停止监控")
        
        # 显示最终报告
        print("\n🏁 监控结束")
        self.trading_system.print_performance_report()

def main():
    """
    主函数
    """
    print("🎯 低频率实时监控系统")
    print("="*50)
    
    # 选择交易品种
    print("\n📈 请选择监控品种:")
    print("1. 比特币 (BTC-USD)")
    print("2. 以太坊 (ETH-USD)")
    print("3. 苹果股票 (AAPL)")
    print("4. 特斯拉 (TSLA)")
    print("5. 自定义代码")
    
    try:
        try:
            user_input = raw_input("\n请输入选择 (1-5，默认1): ")
        except NameError:
            user_input = input("\n请输入选择 (1-5，默认1): ")
        
        choice = str(user_input).strip() or "1"
    except:
        choice = "1"
    
    if choice == "1":
        symbol = "BTC-USD"
        print("✅ 已选择: 比特币 (BTC-USD)")
    elif choice == "2":
        symbol = "ETH-USD"
        print("✅ 已选择: 以太坊 (ETH-USD)")
    elif choice == "3":
        symbol = "AAPL"
        print("✅ 已选择: 苹果 (AAPL)")
    elif choice == "4":
        symbol = "TSLA"
        print("✅ 已选择: 特斯拉 (TSLA)")
    elif choice == "5":
        try:
            try:
                symbol = raw_input("请输入交易代码: ").strip().upper()
            except NameError:
                symbol = input("请输入交易代码: ").strip().upper()
        except:
            symbol = "BTC-USD"
        print("✅ 已选择: {}".format(symbol))
    else:
        symbol = "BTC-USD"
        print("✅ 使用默认: 比特币 (BTC-USD)")
    
    # 设置初始资金
    try:
        try:
            capital_input = raw_input("\n💰 请输入初始资金 (默认100000): ")
        except NameError:
            capital_input = input("\n💰 请输入初始资金 (默认100000): ")
        
        capital = float(capital_input or "100000")
    except:
        capital = 100000
    
    # 创建监控系统
    monitor = LowFrequencyMonitor(symbol=symbol, initial_capital=capital)
    
    # 选择运行模式
    print("\n🔧 请选择运行模式:")
    print("1. 单次检查")
    print("2. 持续监控 (每10分钟检查一次)")
    print("3. 快速测试 (每2分钟检查一次，共5次)")
    
    try:
        try:
            mode_input = raw_input("\n请输入选择 (1-3，默认2): ")
        except NameError:
            mode_input = input("\n请输入选择 (1-3，默认2): ")
        
        mode = str(mode_input).strip() or "2"
    except:
        mode = "2"
    
    if mode == "1":
        # 单次检查
        monitor.run_single_check()
        monitor.trading_system.print_performance_report()
    
    elif mode == "2":
        # 持续监控
        monitor.start_monitoring(max_cycles=144)  # 24小时监控
    
    elif mode == "3":
        # 快速测试
        print("\n🚀 开始快速测试...")
        monitor.check_interval_minutes = 2  # 改为2分钟间隔
        monitor.cache_duration_minutes = 1  # 1分钟缓存
        monitor.start_monitoring(max_cycles=5)
    
    else:
        print("❌ 无效选择，执行持续监控")
        monitor.start_monitoring(max_cycles=144)

if __name__ == "__main__":
    main() 