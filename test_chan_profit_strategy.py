#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
缠论止盈策略测试脚本
演示如何使用基于缠论理论的止盈止损策略
"""

import pandas as pd
import numpy as np
from loguru import logger
from chan_analysis import ChanAnalysis
from multi_level_analysis import MultiLevelAnalysis

def test_chan_profit_strategy():
    """测试缠论止盈策略"""
    
    print("="*80)
    print("缠论止盈策略测试")
    print("="*80)
    
    # 模拟K线数据
    dates = pd.date_range('2024-01-01', periods=100, freq='1H')
    np.random.seed(42)
    
    # 生成模拟价格数据（包含趋势和中枢）
    base_price = 100
    prices = []
    
    for i in range(100):
        if i < 30:
            # 下跌趋势
            price = base_price - i * 0.5 + np.random.normal(0, 0.5)
        elif i < 60:
            # 中枢震荡
            center = base_price - 15
            price = center + np.sin((i-30) * 0.3) * 3 + np.random.normal(0, 0.3)
        else:
            # 上涨趋势
            price = (base_price - 15) + (i-60) * 0.8 + np.random.normal(0, 0.4)
        prices.append(max(price, 1))  # 确保价格为正
    
    # 构建DataFrame
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p + np.random.uniform(0, 1) for p in prices],
        'low': [p - np.random.uniform(0, 1) for p in prices],
        'close': prices,
        'volume': np.random.uniform(1000, 5000, 100)
    })
    
    # 确保OHLC数据的逻辑性
    for i in range(len(df)):
        high = max(df.iloc[i]['open'], df.iloc[i]['close']) + abs(df.iloc[i]['high'] - df.iloc[i]['close'])
        low = min(df.iloc[i]['open'], df.iloc[i]['close']) - abs(df.iloc[i]['low'] - df.iloc[i]['close'])
        df.iloc[i, df.columns.get_loc('high')] = high
        df.iloc[i, df.columns.get_loc('low')] = low
    
    print(f"生成了 {len(df)} 条K线数据")
    print(f"价格范围: {df['low'].min():.2f} - {df['high'].max():.2f}")
    
    # 创建缠论分析器
    chan_analyzer = ChanAnalysis(df)
    
    # 模拟一些中枢数据
    chan_analyzer.pivot_zones = [
        (25, 55, 88, 82),  # 中枢：开始索引，结束索引，上沿，下沿
        (70, 85, 95, 90)   # 另一个中枢
    ]
    
    print("\n" + "="*60)
    print("测试不同类型买点的止盈策略")
    print("="*60)
    
    # 测试第一类买点
    print("\n【第一类买点测试】")
    entry_point_1 = {'index': 30, 'price': 85.0, 'timestamp': '2024-01-01 06:00:00'}
    
    targets_1 = chan_analyzer.calculate_chan_profit_targets(
        entry_point=entry_point_1,
        signal_type='buy',
        buy_sell_point_type=1,
        related_pivot_idx=0,
        current_level='1h'
    )
    
    print_strategy_details("第一类买点（趋势转折）", entry_point_1, targets_1)
    
    # 测试第二类买点
    print("\n【第二类买点测试】")
    entry_point_2 = {'index': 58, 'price': 83.0, 'timestamp': '2024-01-02 10:00:00'}
    
    targets_2 = chan_analyzer.calculate_chan_profit_targets(
        entry_point=entry_point_2,
        signal_type='buy',
        buy_sell_point_type=2,
        related_pivot_idx=0,
        current_level='1h'
    )
    
    print_strategy_details("第二类买点（中枢突破回调）", entry_point_2, targets_2)
    
    # 测试第三类买点
    print("\n【第三类买点测试】")
    entry_point_3 = {'index': 45, 'price': 85.5, 'timestamp': '2024-01-01 21:00:00'}
    
    targets_3 = chan_analyzer.calculate_chan_profit_targets(
        entry_point=entry_point_3,
        signal_type='buy',
        buy_sell_point_type=3,
        related_pivot_idx=0,
        current_level='1h'
    )
    
    print_strategy_details("第三类买点（中枢震荡）", entry_point_3, targets_3)
    
    # 策略对比分析
    print("\n" + "="*60)
    print("策略对比分析")
    print("="*60)
    
    strategies = [
        ("第一类买点", entry_point_1, targets_1),
        ("第二类买点", entry_point_2, targets_2),
        ("第三类买点", entry_point_3, targets_3)
    ]
    
    print(f"{'策略类型':<15} {'入场价':<8} {'止损':<8} {'风险%':<8} {'预期收益%':<12} {'风险收益比':<10}")
    print("-" * 70)
    
    for strategy_name, entry, targets in strategies:
        entry_price = entry['price']
        stop_loss = targets.get('stop_loss', 0)
        profit_targets = targets.get('profit_targets', [])
        
        risk_pct = (entry_price - stop_loss) / entry_price * 100 if stop_loss else 0
        
        if profit_targets:
            avg_return = np.mean([t.get('expected_return', 0) for t in profit_targets])
        else:
            avg_return = 0
        
        risk_reward = targets.get('risk_reward_ratio', 0) or 0
        
        print(f"{strategy_name:<15} {entry_price:<8.2f} {stop_loss:<8.2f} {risk_pct:<8.1f} {avg_return:<12.1f} {risk_reward:<10.2f}")
    
    print("\n" + "="*60)
    print("缠论止盈策略核心要点")
    print("="*60)
    
    print("""
1. 【第一类买点】- 风险最小，收益最大
   • 止损：前期重要低点下方，给趋势发展充分空间
   • 止盈：分级止盈，从阻力位到趋势延续目标
   • 仓位：初始30%，可逐步加仓至80%
   • 适合：中长线投资者，追求大级别趋势

2. 【第二类买点】- 风险中等，收益稳定
   • 止损：中枢下沿下方，风险可控
   • 止盈：以确认突破为首要目标，然后追求测量目标
   • 仓位：初始50%，最大70%
   • 适合：稳健型投资者，平衡风险收益

3. 【第三类买点】- 风险较高，收益有限
   • 止损：中枢下沿下方0.5%，止损很紧
   • 止盈：快进快出，以中枢上沿为主要目标
   • 仓位：初始30%，最大50%
   • 适合：短线交易者，快速获利

4. 【关键改进点】
   • 不再使用固定百分比，而是基于市场结构
   • 结合中枢理论，止损止盈更有逻辑依据
   • 分级止盈，逐步锁定利润
   • 动态仓位管理，根据信号强度调整
   • 考虑多级别共振，提高成功率

5. 【执行建议】
   • 严格按照缠论买卖点分类执行对应策略
   • 第一类买点可以重仓，其他买点控制仓位
   • 止损必须严格执行，保护本金是第一要务
   • 分批止盈，不要试图卖在最高点
   • 结合多个时间周期确认信号
    """)

def print_strategy_details(strategy_name, entry_point, targets):
    """打印策略详情"""
    print(f"\n策略类型: {strategy_name}")
    print(f"入场时间: {entry_point['timestamp']}")
    print(f"入场价格: {entry_point['price']:.2f}")
    print(f"止损价格: {targets.get('stop_loss', 'N/A')}")
    
    profit_targets = targets.get('profit_targets', [])
    if profit_targets:
        print("止盈目标:")
        for i, target in enumerate(profit_targets, 1):
            print(f"  目标{i}: {target['price']:.2f} (减仓{target['percentage']*100:.0f}%) - {target['description']}")
            print(f"         预期收益: {target['expected_return']:.1f}%")
    
    position_mgmt = targets.get('position_management', {})
    if position_mgmt:
        print(f"仓位管理:")
        print(f"  初始仓位: {position_mgmt.get('initial_position', 0)*100:.0f}%")
        print(f"  最大仓位: {position_mgmt.get('max_position', 0)*100:.0f}%")
    
    risk_reward = targets.get('risk_reward_ratio')
    if risk_reward:
        print(f"风险收益比: 1:{risk_reward:.2f}")
    
    strategy_desc = targets.get('strategy_description', '')
    if strategy_desc:
        print(f"策略说明: {strategy_desc.strip()}")

def compare_with_traditional_method():
    """对比传统方法和缠论方法"""
    print("\n" + "="*60)
    print("传统方法 vs 缠论方法对比")
    print("="*60)
    
    entry_price = 100.0
    
    print("传统固定百分比方法:")
    print(f"  止损: {entry_price * 0.95:.2f} (-5%)")
    print(f"  止盈1: {entry_price * 1.05:.2f} (+5%)")
    print(f"  止盈2: {entry_price * 1.10:.2f} (+10%)")
    print(f"  止盈3: {entry_price * 1.15:.2f} (+15%)")
    print("  问题: 不考虑市场结构，机械化操作")
    
    print("\n缠论结构化方法:")
    print("  止损: 基于重要支撑位/中枢边界")
    print("  止盈1: 前期阻力位/中枢上沿")
    print("  止盈2: 更高级别目标/测量目标")
    print("  止盈3: 趋势延续目标")
    print("  优势: 基于市场结构，逻辑性强，适应性好")

if __name__ == "__main__":
    test_chan_profit_strategy()
    compare_with_traditional_method() 