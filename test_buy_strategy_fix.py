#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from chan_analysis import ChanAnalysis

def generate_test_data():
    """生成测试数据"""
    np.random.seed(42)
    length = 100
    base_price = 2600
    
    start_time = datetime.now() - timedelta(hours=length)
    timestamps = [start_time + timedelta(hours=i) for i in range(length)]
    
    prices = []
    current_price = base_price
    
    for i in range(length):
        if i < 30:
            change = np.random.normal(-0.3, 1.0)  # 下跌趋势
        elif i < 60:
            change = np.random.normal(0, 0.8)     # 震荡整理
        else:
            change = np.random.normal(0.5, 1.2)   # 上涨趋势
        
        current_price *= (1 + change / 100)
        prices.append(current_price)
    
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        open_price = prices[i-1] if i > 0 else close
        high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.5)) / 100)
        low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.5)) / 100)
        volume = np.random.randint(1000, 10000)
        
        data.append({
            'timestamp': timestamp,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def test_buy_strategies_complete_targets():
    """测试买点策略是否有完整的三个止盈目标"""
    print("="*80)
    print("买点策略完整止盈目标测试")
    print("="*80)
    
    df = generate_test_data()
    print(f"生成了 {len(df)} 条K线数据")
    print(f"价格范围: {df['low'].min():.2f} - {df['high'].max():.2f}")
    
    chan_analyzer = ChanAnalysis(df)
    chan_analyzer.pivot_zones = [
        (20, 40, 2650, 2600),  # 第一个中枢
        (50, 70, 2680, 2620),  # 第二个中枢
        (75, 90, 2700, 2650)   # 第三个中枢
    ]
    
    # 测试三种类型的买点
    test_scenarios = [
        {
            'name': '第一类买点',
            'entry_point': {'index': 35, 'price': 2616.0, 'timestamp': '2024-01-01 11:00:00'},
            'point_type': 1,
            'pivot_idx': 0,
            'level': '1h',
            'description': '趋势转折点，下跌趋势结束'
        },
        {
            'name': '第二类买点',
            'entry_point': {'index': 55, 'price': 2625.0, 'timestamp': '2024-01-01 15:00:00'},
            'point_type': 2,
            'pivot_idx': 1,
            'level': '30m',
            'description': '中枢突破后的回调买点'
        },
        {
            'name': '第三类买点',
            'entry_point': {'index': 80, 'price': 2655.0, 'timestamp': '2024-01-01 20:00:00'},
            'point_type': 3,
            'pivot_idx': 2,
            'level': '15m',
            'description': '中枢震荡中的买点'
        }
    ]
    
    print("\n" + "="*80)
    print("详细策略分析")
    print("="*80)
    
    all_results = []
    
    for scenario in test_scenarios:
        print(f"\n【{scenario['name']}测试】")
        print(f"场景描述: {scenario['description']}")
        print(f"入场价格: {scenario['entry_point']['price']}")
        print(f"分析级别: {scenario['level']}")
        
        targets = chan_analyzer.calculate_chan_profit_targets(
            entry_point=scenario['entry_point'],
            signal_type='buy',
            buy_sell_point_type=scenario['point_type'],
            related_pivot_idx=scenario['pivot_idx'],
            current_level=scenario['level']
        )
        
        # 分析结果
        entry_price = scenario['entry_point']['price']
        stop_loss = targets.get('stop_loss')
        profit_targets = targets.get('profit_targets', [])
        
        print(f"策略描述: {targets.get('strategy_description', 'N/A').strip()}")
        
        if stop_loss:
            stop_loss_pct = (stop_loss / entry_price - 1) * 100
            print(f"止损位: {stop_loss:.2f} ({stop_loss_pct:+.1f}%)")
        
        print(f"止盈目标数量: {len(profit_targets)}")
        
        # 检查是否有完整的三个止盈目标
        has_three_targets = len(profit_targets) >= 3
        print(f"是否有三个止盈目标: {'✅ 是' if has_three_targets else '❌ 否'}")
        
        total_position = 0
        for i, target in enumerate(profit_targets, 1):
            pct_return = (target['price'] / entry_price - 1) * 100
            total_position += target['percentage']
            print(f"  目标{i}: {target['price']:.2f} ({pct_return:+.1f}%) - {target['description']} - 减仓{target['percentage']*100:.0f}%")
        
        print(f"总仓位分配: {total_position*100:.0f}%")
        
        # 仓位管理
        position_mgmt = targets.get('position_management', {})
        if position_mgmt:
            initial_pos = position_mgmt.get('initial_position', 0) * 100
            max_pos = position_mgmt.get('max_position', 0) * 100
            print(f"仓位管理: 初始{initial_pos:.0f}%, 最大{max_pos:.0f}%")
        
        # 风险收益比
        risk_reward = targets.get('risk_reward_ratio')
        if risk_reward:
            print(f"风险收益比: 1:{risk_reward:.2f}")
        
        # 保存结果用于对比
        all_results.append({
            'name': scenario['name'],
            'entry_price': entry_price,
            'stop_loss': stop_loss,
            'profit_targets': profit_targets,
            'has_three_targets': has_three_targets,
            'risk_reward': risk_reward,
            'position_mgmt': position_mgmt
        })
        
        print("-" * 60)
    
    # 策略对比表格
    print("\n" + "="*80)
    print("策略对比总结")
    print("="*80)
    
    print(f"{'策略类型':<12} {'止损%':<8} {'止盈1%':<8} {'止盈2%':<8} {'止盈3%':<8} {'三目标':<8} {'风险收益比':<10}")
    print("-" * 80)
    
    for result in all_results:
        entry_price = result['entry_price']
        stop_loss_pct = ((result['stop_loss'] / entry_price - 1) * 100) if result['stop_loss'] else 0
        
        profit_targets = result['profit_targets']
        tp1_pct = ((profit_targets[0]['price'] / entry_price - 1) * 100) if len(profit_targets) > 0 else 0
        tp2_pct = ((profit_targets[1]['price'] / entry_price - 1) * 100) if len(profit_targets) > 1 else 0
        tp3_pct = ((profit_targets[2]['price'] / entry_price - 1) * 100) if len(profit_targets) > 2 else 0
        
        risk_reward = result['risk_reward'] or 0
        has_three = "✅" if result['has_three_targets'] else "❌"
        
        print(f"{result['name']:<12} {stop_loss_pct:>+6.1f} {tp1_pct:>+6.1f} {tp2_pct:>+6.1f} {tp3_pct:>+6.1f} {has_three:>6} {risk_reward:>8.2f}")
    
    # 修复效果分析
    print("\n" + "="*80)
    print("修复效果分析")
    print("="*80)
    
    all_have_three = all(result['has_three_targets'] for result in all_results)
    
    if all_have_three:
        print("✅ 修复成功!")
        print("1. 所有买点类型现在都有完整的3个止盈目标")
        print("2. 每个策略都有合理的仓位分配")
        print("3. 止盈目标基于缠论理论设计")
        print("4. 包含默认目标确保完整性")
    else:
        print("❌ 仍有问题!")
        for result in all_results:
            if not result['has_three_targets']:
                print(f"- {result['name']} 仍然缺少完整的止盈目标")
    
    print("\n📊 策略特点:")
    print("- 第一类买点: 趋势转折，分级建仓，风险收益比最优")
    print("- 第二类买点: 中枢突破回调，稳健操作")
    print("- 第三类买点: 中枢震荡，快进快出")
    
    return all_results

if __name__ == "__main__":
    try:
        results = test_buy_strategies_complete_targets()
        print("\n✅ 买点策略完整性测试完成")
        
        # 检查是否所有策略都有三个止盈目标
        all_complete = all(result['has_three_targets'] for result in results)
        if all_complete:
            print("🎉 所有买点策略现在都有完整的三个止盈目标！")
        else:
            print("⚠️  仍有买点策略缺少完整的止盈目标")
            
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
