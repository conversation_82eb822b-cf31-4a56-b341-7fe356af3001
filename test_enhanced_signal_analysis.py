#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强信号分析测试

测试新的信号可信度分析功能，确保能够提供详细的判断理由
"""

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def test_enhanced_signal_analysis():
    """测试增强的信号分析功能"""
    print("="*80)
    print("增强信号分析功能测试")
    print("="*80)
    
    # 模拟信号数据
    test_signals = [
        {
            'name': '优质第二类买点',
            'signal': {
                'type': '第二类买点',
                'index': 95,
                'price': 2604.62,
                'strength': 10,
                'bar_position': 16,
                'description': '第二类买点 - 下跌中枢完成后的低点回调买入信号'
            },
            'market_context': {
                'current_price': 2620.0,
                'trend': 'up',
                'volatility': 2.5,
                'volume_ratio': 1.3,
                'pivot_relationship': 'below_pivot'
            }
        },
        {
            'name': '可疑第二类卖点',
            'signal': {
                'type': '第二类卖点',
                'index': 82,
                'price': 2615.97,
                'strength': 8,
                'bar_position': 18,
                'description': '第二类卖点 - 上涨中枢完成后的高点回调卖出信号'
            },
            'market_context': {
                'current_price': 2620.0,
                'trend': 'unclear',
                'volatility': 4.2,
                'volume_ratio': 0.7,
                'pivot_relationship': 'far_from_pivot'
            }
        },
        {
            'name': '强势第一类买点',
            'signal': {
                'type': '第一类买点',
                'index': 98,
                'price': 2618.5,
                'strength': 9,
                'bar_position': 2,
                'description': '第一类买点 - 向下笔的底分型确认，下跌趋势结束信号'
            },
            'market_context': {
                'current_price': 2620.0,
                'trend': 'reversal',
                'volatility': 1.8,
                'volume_ratio': 2.1,
                'pivot_relationship': 'near_pivot'
            }
        }
    ]
    
    print("测试场景:")
    for i, scenario in enumerate(test_signals, 1):
        print(f"\n{i}. {scenario['name']}")
        signal = scenario['signal']
        context = scenario['market_context']
        
        print(f"   信号类型: {signal['type']}")
        print(f"   信号强度: {signal['strength']}/10")
        print(f"   信号位置: {signal['bar_position']}根K线前")
        print(f"   信号价格: {signal['price']}")
        print(f"   当前价格: {context['current_price']}")
        print(f"   市场趋势: {context['trend']}")
        print(f"   波动率: {context['volatility']}%")
        print(f"   成交量比: {context['volume_ratio']}")
        print(f"   中枢关系: {context['pivot_relationship']}")
    
    # 模拟增强分析结果
    print("\n" + "="*80)
    print("增强分析结果")
    print("="*80)
    
    enhanced_results = []
    
    for i, scenario in enumerate(test_signals, 1):
        print(f"\n场景 {i}: {scenario['name']}")
        print("-" * 60)
        
        signal = scenario['signal']
        context = scenario['market_context']
        
        # 模拟可信度分析
        credibility_analysis = simulate_credibility_analysis(signal, context)
        
        print("📊 详细分析结果:")
        print(f"   可信度评分: {credibility_analysis['score']:.1f}/10")
        print(f"   置信度等级: {credibility_analysis['confidence_level']}")
        
        print("\n🔍 分析维度:")
        for dimension, analysis in credibility_analysis['dimensions'].items():
            print(f"   {dimension}: {analysis['quality']} - {analysis['description']}")
        
        print(f"\n📝 详细判断理由:")
        print(f"   {credibility_analysis['detailed_reason']}")
        
        if credibility_analysis['risk_warnings']:
            print(f"\n⚠️ 风险提示:")
            for warning in credibility_analysis['risk_warnings']:
                print(f"   - {warning}")
        
        print(f"\n💡 操作建议:")
        print(f"   {credibility_analysis['recommendation']}")
        
        enhanced_results.append({
            'scenario': scenario['name'],
            'credibility_score': credibility_analysis['score'],
            'confidence_level': credibility_analysis['confidence_level'],
            'recommendation': credibility_analysis['recommendation']
        })
    
    # 对比分析
    print("\n" + "="*80)
    print("信号对比分析")
    print("="*80)
    
    print("信号可信度排名:")
    sorted_results = sorted(enhanced_results, key=lambda x: x['credibility_score'], reverse=True)
    
    for i, result in enumerate(sorted_results, 1):
        print(f"{i}. {result['scenario']}")
        print(f"   可信度: {result['credibility_score']:.1f}/10 ({result['confidence_level']})")
        print(f"   建议: {result['recommendation']}")
        print()
    
    # 系统优势展示
    print("="*80)
    print("增强分析系统优势")
    print("="*80)
    
    print("🔴 传统信号输出的问题:")
    print("  ❌ 只显示信号类型和强度")
    print("  ❌ 缺乏详细的判断依据")
    print("  ❌ 无法评估信号可信度")
    print("  ❌ 难以识别虚假信号")
    print("  ❌ 用户难以做出决策")
    
    print("\n🟢 增强分析系统的优势:")
    print("  ✅ 多维度可信度分析")
    print("  ✅ 详细的判断理由说明")
    print("  ✅ 量化的可信度评分")
    print("  ✅ 明确的风险提示")
    print("  ✅ 具体的操作建议")
    
    print("\n📊 分析维度:")
    print("  1. 技术形态分析 (20%权重)")
    print("  2. 中枢关系分析 (25%权重)")
    print("  3. 笔结构分析 (20%权重)")
    print("  4. 价格位置分析 (15%权重)")
    print("  5. 时效性分析 (15%权重)")
    print("  6. 成交量确认 (5%权重)")
    
    print("\n🎯 实际价值:")
    print("  💡 帮助用户理解信号形成原因")
    print("  🛡️ 识别和过滤低质量信号")
    print("  📈 提高交易决策的准确性")
    print("  🎓 增强用户对缠论的理解")
    print("  ⚡ 减少因信号不明而错失机会")
    
    return True

def simulate_credibility_analysis(signal, context):
    """模拟可信度分析"""
    
    # 根据信号和市场环境计算各维度评分
    dimensions = {}
    
    # 技术形态分析
    if context['trend'] == 'reversal' and context['volatility'] < 3:
        tech_quality = 'good'
        tech_desc = f"技术形态良好，{context['trend']}趋势，波动率{context['volatility']}%"
    elif context['trend'] == 'up' and context['volatility'] < 4:
        tech_quality = 'fair'
        tech_desc = f"技术形态一般，{context['trend']}趋势"
    else:
        tech_quality = 'poor'
        tech_desc = f"技术形态不明确，趋势{context['trend']}"
    
    dimensions['技术形态'] = {'quality': tech_quality, 'description': tech_desc}
    
    # 中枢关系分析
    if context['pivot_relationship'] == 'near_pivot':
        pivot_quality = 'excellent'
        pivot_desc = "与中枢关系优秀，位置理想"
    elif context['pivot_relationship'] == 'below_pivot':
        pivot_quality = 'good'
        pivot_desc = "位于中枢下方，符合买点逻辑"
    else:
        pivot_quality = 'poor'
        pivot_desc = "与中枢关系不明确"
    
    dimensions['中枢关系'] = {'quality': pivot_quality, 'description': pivot_desc}
    
    # 价格位置分析
    price_diff = abs(context['current_price'] - signal['price']) / signal['price'] * 100
    if price_diff < 1:
        price_quality = 'excellent'
        price_desc = f"当前价格与信号价格非常接近(偏差{price_diff:.1f}%)"
    elif price_diff < 2:
        price_quality = 'good'
        price_desc = f"当前价格接近信号价格(偏差{price_diff:.1f}%)"
    else:
        price_quality = 'fair'
        price_desc = f"当前价格偏离信号价格(偏差{price_diff:.1f}%)"
    
    dimensions['价格位置'] = {'quality': price_quality, 'description': price_desc}
    
    # 时效性分析
    bar_position = signal['bar_position']
    if bar_position <= 5:
        timing_quality = 'good'
        timing_desc = f"信号较新鲜({bar_position}根K线前)"
    elif bar_position <= 15:
        timing_quality = 'fair'
        timing_desc = f"信号时效性一般({bar_position}根K线前)"
    else:
        timing_quality = 'poor'
        timing_desc = f"信号已过时({bar_position}根K线前)"
    
    dimensions['时效性'] = {'quality': timing_quality, 'description': timing_desc}
    
    # 成交量分析
    volume_ratio = context['volume_ratio']
    if volume_ratio > 1.5:
        volume_quality = 'excellent'
        volume_desc = f"成交量强力确认(是平均量的{volume_ratio}倍)"
    elif volume_ratio > 1.0:
        volume_quality = 'good'
        volume_desc = f"成交量适度确认(是平均量的{volume_ratio}倍)"
    else:
        volume_quality = 'poor'
        volume_desc = f"成交量偏弱(仅为平均量的{volume_ratio}倍)"
    
    dimensions['成交量'] = {'quality': volume_quality, 'description': volume_desc}
    
    # 计算综合评分
    quality_scores = {
        'excellent': 2.0, 'good': 1.5, 'fair': 1.0, 'poor': 0.5
    }
    
    weights = {
        '技术形态': 0.2, '中枢关系': 0.25, '价格位置': 0.15,
        '时效性': 0.15, '成交量': 0.05
    }
    
    total_score = 0
    for dim, weight in weights.items():
        if dim in dimensions:
            quality = dimensions[dim]['quality']
            score = quality_scores.get(quality, 0.5)
            total_score += score * weight * 10  # 转换为10分制
    
    # 笔结构分析（简化）
    total_score += 1.5  # 假设笔结构良好
    
    # 置信度等级
    if total_score >= 8.5:
        confidence_level = "极高"
    elif total_score >= 7.0:
        confidence_level = "高"
    elif total_score >= 5.5:
        confidence_level = "中等"
    elif total_score >= 3.5:
        confidence_level = "较低"
    else:
        confidence_level = "低"
    
    # 详细理由
    reason_parts = [f"信号类型: {signal['type']}"]
    for dim, analysis in dimensions.items():
        reason_parts.append(f"{dim}: {analysis['description']}")
    reason_parts.append(f"综合可信度: {total_score:.1f}/10 ({confidence_level})")
    
    detailed_reason = " | ".join(reason_parts)
    
    # 风险提示
    risk_warnings = []
    if timing_quality == 'poor':
        risk_warnings.append("信号已过时，入场风险较高")
    if volume_quality == 'poor':
        risk_warnings.append("成交量不足，信号确认度低")
    if price_diff > 2:
        risk_warnings.append(f"价格偏离{price_diff:.1f}%，存在滑点风险")
    
    # 操作建议
    if total_score >= 7.5:
        recommendation = "强烈推荐，可重仓操作"
    elif total_score >= 6.0:
        recommendation = "推荐操作，建议中等仓位"
    elif total_score >= 4.5:
        recommendation = "谨慎操作，建议小仓位试探"
    else:
        recommendation = "不建议操作，等待更好机会"
    
    return {
        'score': total_score,
        'confidence_level': confidence_level,
        'dimensions': dimensions,
        'detailed_reason': detailed_reason,
        'risk_warnings': risk_warnings,
        'recommendation': recommendation
    }

if __name__ == "__main__":
    try:
        success = test_enhanced_signal_analysis()
        if success:
            print("\n🎉 增强信号分析功能测试完成！")
            print("💡 现在用户可以获得详细的信号判断理由")
        else:
            print("\n⚠️ 增强信号分析功能测试存在问题")
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
