#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据获取模块：负责从交易所获取多个交易对的5分钟K线数据
"""

import time
import ccxt
import pandas as pd
from loguru import logger

class DataFetcher:
    """
    数据获取类
    支持多个交易所数据源，自动尝试切换
    支持同时获取多个交易对数据
    """
    
    # 支持的交易所列表
    SUPPORTED_EXCHANGES = ['gate'
    ]
    
    def __init__(self, symbols=None, timeframe="5m", exchange_id=None):
        """
        初始化数据获取器
        
        参数:
            symbols (str or list): 交易对，如 "BTC/USDT" 或 ["BTC/USDT", "ETH/USDT"]
            timeframe (str): 时间周期，如 5m, 15m, 1h
            exchange_id (str, optional): 指定交易所ID，若不指定则自动尝试可用交易所
        """
        # 确保symbols是列表形式
        if symbols is None:
            self.symbols = ["BTC/USDT"]
        elif isinstance(symbols, str):
            self.symbols = [symbols]
        else:
            self.symbols = symbols
            
        self.timeframe = timeframe
        self.exchanges = []
        self.current_exchange_index = 0
        
        # 初始化交易所列表
        if exchange_id and exchange_id in self.SUPPORTED_EXCHANGES:
            # 如果指定了交易所，则只初始化该交易所
            self._init_exchange(exchange_id)
        else:
            # 否则初始化所有支持的交易所
            for ex_id in self.SUPPORTED_EXCHANGES:
                success = self._init_exchange(ex_id)
                if success:
                    logger.info(f"成功初始化交易所: {ex_id}")
                else:
                    logger.warning(f"初始化交易所 {ex_id} 失败，将不使用该交易所")
        
        if not self.exchanges:
            logger.error("无法初始化任何交易所，请检查网络连接")
            raise Exception("无法初始化任何交易所")
        
        logger.info(f"数据获取器初始化完成，当前使用交易所: {self.current_exchange.id}，监控交易对: {self.symbols}")
    
    def _init_exchange(self, exchange_id):
        """初始化单个交易所"""
        try:
            # 设置更长的超时时间
            exchange_class = getattr(ccxt, exchange_id)
            exchange = exchange_class({
                'timeout': 30000,  # 30秒超时
                'enableRateLimit': True,
            })
            
            # 验证交易所是否支持该交易对和时间周期
            exchange.load_markets()
            
            # 检查是否支持所有交易对
            for symbol in self.symbols:
                if symbol not in exchange.symbols:
                    logger.warning(f"交易所 {exchange_id} 不支持交易对 {symbol}")
                    return False
                    
            # 检查是否支持该时间周期
            if hasattr(exchange, 'timeframes') and self.timeframe not in exchange.timeframes:
                logger.warning(f"交易所 {exchange_id} 不支持时间周期 {self.timeframe}")
                return False
            
            # 测试连接性 - 选择第一个交易对进行测试
            exchange.fetch_ticker(self.symbols[0])
            
            # 添加到交易所列表
            self.exchanges.append(exchange)
            return True
            
        except Exception as e:
            logger.warning(f"初始化交易所 {exchange_id} 时出错: {str(e)}")
            return False
    
    @property
    def current_exchange(self):
        """获取当前使用的交易所"""
        if not self.exchanges:
            raise Exception("没有可用的交易所")
        return self.exchanges[self.current_exchange_index]
    
    def switch_exchange(self):
        """切换到下一个可用的交易所"""
        if len(self.exchanges) <= 1:
            logger.warning("没有其他可用的交易所可切换")
            return False
        
        old_exchange = self.current_exchange.id
        self.current_exchange_index = (self.current_exchange_index + 1) % len(self.exchanges)
        logger.info(f"已切换交易所: {old_exchange} -> {self.current_exchange.id}")
        return True
    
    def get_klines(self, symbol=None, limit=100, retry_count=3):
        """
        获取K线数据
        
        参数:
            symbol (str, optional): 指定获取特定交易对的数据，不指定则返回第一个交易对的数据
            limit (int): 获取的K线数量
            retry_count (int): 失败重试次数，会自动切换交易所
            
        返回:
            pandas.DataFrame: K线数据
        """
        # 如果没有指定symbol，使用第一个交易对
        if symbol is None:
            symbol = self.symbols[0]
        
        # 确保symbol在监控列表中
        if symbol not in self.symbols:
            logger.error(f"交易对 {symbol} 不在监控列表中: {self.symbols}")
            raise ValueError(f"交易对 {symbol} 不在监控列表中")
            
        for attempt in range(retry_count):
            try:
                logger.info(f"从 {self.current_exchange.id} 获取 {symbol} {self.timeframe} K线数据，数量: {limit}")
                
                # 获取K线数据
                ohlcv = self.current_exchange.fetch_ohlcv(
                    symbol=symbol,
                    timeframe=self.timeframe,
                    limit=limit
                )
                
                if not ohlcv or len(ohlcv) == 0:
                    raise Exception("获取到的K线数据为空")
                
                # 转换为DataFrame
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                
                # 转换时间戳
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                
                logger.info(f"成功获取 {len(df)} 条K线数据")
                return df
                
            except Exception as e:
                logger.error(f"从 {self.current_exchange.id} 获取K线数据失败: {str(e)}")
                
                if attempt < retry_count - 1:
                    if self.switch_exchange():
                        logger.info(f"尝试使用新的交易所: {self.current_exchange.id}")
                    else:
                        logger.warning("无法切换到其他交易所，尝试重试当前交易所")
                    time.sleep(1)  # 避免立即重试
                    
        # 所有尝试都失败
        logger.error(f"在 {retry_count} 次尝试后仍未能获取K线数据")
        raise Exception("无法获取K线数据，请检查网络连接或尝试其他交易所")
    
    def get_all_klines(self, limit=100, retry_count=3):
        """
        获取所有监控交易对的K线数据
        
        参数:
            limit (int): 获取的K线数量
            retry_count (int): 失败重试次数，会自动切换交易所
            
        返回:
            Dict[str, pandas.DataFrame]: 包含所有交易对K线数据的字典
        """
        all_klines = {}
        
        for symbol in self.symbols:
            try:
                df = self.get_klines(symbol=symbol, limit=limit, retry_count=retry_count)
                all_klines[symbol] = df
            except Exception as e:
                logger.error(f"获取 {symbol} K线数据失败: {str(e)}")
                all_klines[symbol] = pd.DataFrame()  # 返回空DataFrame
        
        return all_klines
    
    def get_latest_price(self, symbol=None):
        """
        获取最新价格
        
        参数:
            symbol (str, optional): 指定获取特定交易对的价格，不指定则返回第一个交易对的价格
            
        返回:
            float: 最新价格
        """
        # 如果没有指定symbol，使用第一个交易对
        if symbol is None:
            symbol = self.symbols[0]
            
        # 确保symbol在监控列表中
        if symbol not in self.symbols:
            logger.error(f"交易对 {symbol} 不在监控列表中: {self.symbols}")
            raise ValueError(f"交易对 {symbol} 不在监控列表中")
            
        for attempt in range(3):
            try:
                ticker = self.current_exchange.fetch_ticker(symbol)
                return ticker['last']
            except Exception as e:
                logger.error(f"获取 {symbol} 最新价格失败: {str(e)}")
                if not self.switch_exchange():
                    break
        
        raise Exception(f"无法获取 {symbol} 最新价格")
    
    def get_all_latest_prices(self):
        """
        获取所有监控交易对的最新价格
        
        返回:
            Dict[str, float]: 包含所有交易对最新价格的字典
        """
        all_prices = {}
        
        for symbol in self.symbols:
            try:
                price = self.get_latest_price(symbol=symbol)
                all_prices[symbol] = price
            except Exception as e:
                logger.error(f"获取 {symbol} 最新价格失败: {str(e)}")
                all_prices[symbol] = None  # 获取失败返回None
        
        return all_prices


# 测试代码
if __name__ == "__main__":
    # 设置日志
    logger.add("logs/data_fetcher_{time}.log", rotation="100 MB")
    
    # 测试多个交易对
    symbols = ["BTC/USDT", "ETH/USDT"]
    print(f"\n测试多个交易对: {symbols}")
    try:
        fetcher = DataFetcher(symbols=symbols, timeframe="5m", exchange_id="gate")
        
        # 获取单个交易对数据
        btc_df = fetcher.get_klines(symbol="BTC/USDT", limit=5)
        print(f"BTC/USDT 获取到 {len(btc_df)} 条数据")
        print(btc_df.head(2))
        
        # 获取所有交易对数据
        all_klines = fetcher.get_all_klines(limit=5)
        for symbol, df in all_klines.items():
            print(f"{symbol} 获取到 {len(df)} 条数据")
            
        # 获取最新价格
        all_prices = fetcher.get_all_latest_prices()
        for symbol, price in all_prices.items():
            print(f"{symbol} 当前价格: {price}")
            
        print("多交易对测试成功\n")
    except Exception as e:
        print(f"多交易对测试失败: {str(e)}") 