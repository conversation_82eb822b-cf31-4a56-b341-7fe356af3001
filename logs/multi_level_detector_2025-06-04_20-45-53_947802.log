2025-06-04 20:45:53.951 | INFO     | __main__:__init__:41 - 初始化多级别线段终结判断系统，交易对: ETH/USDT
2025-06-04 20:46:08.349 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-04 20:46:08.349 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-04 20:46:08.349 | INFO     | multi_level_analysis:__init__:63 - 初始化 5m 级别分析器成功
2025-06-04 20:46:15.949 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-04 20:46:15.950 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-04 20:46:15.950 | INFO     | multi_level_analysis:__init__:63 - 初始化 15m 级别分析器成功
2025-06-04 20:46:22.503 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-04 20:46:22.504 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-04 20:46:22.504 | INFO     | multi_level_analysis:__init__:63 - 初始化 30m 级别分析器成功
2025-06-04 20:46:26.608 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-04 20:46:26.608 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-04 20:46:26.609 | INFO     | multi_level_analysis:__init__:63 - 初始化 1h 级别分析器成功
2025-06-04 20:46:26.609 | INFO     | multi_level_analysis:__init__:70 - 多级别分析器初始化完成，监控级别: ['5m', '15m', '30m', '1h']
2025-06-04 20:46:26.609 | INFO     | notifier:_load_dingtalk_config_from_file:67 - 已从配置文件加载钉钉机器人配置
2025-06-04 20:46:26.609 | INFO     | notifier:__init__:51 - 初始化通知器，通知方式: ['log', 'file', 'dingtalk']
2025-06-04 20:46:26.609 | INFO     | __main__:__init__:59 - 多级别系统初始化完成
2025-06-04 20:46:26.610 | INFO     | __main__:signal_monitor:512 - 检查 ETH/USDT 的买卖点信号...
2025-06-04 20:46:26.610 | INFO     | __main__:main:617 - 已启动 ETH/USDT 的买卖点信号监控，间隔: 5分钟
2025-06-04 20:46:26.610 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 5m 实时买卖点信号
2025-06-04 20:46:26.611 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 5m K线数据，数量: 200
2025-06-04 20:46:27.863 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-04 20:46:27.872 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:27.874 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:27.911 | INFO     | chan_analysis:find_fractals:172 - 共找到 53 个分型
2025-06-04 20:46:27.911 | INFO     | chan_analysis:identify_bi:205 - 共识别出 40 个笔
2025-06-04 20:46:27.911 | INFO     | chan_analysis:identify_xd:294 - 共识别出 3 个线段
2025-06-04 20:46:27.912 | INFO     | chan_analysis:analyze:494 - 当前线段未终结
2025-06-04 20:46:27.916 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:27.916 | INFO     | multi_level_analysis:_calculate_stop_levels:1450 - 使用缠论理论计算止盈止损: 止损=2664.8345, 止盈1=2634.52, 止盈2=2480.1429999999996, 止盈3=2374.605
2025-06-04 20:46:27.918 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:27.919 | INFO     | multi_level_analysis:_calculate_stop_levels:1450 - 使用缠论理论计算止盈止损: 止损=2581.8296, 止盈1=2691.219, 止盈2=None, 止盈3=None
2025-06-04 20:46:27.919 | INFO     | multi_level_analysis:check_realtime_signals:1381 - ETH/USDT 5m 实时信号检查完成: 买卖信号同时存在，买点更新鲜（0根K线前 vs 7根K线前），中枢上沿买点，强度: 8/10
2025-06-04 20:46:27.920 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_204626.txt
2025-06-04 20:46:30.235 | INFO     | notifier:_send_dingtalk:191 - 钉钉通知发送成功
2025-06-04 20:46:30.236 | INFO     | __main__:signal_monitor:596 - 已发送 ETH/USDT 5m 买卖点信号通知到钉钉
2025-06-04 20:46:30.236 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 5m 买卖点信号通知
2025-06-04 20:46:30.237 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 15m 实时买卖点信号
2025-06-04 20:46:30.237 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 15m K线数据，数量: 200
2025-06-04 20:46:30.450 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-04 20:46:30.458 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:30.464 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:30.508 | INFO     | chan_analysis:find_fractals:172 - 共找到 52 个分型
2025-06-04 20:46:30.509 | INFO     | chan_analysis:identify_bi:205 - 共识别出 38 个笔
2025-06-04 20:46:30.509 | INFO     | chan_analysis:identify_xd:294 - 共识别出 5 个线段
2025-06-04 20:46:30.509 | WARNING  | chan_analysis:analyze:492 - 检测到线段终结信号: {'终结': True, 'MACD背驰': False, '结构破坏': True, '均线突破': True, '形态变化': False, '成交量异常': False, '细节': {'结构破坏': '当前低点 2624.1 跌破了前期高点 2644.51', '均线突破': '收盘价跌破MA20均线'}, '原因': '满足2个终结条件'}
2025-06-04 20:46:30.513 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:30.513 | INFO     | multi_level_analysis:_calculate_stop_levels:1450 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:46:30.516 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:30.516 | INFO     | multi_level_analysis:_calculate_stop_levels:1450 - 使用缠论理论计算止盈止损: 止损=2660.5521, 止盈1=2631.39, 止盈2=2480.1429999999996, 止盈3=2374.605
2025-06-04 20:46:30.519 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:30.519 | INFO     | multi_level_analysis:_calculate_stop_levels:1450 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:46:30.521 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:30.522 | INFO     | multi_level_analysis:_calculate_stop_levels:1450 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:46:30.524 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:30.524 | INFO     | multi_level_analysis:_calculate_stop_levels:1450 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:46:30.527 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:30.527 | INFO     | multi_level_analysis:_calculate_stop_levels:1450 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:46:30.529 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:30.529 | INFO     | multi_level_analysis:_calculate_stop_levels:1450 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:46:30.532 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:30.532 | INFO     | multi_level_analysis:_calculate_stop_levels:1450 - 使用缠论理论计算止盈止损: 止损=2532.0456, 止盈1=2757.531, 止盈2=2897.6310000000003, 止盈3=3020.1529999999993
2025-06-04 20:46:30.533 | INFO     | multi_level_analysis:check_realtime_signals:1381 - ETH/USDT 15m 实时信号检查完成: 买卖信号同时存在，卖点更新鲜（3根K线前 vs 14根K线前），第一类卖点，强度: 11/10
2025-06-04 20:46:30.533 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_204626.txt
2025-06-04 20:46:32.283 | INFO     | notifier:_send_dingtalk:191 - 钉钉通知发送成功
2025-06-04 20:46:32.284 | INFO     | __main__:signal_monitor:596 - 已发送 ETH/USDT 15m 买卖点信号通知到钉钉
2025-06-04 20:46:32.284 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 15m 买卖点信号通知
2025-06-04 20:46:32.284 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 1h 实时买卖点信号
2025-06-04 20:46:32.284 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 1h K线数据，数量: 200
2025-06-04 20:46:32.390 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-04 20:46:32.396 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:32.401 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:32.442 | INFO     | chan_analysis:find_fractals:172 - 共找到 61 个分型
2025-06-04 20:46:32.442 | INFO     | chan_analysis:identify_bi:205 - 共识别出 52 个笔
2025-06-04 20:46:32.442 | INFO     | chan_analysis:identify_xd:294 - 共识别出 13 个线段
2025-06-04 20:46:32.443 | WARNING  | chan_analysis:analyze:492 - 检测到线段终结信号: {'终结': True, 'MACD背驰': False, '结构破坏': True, '均线突破': True, '形态变化': False, '成交量异常': False, '细节': {'结构破坏': '当前高点 2638.45 突破了前期低点 2619.54', '均线突破': '收盘价突破MA20均线'}, '原因': '满足2个终结条件'}
2025-06-04 20:46:32.447 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:32.447 | INFO     | multi_level_analysis:_calculate_stop_levels:1450 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2672.8181999999997, 止盈2=None, 止盈3=None
2025-06-04 20:46:32.450 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:32.450 | INFO     | multi_level_analysis:_calculate_stop_levels:1450 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2672.8181999999997, 止盈2=None, 止盈3=None
2025-06-04 20:46:32.452 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:32.452 | INFO     | multi_level_analysis:_calculate_stop_levels:1450 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 20:46:32.455 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:32.455 | INFO     | multi_level_analysis:_calculate_stop_levels:1450 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 20:46:32.457 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:46:32.458 | INFO     | multi_level_analysis:_calculate_stop_levels:1450 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 20:46:32.458 | INFO     | multi_level_analysis:check_realtime_signals:1381 - ETH/USDT 1h 实时信号检查完成: 买卖信号均已过时（买:12根K线前，卖:17根K线前），建议观望
2025-06-04 20:46:32.458 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_204626.txt
2025-06-04 20:46:32.459 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-04 20:46:32.459 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 1h 买卖点信号通知
2025-06-04 20:49:09.768 | INFO     | __main__:main:627 - 用户中断，正在退出...
