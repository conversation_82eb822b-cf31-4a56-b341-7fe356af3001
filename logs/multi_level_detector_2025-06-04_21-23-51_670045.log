2025-06-04 21:23:51.673 | INFO     | __main__:__init__:41 - 初始化多级别线段终结判断系统，交易对: ETH/USDT
2025-06-04 21:25:13.764 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-04 21:25:13.766 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-04 21:25:13.766 | INFO     | multi_level_analysis:__init__:63 - 初始化 5m 级别分析器成功
2025-06-04 21:27:01.961 | WARNING  | data_fetcher:_init_exchange:95 - 初始化交易所 gate 时出错: gate GET https://api.gateio.ws/api/v4/spot/currencies
2025-06-04 21:27:01.963 | WARNING  | data_fetcher:__init__:55 - 初始化交易所 gate 失败，将不使用该交易所
2025-06-04 21:27:01.963 | ERROR    | data_fetcher:__init__:58 - 无法初始化任何交易所，请检查网络连接
2025-06-04 21:27:01.964 | ERROR    | multi_level_analysis:__init__:65 - 初始化 15m 级别分析器失败: 无法初始化任何交易所
2025-06-04 21:27:09.610 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-04 21:27:09.611 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-04 21:27:09.611 | INFO     | multi_level_analysis:__init__:63 - 初始化 30m 级别分析器成功
2025-06-04 21:27:30.937 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-04 21:27:30.937 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-04 21:27:30.937 | INFO     | multi_level_analysis:__init__:63 - 初始化 1h 级别分析器成功
2025-06-04 21:27:30.937 | INFO     | multi_level_analysis:__init__:70 - 多级别分析器初始化完成，监控级别: ['5m', '15m', '30m', '1h']
2025-06-04 21:27:30.938 | INFO     | notifier:_load_dingtalk_config_from_file:67 - 已从配置文件加载钉钉机器人配置
2025-06-04 21:27:30.938 | INFO     | notifier:__init__:51 - 初始化通知器，通知方式: ['log', 'file', 'dingtalk']
2025-06-04 21:27:30.938 | INFO     | __main__:__init__:59 - 多级别系统初始化完成
2025-06-04 21:27:30.939 | INFO     | __main__:signal_monitor:512 - 检查 ETH/USDT 的买卖点信号...
2025-06-04 21:27:30.939 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 5m 实时买卖点信号
2025-06-04 21:27:30.939 | INFO     | __main__:main:617 - 已启动 ETH/USDT 的买卖点信号监控，间隔: 5分钟
2025-06-04 21:27:30.940 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 5m K线数据，数量: 200
2025-06-04 21:27:55.130 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-04 21:27:55.148 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:27:55.152 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:27:55.185 | INFO     | chan_analysis:find_fractals:172 - 共找到 53 个分型
2025-06-04 21:27:55.185 | INFO     | chan_analysis:identify_bi:205 - 共识别出 41 个笔
2025-06-04 21:27:55.185 | INFO     | chan_analysis:identify_xd:294 - 共识别出 3 个线段
2025-06-04 21:27:55.186 | INFO     | chan_analysis:analyze:494 - 当前线段未终结
2025-06-04 21:27:55.190 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:27:55.191 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2653.2498, 止盈1=2623.34, 止盈2=2469.3612, 止盈3=2364.282
2025-06-04 21:27:55.193 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:27:55.193 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2653.2498, 止盈1=2623.34, 止盈2=2469.3612, 止盈3=2364.282
2025-06-04 21:27:55.196 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:27:55.196 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2570.8732, 止盈1=2679.5196, 止盈2=None, 止盈3=None
2025-06-04 21:27:55.196 | INFO     | multi_level_analysis:check_realtime_signals:1397 - ETH/USDT 5m 实时信号检查完成: 买卖信号同时存在，买点更新鲜（0根K线前 vs 8根K线前），中枢上沿买点，强度: 8/10，止损: 2570.8732，止盈1: 2679.5196，止盈2: None，止盈3: None
2025-06-04 21:27:55.197 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_212730.txt
2025-06-04 21:27:55.741 | INFO     | notifier:_send_dingtalk:191 - 钉钉通知发送成功
2025-06-04 21:27:55.741 | INFO     | __main__:signal_monitor:596 - 已发送 ETH/USDT 5m 买卖点信号通知到钉钉
2025-06-04 21:27:55.742 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 5m 买卖点信号通知
2025-06-04 21:27:55.742 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 15m 实时买卖点信号
2025-06-04 21:27:55.743 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_212730.txt
2025-06-04 21:27:55.743 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-04 21:27:55.744 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 15m 买卖点信号通知
2025-06-04 21:27:55.744 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 1h 实时买卖点信号
2025-06-04 21:27:55.744 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 1h K线数据，数量: 200
2025-06-04 21:27:55.928 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-04 21:27:55.932 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:27:55.936 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:27:55.972 | INFO     | chan_analysis:find_fractals:172 - 共找到 61 个分型
2025-06-04 21:27:55.973 | INFO     | chan_analysis:identify_bi:205 - 共识别出 52 个笔
2025-06-04 21:27:55.973 | INFO     | chan_analysis:identify_xd:294 - 共识别出 13 个线段
2025-06-04 21:27:55.973 | INFO     | chan_analysis:analyze:494 - 当前线段未终结
2025-06-04 21:27:55.979 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:27:55.979 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2672.8181999999997, 止盈2=None, 止盈3=None
2025-06-04 21:27:55.982 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:27:55.982 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2672.8181999999997, 止盈2=None, 止盈3=None
2025-06-04 21:27:55.984 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:27:55.984 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 21:27:55.987 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:27:55.987 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 21:27:55.989 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:27:55.989 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 21:27:55.989 | INFO     | multi_level_analysis:check_realtime_signals:1397 - ETH/USDT 1h 实时信号检查完成: 买卖信号均已过时（买:13根K线前，卖:18根K线前），建议观望
2025-06-04 21:27:55.991 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_212730.txt
2025-06-04 21:27:55.991 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-04 21:27:55.991 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 1h 买卖点信号通知
2025-06-04 21:32:55.995 | INFO     | __main__:signal_monitor:512 - 检查 ETH/USDT 的买卖点信号...
2025-06-04 21:32:55.998 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 5m 实时买卖点信号
2025-06-04 21:32:55.998 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 5m K线数据，数量: 200
2025-06-04 21:33:08.191 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-04 21:33:08.209 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:33:08.213 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:33:08.247 | INFO     | chan_analysis:find_fractals:172 - 共找到 53 个分型
2025-06-04 21:33:08.248 | INFO     | chan_analysis:identify_bi:205 - 共识别出 41 个笔
2025-06-04 21:33:08.248 | INFO     | chan_analysis:identify_xd:294 - 共识别出 3 个线段
2025-06-04 21:33:08.249 | INFO     | chan_analysis:analyze:494 - 当前线段未终结
2025-06-04 21:33:08.253 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:33:08.254 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2653.2498, 止盈1=2623.34, 止盈2=2469.3612, 止盈3=2364.282
2025-06-04 21:33:08.256 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:33:08.257 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2570.8732, 止盈1=2679.5196, 止盈2=None, 止盈3=None
2025-06-04 21:33:08.259 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:33:08.259 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2653.2498, 止盈1=2623.34, 止盈2=2469.3612, 止盈3=2364.282
2025-06-04 21:33:08.259 | INFO     | multi_level_analysis:check_realtime_signals:1397 - ETH/USDT 5m 实时信号检查完成: 买卖信号同时存在，买点更新鲜（3根K线前 vs 9根K线前），第二类买点，强度: 10/10，止损: 2570.8732，止盈1: 2679.5196，止盈2: None，止盈3: None
2025-06-04 21:33:08.260 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_212730.txt
2025-06-04 21:33:08.788 | INFO     | notifier:_send_dingtalk:191 - 钉钉通知发送成功
2025-06-04 21:33:08.789 | INFO     | __main__:signal_monitor:596 - 已发送 ETH/USDT 5m 买卖点信号通知到钉钉
2025-06-04 21:33:08.789 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 5m 买卖点信号通知
2025-06-04 21:33:08.789 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 15m 实时买卖点信号
2025-06-04 21:33:08.790 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_212730.txt
2025-06-04 21:33:08.790 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-04 21:33:08.790 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 15m 买卖点信号通知
2025-06-04 21:33:08.790 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 1h 实时买卖点信号
2025-06-04 21:33:08.790 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 1h K线数据，数量: 200
2025-06-04 21:33:52.975 | ERROR    | data_fetcher:get_klines:161 - 从 gate 获取K线数据失败: gate GET https://api.gateio.ws/api/v4/spot/candlesticks?currency_pair=ETH_USDT&interval=1h&limit=200
2025-06-04 21:33:52.977 | WARNING  | data_fetcher:switch_exchange:108 - 没有其他可用的交易所可切换
2025-06-04 21:33:52.977 | WARNING  | data_fetcher:get_klines:167 - 无法切换到其他交易所，尝试重试当前交易所
2025-06-04 21:33:53.979 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 1h K线数据，数量: 200
2025-06-04 21:33:55.262 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-04 21:33:55.267 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:33:55.270 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:33:55.302 | INFO     | chan_analysis:find_fractals:172 - 共找到 61 个分型
2025-06-04 21:33:55.302 | INFO     | chan_analysis:identify_bi:205 - 共识别出 52 个笔
2025-06-04 21:33:55.303 | INFO     | chan_analysis:identify_xd:294 - 共识别出 13 个线段
2025-06-04 21:33:55.303 | INFO     | chan_analysis:analyze:494 - 当前线段未终结
2025-06-04 21:33:55.309 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:33:55.310 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2672.8181999999997, 止盈2=None, 止盈3=None
2025-06-04 21:33:55.313 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:33:55.313 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2672.8181999999997, 止盈2=None, 止盈3=None
2025-06-04 21:33:55.315 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:33:55.315 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 21:33:55.318 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:33:55.318 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 21:33:55.321 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:33:55.321 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 21:33:55.321 | INFO     | multi_level_analysis:check_realtime_signals:1397 - ETH/USDT 1h 实时信号检查完成: 买卖信号均已过时（买:13根K线前，卖:18根K线前），建议观望
2025-06-04 21:33:55.322 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_212730.txt
2025-06-04 21:33:55.322 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-04 21:33:55.322 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 1h 买卖点信号通知
2025-06-04 21:37:00.411 | INFO     | __main__:main:627 - 用户中断，正在退出...
