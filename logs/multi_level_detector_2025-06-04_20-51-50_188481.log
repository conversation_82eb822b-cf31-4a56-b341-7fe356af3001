2025-06-04 20:51:50.191 | INFO     | __main__:__init__:41 - 初始化多级别线段终结判断系统，交易对: ETH/USDT
2025-06-04 20:52:39.295 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-04 20:52:39.296 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-04 20:52:39.297 | INFO     | multi_level_analysis:__init__:63 - 初始化 5m 级别分析器成功
2025-06-04 20:53:21.278 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-04 20:53:21.279 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-04 20:53:21.279 | INFO     | multi_level_analysis:__init__:63 - 初始化 15m 级别分析器成功
2025-06-04 20:53:42.177 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-04 20:53:42.177 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-04 20:53:42.178 | INFO     | multi_level_analysis:__init__:63 - 初始化 30m 级别分析器成功
2025-06-04 20:54:33.025 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-04 20:54:33.026 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-04 20:54:33.026 | INFO     | multi_level_analysis:__init__:63 - 初始化 1h 级别分析器成功
2025-06-04 20:54:33.026 | INFO     | multi_level_analysis:__init__:70 - 多级别分析器初始化完成，监控级别: ['5m', '15m', '30m', '1h']
2025-06-04 20:54:33.027 | INFO     | notifier:_load_dingtalk_config_from_file:67 - 已从配置文件加载钉钉机器人配置
2025-06-04 20:54:33.027 | INFO     | notifier:__init__:51 - 初始化通知器，通知方式: ['log', 'file', 'dingtalk']
2025-06-04 20:54:33.027 | INFO     | __main__:__init__:59 - 多级别系统初始化完成
2025-06-04 20:54:33.028 | INFO     | __main__:signal_monitor:512 - 检查 ETH/USDT 的买卖点信号...
2025-06-04 20:54:33.028 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 5m 实时买卖点信号
2025-06-04 20:54:33.028 | INFO     | __main__:main:617 - 已启动 ETH/USDT 的买卖点信号监控，间隔: 5分钟
2025-06-04 20:54:33.030 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 5m K线数据，数量: 200
2025-06-04 20:54:33.255 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-04 20:54:33.263 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:33.267 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:33.305 | INFO     | chan_analysis:find_fractals:172 - 共找到 53 个分型
2025-06-04 20:54:33.305 | INFO     | chan_analysis:identify_bi:205 - 共识别出 40 个笔
2025-06-04 20:54:33.305 | INFO     | chan_analysis:identify_xd:294 - 共识别出 3 个线段
2025-06-04 20:54:33.306 | WARNING  | chan_analysis:analyze:492 - 检测到线段终结信号: {'终结': True, 'MACD背驰': True, '结构破坏': True, '均线突破': False, '形态变化': False, '成交量异常': False, '细节': {'MACD背驰': '价格创新低，但MACD未创新低', '结构破坏': '当前高点 2624.35 突破了前期低点 2616.0'}, '原因': '满足2个终结条件'}
2025-06-04 20:54:33.310 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:33.310 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2664.8345, 止盈1=2634.52, 止盈2=2480.1429999999996, 止盈3=2374.605
2025-06-04 20:54:33.313 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:33.313 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2581.8296, 止盈1=2691.219, 止盈2=None, 止盈3=None
2025-06-04 20:54:33.313 | INFO     | multi_level_analysis:check_realtime_signals:1393 - ETH/USDT 5m 实时信号检查完成: 买卖信号同时存在，买点更新鲜（0根K线前 vs 8根K线前），中枢上沿买点，强度: 8/10
2025-06-04 20:54:33.315 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_205433.txt
2025-06-04 20:54:35.113 | INFO     | notifier:_send_dingtalk:191 - 钉钉通知发送成功
2025-06-04 20:54:35.114 | INFO     | __main__:signal_monitor:596 - 已发送 ETH/USDT 5m 买卖点信号通知到钉钉
2025-06-04 20:54:35.115 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 5m 买卖点信号通知
2025-06-04 20:54:35.115 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 15m 实时买卖点信号
2025-06-04 20:54:35.115 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 15m K线数据，数量: 200
2025-06-04 20:54:35.723 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-04 20:54:35.727 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:35.730 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:35.775 | INFO     | chan_analysis:find_fractals:172 - 共找到 52 个分型
2025-06-04 20:54:35.776 | INFO     | chan_analysis:identify_bi:205 - 共识别出 38 个笔
2025-06-04 20:54:35.776 | INFO     | chan_analysis:identify_xd:294 - 共识别出 5 个线段
2025-06-04 20:54:35.776 | WARNING  | chan_analysis:analyze:492 - 检测到线段终结信号: {'终结': True, 'MACD背驰': False, '结构破坏': True, '均线突破': True, '形态变化': False, '成交量异常': False, '细节': {'结构破坏': '当前低点 2619.08 跌破了前期高点 2644.51', '均线突破': '收盘价跌破MA20均线'}, '原因': '满足2个终结条件'}
2025-06-04 20:54:35.780 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:35.781 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:54:35.784 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:35.784 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2660.5521, 止盈1=2631.39, 止盈2=2480.1429999999996, 止盈3=2374.605
2025-06-04 20:54:35.786 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:35.787 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:54:35.789 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:35.789 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:54:35.792 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:35.792 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:54:35.795 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:35.795 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:54:35.798 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:35.798 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:54:35.800 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:35.801 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2532.0456, 止盈1=2752.2495000000004, 止盈2=2897.6310000000003, 止盈3=3014.3685
2025-06-04 20:54:35.801 | INFO     | multi_level_analysis:check_realtime_signals:1393 - ETH/USDT 15m 实时信号检查完成: 买卖信号同时存在，卖点更新鲜（3根K线前 vs 14根K线前），第一类卖点，强度: 11/10
2025-06-04 20:54:35.801 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_205433.txt
2025-06-04 20:54:36.044 | INFO     | notifier:_send_dingtalk:191 - 钉钉通知发送成功
2025-06-04 20:54:36.045 | INFO     | __main__:signal_monitor:596 - 已发送 ETH/USDT 15m 买卖点信号通知到钉钉
2025-06-04 20:54:36.046 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 15m 买卖点信号通知
2025-06-04 20:54:36.046 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 1h 实时买卖点信号
2025-06-04 20:54:36.046 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 1h K线数据，数量: 200
2025-06-04 20:54:36.817 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-04 20:54:36.825 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:36.830 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:36.874 | INFO     | chan_analysis:find_fractals:172 - 共找到 61 个分型
2025-06-04 20:54:36.874 | INFO     | chan_analysis:identify_bi:205 - 共识别出 52 个笔
2025-06-04 20:54:36.874 | INFO     | chan_analysis:identify_xd:294 - 共识别出 13 个线段
2025-06-04 20:54:36.874 | WARNING  | chan_analysis:analyze:492 - 检测到线段终结信号: {'终结': True, 'MACD背驰': False, '结构破坏': True, '均线突破': True, '形态变化': False, '成交量异常': False, '细节': {'结构破坏': '当前高点 2638.45 突破了前期低点 2619.54', '均线突破': '收盘价突破MA20均线'}, '原因': '满足2个终结条件'}
2025-06-04 20:54:36.878 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:36.878 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2672.8181999999997, 止盈2=None, 止盈3=None
2025-06-04 20:54:36.880 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:36.880 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2672.8181999999997, 止盈2=None, 止盈3=None
2025-06-04 20:54:36.883 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:36.883 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 20:54:36.886 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:36.886 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 20:54:36.889 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:54:36.889 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 20:54:36.889 | INFO     | multi_level_analysis:check_realtime_signals:1393 - ETH/USDT 1h 实时信号检查完成: 买卖信号均已过时（买:12根K线前，卖:17根K线前），建议观望
2025-06-04 20:54:36.889 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_205433.txt
2025-06-04 20:54:36.889 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-04 20:54:36.889 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 1h 买卖点信号通知
2025-06-04 20:59:36.891 | INFO     | __main__:signal_monitor:512 - 检查 ETH/USDT 的买卖点信号...
2025-06-04 20:59:36.894 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 5m 实时买卖点信号
2025-06-04 20:59:36.897 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 5m K线数据，数量: 200
2025-06-04 20:59:38.154 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-04 20:59:38.167 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:38.171 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:38.210 | INFO     | chan_analysis:find_fractals:172 - 共找到 54 个分型
2025-06-04 20:59:38.210 | INFO     | chan_analysis:identify_bi:205 - 共识别出 41 个笔
2025-06-04 20:59:38.210 | INFO     | chan_analysis:identify_xd:294 - 共识别出 3 个线段
2025-06-04 20:59:38.211 | INFO     | chan_analysis:analyze:494 - 当前线段未终结
2025-06-04 20:59:38.215 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:38.215 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2664.8345, 止盈1=2634.52, 止盈2=2480.1429999999996, 止盈3=2374.605
2025-06-04 20:59:38.215 | INFO     | multi_level_analysis:check_realtime_signals:1393 - ETH/USDT 5m 实时信号检查完成: 发现第一类卖点，强度: 10/10，位于9根K线前，止损: 2664.8345，止盈1: 2634.52，止盈2: 2480.1429999999996，止盈3: 2374.605
2025-06-04 20:59:38.216 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_205433.txt
2025-06-04 20:59:38.216 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-04 20:59:38.217 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 5m 买卖点信号通知
2025-06-04 20:59:38.217 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 15m 实时买卖点信号
2025-06-04 20:59:38.217 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 15m K线数据，数量: 200
2025-06-04 20:59:39.102 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-04 20:59:39.110 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:39.115 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:39.160 | INFO     | chan_analysis:find_fractals:172 - 共找到 51 个分型
2025-06-04 20:59:39.161 | INFO     | chan_analysis:identify_bi:205 - 共识别出 37 个笔
2025-06-04 20:59:39.161 | INFO     | chan_analysis:identify_xd:294 - 共识别出 5 个线段
2025-06-04 20:59:39.161 | WARNING  | chan_analysis:analyze:492 - 检测到线段终结信号: {'终结': True, 'MACD背驰': False, '结构破坏': True, '均线突破': True, '形态变化': False, '成交量异常': False, '细节': {'结构破坏': '当前低点 2615.0 跌破了前期高点 2644.51', '均线突破': '收盘价跌破MA20均线'}, '原因': '满足2个终结条件'}
2025-06-04 20:59:39.165 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:39.165 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:59:39.167 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:39.167 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:59:39.170 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:39.170 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:59:39.173 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:39.173 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:59:39.176 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:39.176 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:59:39.178 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:39.178 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 20:59:39.178 | INFO     | multi_level_analysis:check_realtime_signals:1393 - ETH/USDT 15m 实时信号检查完成: 发现第三类买点，但信号已过时（14根K线前），建议观望
2025-06-04 20:59:39.179 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_205433.txt
2025-06-04 20:59:39.179 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-04 20:59:39.179 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 15m 买卖点信号通知
2025-06-04 20:59:39.179 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 1h 实时买卖点信号
2025-06-04 20:59:39.179 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 1h K线数据，数量: 200
2025-06-04 20:59:42.094 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-04 20:59:42.102 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:42.107 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:42.152 | INFO     | chan_analysis:find_fractals:172 - 共找到 61 个分型
2025-06-04 20:59:42.153 | INFO     | chan_analysis:identify_bi:205 - 共识别出 52 个笔
2025-06-04 20:59:42.153 | INFO     | chan_analysis:identify_xd:294 - 共识别出 13 个线段
2025-06-04 20:59:42.153 | INFO     | chan_analysis:analyze:494 - 当前线段未终结
2025-06-04 20:59:42.158 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:42.158 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2672.8181999999997, 止盈2=None, 止盈3=None
2025-06-04 20:59:42.160 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:42.161 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2672.8181999999997, 止盈2=None, 止盈3=None
2025-06-04 20:59:42.163 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:42.163 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 20:59:42.165 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:42.165 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 20:59:42.168 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 20:59:42.168 | INFO     | multi_level_analysis:_calculate_stop_levels:1462 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 20:59:42.168 | INFO     | multi_level_analysis:check_realtime_signals:1393 - ETH/USDT 1h 实时信号检查完成: 买卖信号均已过时（买:12根K线前，卖:17根K线前），建议观望
2025-06-04 20:59:42.168 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_205433.txt
2025-06-04 20:59:42.168 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-04 20:59:42.168 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 1h 买卖点信号通知
2025-06-04 21:04:42.172 | INFO     | __main__:signal_monitor:512 - 检查 ETH/USDT 的买卖点信号...
2025-06-04 21:04:42.175 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 5m 实时买卖点信号
2025-06-04 21:04:42.182 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 5m K线数据，数量: 200
2025-06-04 21:04:45.362 | INFO     | __main__:main:627 - 用户中断，正在退出...
