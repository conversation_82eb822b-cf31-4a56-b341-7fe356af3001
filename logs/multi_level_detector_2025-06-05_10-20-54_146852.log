2025-06-05 10:20:54.149 | INFO     | __main__:__init__:41 - 初始化多级别线段终结判断系统，交易对: ETH/USDT
2025-06-05 10:20:59.607 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-05 10:20:59.607 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-05 10:20:59.608 | INFO     | multi_level_analysis:__init__:63 - 初始化 5m 级别分析器成功
2025-06-05 10:21:03.142 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-05 10:21:03.143 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-05 10:21:03.143 | INFO     | multi_level_analysis:__init__:63 - 初始化 15m 级别分析器成功
2025-06-05 10:21:08.250 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-05 10:21:08.251 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-05 10:21:08.251 | INFO     | multi_level_analysis:__init__:63 - 初始化 30m 级别分析器成功
2025-06-05 10:21:11.847 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-05 10:21:11.847 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-05 10:21:11.847 | INFO     | multi_level_analysis:__init__:63 - 初始化 1h 级别分析器成功
2025-06-05 10:21:11.847 | INFO     | multi_level_analysis:__init__:70 - 多级别分析器初始化完成，监控级别: ['5m', '15m', '30m', '1h']
2025-06-05 10:21:11.848 | INFO     | notifier:_load_dingtalk_config_from_file:67 - 已从配置文件加载钉钉机器人配置
2025-06-05 10:21:11.849 | INFO     | notifier:__init__:51 - 初始化通知器，通知方式: ['log', 'file', 'dingtalk']
2025-06-05 10:21:11.849 | INFO     | __main__:__init__:59 - 多级别系统初始化完成
2025-06-05 10:21:11.849 | INFO     | __main__:signal_monitor:512 - 检查 ETH/USDT 的买卖点信号...
2025-06-05 10:21:11.849 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 1m 实时买卖点信号
2025-06-05 10:21:11.849 | INFO     | __main__:main:617 - 已启动 ETH/USDT 的买卖点信号监控，间隔: 5分钟
2025-06-05 10:21:11.849 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250605_102111.txt
2025-06-05 10:21:11.850 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-05 10:21:11.850 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 1m 买卖点信号通知
2025-06-05 10:21:11.850 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 5m 实时买卖点信号
2025-06-05 10:21:11.850 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 5m K线数据，数量: 200
2025-06-05 10:21:11.963 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-05 10:21:11.968 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:11.971 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.003 | INFO     | chan_analysis:find_fractals:172 - 共找到 54 个分型
2025-06-05 10:21:12.003 | INFO     | chan_analysis:identify_bi:205 - 共识别出 46 个笔
2025-06-05 10:21:12.003 | INFO     | chan_analysis:identify_xd:294 - 共识别出 4 个线段
2025-06-05 10:21:12.003 | INFO     | chan_analysis:analyze:494 - 当前线段未终结
2025-06-05 10:21:12.007 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.007 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2638.4937, 止盈1=2609.18, 止盈2=2453.1838, 止盈3=2348.793
2025-06-05 10:21:12.010 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.010 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2547.7745999999997, 止盈1=2746.1175, 止盈2=2873.607, 止盈3=3007.6524999999997
2025-06-05 10:21:12.010 | INFO     | multi_level_analysis:check_realtime_signals:1397 - ETH/USDT 5m 实时信号检查完成: 买卖信号同时存在，买点更新鲜（0根K线前 vs 7根K线前），中枢上沿买点，强度: 8/10，止损: 2547.7745999999997，止盈1: 2746.1175，止盈2: 2873.607，止盈3: 3007.6524999999997
2025-06-05 10:21:12.010 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250605_102111.txt
2025-06-05 10:21:12.403 | INFO     | notifier:_send_dingtalk:191 - 钉钉通知发送成功
2025-06-05 10:21:12.404 | INFO     | __main__:signal_monitor:596 - 已发送 ETH/USDT 5m 买卖点信号通知到钉钉
2025-06-05 10:21:12.404 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 5m 买卖点信号通知
2025-06-05 10:21:12.404 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 15m 实时买卖点信号
2025-06-05 10:21:12.404 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 15m K线数据，数量: 200
2025-06-05 10:21:12.503 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-05 10:21:12.507 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.511 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.549 | INFO     | chan_analysis:find_fractals:172 - 共找到 52 个分型
2025-06-05 10:21:12.549 | INFO     | chan_analysis:identify_bi:205 - 共识别出 37 个笔
2025-06-05 10:21:12.549 | INFO     | chan_analysis:identify_xd:294 - 共识别出 7 个线段
2025-06-05 10:21:12.550 | INFO     | chan_analysis:analyze:494 - 当前线段未终结
2025-06-05 10:21:12.553 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.553 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2542.6198, 止盈1=2659.67, 止盈2=2891.4270000000006, 止盈3=2991.9435
2025-06-05 10:21:12.556 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.556 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2654.8557, 止盈1=2608.2, 止盈2=2458.9836, 止盈3=2354.346
2025-06-05 10:21:12.558 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.559 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2542.6198, 止盈1=2659.67, 止盈2=2891.4270000000006, 止盈3=2991.9435
2025-06-05 10:21:12.561 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.561 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2654.8557, 止盈1=2608.2, 止盈2=2459.0117999999998, 止盈3=2354.373
2025-06-05 10:21:12.564 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.564 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2654.8557, 止盈1=2608.2, 止盈2=2458.9836, 止盈3=2354.346
2025-06-05 10:21:12.566 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.566 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2654.8557, 止盈1=2608.2, 止盈2=2458.9836, 止盈3=2354.346
2025-06-05 10:21:12.568 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.568 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2654.8557, 止盈1=2608.2, 止盈2=2459.0117999999998, 止盈3=2354.373
2025-06-05 10:21:12.570 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.571 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2542.6198, 止盈1=2674.35, 止盈2=2891.4270000000006, 止盈3=3007.6524999999997
2025-06-05 10:21:12.571 | INFO     | multi_level_analysis:check_realtime_signals:1397 - ETH/USDT 15m 实时信号检查完成: 买卖信号同时存在，买点综合评分显著更高（10.0 vs 5.5），第一类买点，强度: 10/10，止损: 2542.6198，止盈1: 2659.67，止盈2: 2891.4270000000006，止盈3: 2991.9435
2025-06-05 10:21:12.573 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250605_102111.txt
2025-06-05 10:21:12.827 | INFO     | notifier:_send_dingtalk:191 - 钉钉通知发送成功
2025-06-05 10:21:12.828 | INFO     | __main__:signal_monitor:596 - 已发送 ETH/USDT 15m 买卖点信号通知到钉钉
2025-06-05 10:21:12.828 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 15m 买卖点信号通知
2025-06-05 10:21:12.828 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 1h 实时买卖点信号
2025-06-05 10:21:12.828 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 1h K线数据，数量: 200
2025-06-05 10:21:12.932 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-05 10:21:12.937 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.940 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.988 | INFO     | chan_analysis:find_fractals:172 - 共找到 60 个分型
2025-06-05 10:21:12.988 | INFO     | chan_analysis:identify_bi:205 - 共识别出 51 个笔
2025-06-05 10:21:12.988 | INFO     | chan_analysis:identify_xd:294 - 共识别出 14 个线段
2025-06-05 10:21:12.988 | WARNING  | chan_analysis:analyze:492 - 检测到线段终结信号: {'终结': True, 'MACD背驰': True, '结构破坏': True, '均线突破': True, '形态变化': False, '成交量异常': False, '细节': {'MACD背驰': '价格创新高，但MACD未创新高', '结构破坏': '当前低点 2608.37 跌破了前期高点 2633.17', '均线突破': '收盘价跌破MA20均线'}, '原因': '满足3个终结条件'}
2025-06-05 10:21:12.992 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.992 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2672.8181999999997, 止盈2=2750.1806000000006, 止盈3=2853.9610000000007
2025-06-05 10:21:12.995 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.995 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2491.0, 止盈3=2385.0
2025-06-05 10:21:12.998 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:12.999 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2491.0, 止盈3=2385.0
2025-06-05 10:21:13.001 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:13.001 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2672.8181999999997, 止盈2=2750.1806000000006, 止盈3=2853.9610000000007
2025-06-05 10:21:13.004 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:13.004 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2672.8181999999997, 止盈2=2750.1806000000006, 止盈3=2853.9610000000007
2025-06-05 10:21:13.006 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:21:13.006 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2672.8181999999997, 止盈2=2750.1806000000006, 止盈3=2853.9610000000007
2025-06-05 10:21:13.006 | INFO     | multi_level_analysis:check_realtime_signals:1397 - ETH/USDT 1h 实时信号检查完成: 买卖信号均已过时（买:12根K线前，卖:16根K线前），建议观望
2025-06-05 10:21:13.006 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250605_102111.txt
2025-06-05 10:21:13.007 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-05 10:21:13.007 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 1h 买卖点信号通知
2025-06-05 10:22:15.845 | INFO     | __main__:main:627 - 用户中断，正在退出...
