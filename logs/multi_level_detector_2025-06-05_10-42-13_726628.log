2025-06-05 10:42:13.729 | INFO     | __main__:__init__:41 - 初始化多级别线段终结判断系统，交易对: ETH/USDT
2025-06-05 10:42:17.638 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-05 10:42:17.638 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-05 10:42:17.638 | INFO     | multi_level_analysis:__init__:63 - 初始化 1m 级别分析器成功
2025-06-05 10:42:21.080 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-05 10:42:21.080 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-05 10:42:21.080 | INFO     | multi_level_analysis:__init__:63 - 初始化 5m 级别分析器成功
2025-06-05 10:42:25.620 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-05 10:42:25.621 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-05 10:42:25.621 | INFO     | multi_level_analysis:__init__:63 - 初始化 15m 级别分析器成功
2025-06-05 10:42:29.425 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-05 10:42:29.425 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-05 10:42:29.425 | INFO     | multi_level_analysis:__init__:63 - 初始化 30m 级别分析器成功
2025-06-05 10:42:33.110 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-05 10:42:33.110 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-05 10:42:33.110 | INFO     | multi_level_analysis:__init__:63 - 初始化 1h 级别分析器成功
2025-06-05 10:42:33.110 | INFO     | multi_level_analysis:__init__:70 - 多级别分析器初始化完成，监控级别: ['1m', '5m', '15m', '30m', '1h']
2025-06-05 10:42:33.111 | INFO     | notifier:_load_dingtalk_config_from_file:67 - 已从配置文件加载钉钉机器人配置
2025-06-05 10:42:33.111 | INFO     | notifier:__init__:51 - 初始化通知器，通知方式: ['log', 'file', 'dingtalk']
2025-06-05 10:42:33.111 | INFO     | __main__:__init__:59 - 多级别系统初始化完成
2025-06-05 10:42:33.111 | INFO     | __main__:signal_monitor:512 - 检查 ETH/USDT 的买卖点信号...
2025-06-05 10:42:33.111 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 1m 实时买卖点信号
2025-06-05 10:42:33.112 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 1m K线数据，数量: 200
2025-06-05 10:42:33.111 | INFO     | __main__:main:617 - 已启动 ETH/USDT 的买卖点信号监控，间隔: 5分钟
2025-06-05 10:42:33.232 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-05 10:42:33.244 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.248 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.286 | INFO     | chan_analysis:find_fractals:172 - 共找到 50 个分型
2025-06-05 10:42:33.286 | INFO     | chan_analysis:identify_bi:205 - 共识别出 40 个笔
2025-06-05 10:42:33.286 | INFO     | chan_analysis:identify_xd:294 - 共识别出 1 个线段
2025-06-05 10:42:33.287 | INFO     | chan_analysis:analyze:494 - 当前线段未终结
2025-06-05 10:42:33.290 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.291 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2558.8878, 止盈1=2653.8004584, 止盈2=2664.09623, 止盈3=2679.77507
2025-06-05 10:42:33.293 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.293 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2558.8878, 止盈1=2651.7388716, 止盈2=2662.0266450000004, 止盈3=2677.6933050000002
2025-06-05 10:42:33.295 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.296 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2558.8878, 止盈1=2653.8004584, 止盈2=2664.09623, 止盈3=2679.77507
2025-06-05 10:42:33.298 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.298 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2558.8878, 止盈1=2651.7388716, 止盈2=2662.0266450000004, 止盈3=2677.6933050000002
2025-06-05 10:42:33.300 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.301 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2558.8878, 止盈1=2653.8004584, 止盈2=2664.09623, 止盈3=2679.77507
2025-06-05 10:42:33.303 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.303 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2558.8878, 止盈1=2653.8004584, 止盈2=2664.09623, 止盈3=2679.77507
2025-06-05 10:42:33.305 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.306 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2558.8878, 止盈1=2651.7388716, 止盈2=2662.0266450000004, 止盈3=2677.6933050000002
2025-06-05 10:42:33.308 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.308 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2558.8878, 止盈1=2653.8004584, 止盈2=2664.09623, 止盈3=2679.77507
2025-06-05 10:42:33.308 | INFO     | multi_level_analysis:check_realtime_signals:1397 - ETH/USDT 1m 实时信号检查完成: 发现第三类买点，但信号已过时（12根K线前），建议观望
2025-06-05 10:42:33.309 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250605_104233.txt
2025-06-05 10:42:33.309 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-05 10:42:33.309 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 1m 买卖点信号通知
2025-06-05 10:42:33.309 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 5m 实时买卖点信号
2025-06-05 10:42:33.309 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 5m K线数据，数量: 200
2025-06-05 10:42:33.435 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-05 10:42:33.438 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.441 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.474 | INFO     | chan_analysis:find_fractals:172 - 共找到 53 个分型
2025-06-05 10:42:33.474 | INFO     | chan_analysis:identify_bi:205 - 共识别出 45 个笔
2025-06-05 10:42:33.474 | INFO     | chan_analysis:identify_xd:294 - 共识别出 3 个线段
2025-06-05 10:42:33.475 | INFO     | chan_analysis:analyze:494 - 当前线段未终结
2025-06-05 10:42:33.478 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.478 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2556.9964, 止盈1=2656.9728619999996, 止盈2=2682.758599999999, 止盈3=2708.804799999999
2025-06-05 10:42:33.480 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.480 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2635.8677, 止盈1=2557.8355770000003, 止盈2=2531.4769000000006, 止盈3=2505.3792000000008
2025-06-05 10:42:33.481 | INFO     | multi_level_analysis:check_realtime_signals:1397 - ETH/USDT 5m 实时信号检查完成: 买卖信号同时存在，买点综合评分显著更高（11.0 vs 4.5），第一类买点，强度: 11/10，止损: 2556.9964，止盈1: 2656.9728619999996，止盈2: 2682.758599999999，止盈3: 2708.804799999999
2025-06-05 10:42:33.481 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250605_104233.txt
2025-06-05 10:42:33.481 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-05 10:42:33.481 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 5m 买卖点信号通知
2025-06-05 10:42:33.481 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 15m 实时买卖点信号
2025-06-05 10:42:33.481 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 15m K线数据，数量: 200
2025-06-05 10:42:33.640 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-05 10:42:33.645 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.649 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.690 | INFO     | chan_analysis:find_fractals:172 - 共找到 52 个分型
2025-06-05 10:42:33.690 | INFO     | chan_analysis:identify_bi:205 - 共识别出 37 个笔
2025-06-05 10:42:33.690 | INFO     | chan_analysis:identify_xd:294 - 共识别出 7 个线段
2025-06-05 10:42:33.690 | INFO     | chan_analysis:analyze:494 - 当前线段未终结
2025-06-05 10:42:33.694 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.694 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2542.6198, 止盈1=2681.633429475, 止盈2=2783.8083, 止盈3=2852.1026625000004
2025-06-05 10:42:33.697 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.697 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2654.8557, 止盈1=2536.7489563500003, 止盈2=2432.8242000000005, 止盈3=2364.1557750000006
2025-06-05 10:42:33.699 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.700 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2542.6198, 止盈1=2681.633429475, 止盈2=2783.8083, 止盈3=2852.1026625000004
2025-06-05 10:42:33.702 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.702 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2654.8557, 止盈1=2536.778048175, 止盈2=2432.8521000000005, 止盈3=2364.182887500001
2025-06-05 10:42:33.705 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.705 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2654.8557, 止盈1=2536.7489563500003, 止盈2=2432.8242000000005, 止盈3=2364.1557750000006
2025-06-05 10:42:33.707 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.707 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2654.8557, 止盈1=2536.7489563500003, 止盈2=2432.8242000000005, 止盈3=2364.1557750000006
2025-06-05 10:42:33.709 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.710 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2654.8557, 止盈1=2536.778048175, 止盈2=2432.8521000000005, 止盈3=2364.182887500001
2025-06-05 10:42:33.712 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:33.712 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2542.6198, 止盈1=2705.453542, 止盈2=2808.536000000001, 止盈3=2877.4370000000013
2025-06-05 10:42:33.712 | INFO     | multi_level_analysis:check_realtime_signals:1397 - ETH/USDT 15m 实时信号检查完成: 买卖信号同时存在，买点综合评分显著更高（10.0 vs 5.5），第一类买点，强度: 10/10，止损: 2542.6198，止盈1: 2681.633429475，止盈2: 2783.8083，止盈3: 2852.1026625000004
2025-06-05 10:42:33.713 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250605_104233.txt
2025-06-05 10:42:34.252 | INFO     | notifier:_send_dingtalk:191 - 钉钉通知发送成功
2025-06-05 10:42:34.252 | INFO     | __main__:signal_monitor:596 - 已发送 ETH/USDT 15m 买卖点信号通知到钉钉
2025-06-05 10:42:34.253 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 15m 买卖点信号通知
2025-06-05 10:42:34.253 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 1h 实时买卖点信号
2025-06-05 10:42:34.253 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 1h K线数据，数量: 200
2025-06-05 10:42:34.391 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-05 10:42:34.398 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:34.404 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:34.445 | INFO     | chan_analysis:find_fractals:172 - 共找到 60 个分型
2025-06-05 10:42:34.445 | INFO     | chan_analysis:identify_bi:205 - 共识别出 51 个笔
2025-06-05 10:42:34.445 | INFO     | chan_analysis:identify_xd:294 - 共识别出 14 个线段
2025-06-05 10:42:34.446 | WARNING  | chan_analysis:analyze:492 - 检测到线段终结信号: {'终结': True, 'MACD背驰': True, '结构破坏': True, '均线突破': True, '形态变化': False, '成交量异常': False, '细节': {'MACD背驰': '价格创新高，但MACD未创新高', '结构破坏': '当前低点 2608.37 跌破了前期高点 2633.17', '均线突破': '收盘价跌破MA20均线'}, '原因': '满足3个终结条件'}
2025-06-05 10:42:34.450 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:34.450 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2752.2562080000002, 止盈2=3217.192400000001, 止盈3=3476.643400000001
2025-06-05 10:42:34.453 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:34.453 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2493.12, 止盈2=2014.0, 止盈3=1749.0
2025-06-05 10:42:34.455 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:34.455 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2493.12, 止盈2=2014.0, 止盈3=1749.0
2025-06-05 10:42:34.458 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:34.458 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2752.2562080000002, 止盈2=3217.192400000001, 止盈3=3476.643400000001
2025-06-05 10:42:34.461 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:34.461 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2752.2562080000002, 止盈2=3217.192400000001, 止盈3=3476.643400000001
2025-06-05 10:42:34.463 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-05 10:42:34.464 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2752.2562080000002, 止盈2=3217.192400000001, 止盈3=3476.643400000001
2025-06-05 10:42:34.464 | INFO     | multi_level_analysis:check_realtime_signals:1397 - ETH/USDT 1h 实时信号检查完成: 买卖信号均已过时（买:12根K线前，卖:16根K线前），建议观望
2025-06-05 10:42:34.464 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250605_104233.txt
2025-06-05 10:42:34.464 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-05 10:42:34.464 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 1h 买卖点信号通知
2025-06-05 10:44:52.469 | INFO     | __main__:main:627 - 用户中断，正在退出...
