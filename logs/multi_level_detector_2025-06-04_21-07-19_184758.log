2025-06-04 21:07:19.188 | INFO     | __main__:__init__:41 - 初始化多级别线段终结判断系统，交易对: ETH/USDT
2025-06-04 21:09:36.000 | WARNING  | data_fetcher:_init_exchange:95 - 初始化交易所 gate 时出错: gate GET https://api.gateio.ws/api/v4/futures/usdt/contracts
2025-06-04 21:09:36.003 | WARNING  | data_fetcher:__init__:55 - 初始化交易所 gate 失败，将不使用该交易所
2025-06-04 21:09:36.003 | ERROR    | data_fetcher:__init__:58 - 无法初始化任何交易所，请检查网络连接
2025-06-04 21:09:36.003 | ERROR    | multi_level_analysis:__init__:65 - 初始化 5m 级别分析器失败: 无法初始化任何交易所
2025-06-04 21:11:04.644 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-04 21:11:04.645 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-04 21:11:04.645 | INFO     | multi_level_analysis:__init__:63 - 初始化 15m 级别分析器成功
2025-06-04 21:11:23.280 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-04 21:11:23.280 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-04 21:11:23.280 | INFO     | multi_level_analysis:__init__:63 - 初始化 30m 级别分析器成功
2025-06-04 21:11:27.063 | INFO     | data_fetcher:__init__:53 - 成功初始化交易所: gate
2025-06-04 21:11:27.063 | INFO     | data_fetcher:__init__:61 - 数据获取器初始化完成，当前使用交易所: gate，监控交易对: ['ETH/USDT']
2025-06-04 21:11:27.063 | INFO     | multi_level_analysis:__init__:63 - 初始化 1h 级别分析器成功
2025-06-04 21:11:27.063 | INFO     | multi_level_analysis:__init__:70 - 多级别分析器初始化完成，监控级别: ['5m', '15m', '30m', '1h']
2025-06-04 21:11:27.065 | INFO     | notifier:_load_dingtalk_config_from_file:67 - 已从配置文件加载钉钉机器人配置
2025-06-04 21:11:27.065 | INFO     | notifier:__init__:51 - 初始化通知器，通知方式: ['log', 'file', 'dingtalk']
2025-06-04 21:11:27.065 | INFO     | __main__:__init__:59 - 多级别系统初始化完成
2025-06-04 21:11:27.065 | INFO     | __main__:signal_monitor:512 - 检查 ETH/USDT 的买卖点信号...
2025-06-04 21:11:27.065 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 5m 实时买卖点信号
2025-06-04 21:11:27.065 | INFO     | __main__:main:617 - 已启动 ETH/USDT 的买卖点信号监控，间隔: 5分钟
2025-06-04 21:11:27.067 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_211127.txt
2025-06-04 21:11:27.067 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-04 21:11:27.067 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 5m 买卖点信号通知
2025-06-04 21:11:27.067 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 15m 实时买卖点信号
2025-06-04 21:11:27.067 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 15m K线数据，数量: 200
2025-06-04 21:11:27.929 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-04 21:11:27.943 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:11:27.947 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:11:27.985 | INFO     | chan_analysis:find_fractals:172 - 共找到 51 个分型
2025-06-04 21:11:27.985 | INFO     | chan_analysis:identify_bi:205 - 共识别出 37 个笔
2025-06-04 21:11:27.985 | INFO     | chan_analysis:identify_xd:294 - 共识别出 5 个线段
2025-06-04 21:11:27.986 | WARNING  | chan_analysis:analyze:492 - 检测到线段终结信号: {'终结': True, 'MACD背驰': False, '结构破坏': True, '均线突破': True, '形态变化': False, '成交量异常': False, '细节': {'结构破坏': '当前低点 2609.47 跌破了前期高点 2644.51', '均线突破': '收盘价跌破MA20均线'}, '原因': '满足2个终结条件'}
2025-06-04 21:11:27.990 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:11:27.990 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 21:11:27.992 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:11:27.992 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 21:11:27.994 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:11:27.994 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 21:11:27.997 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:11:27.997 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 21:11:27.999 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:11:27.999 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2578.7621999999997, 止盈1=2686.8942, 止盈2=None, 止盈3=None
2025-06-04 21:11:27.999 | INFO     | multi_level_analysis:check_realtime_signals:1397 - ETH/USDT 15m 实时信号检查完成: 发现第三类买点，但信号已过时（15根K线前），建议观望
2025-06-04 21:11:28.000 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_211127.txt
2025-06-04 21:11:28.000 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-04 21:11:28.000 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 15m 买卖点信号通知
2025-06-04 21:11:28.000 | INFO     | multi_level_analysis:check_realtime_signals:913 - 开始检查 ETH/USDT 1h 实时买卖点信号
2025-06-04 21:11:28.000 | INFO     | data_fetcher:get_klines:139 - 从 gate 获取 ETH/USDT 1h K线数据，数量: 200
2025-06-04 21:11:28.100 | INFO     | data_fetcher:get_klines:157 - 成功获取 200 条K线数据
2025-06-04 21:11:28.104 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:11:28.107 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:11:28.143 | INFO     | chan_analysis:find_fractals:172 - 共找到 61 个分型
2025-06-04 21:11:28.144 | INFO     | chan_analysis:identify_bi:205 - 共识别出 52 个笔
2025-06-04 21:11:28.144 | INFO     | chan_analysis:identify_xd:294 - 共识别出 13 个线段
2025-06-04 21:11:28.144 | INFO     | chan_analysis:analyze:494 - 当前线段未终结
2025-06-04 21:11:28.148 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:11:28.148 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2672.8181999999997, 止盈2=None, 止盈3=None
2025-06-04 21:11:28.151 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:11:28.151 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2551.3124000000003, 止盈1=2672.8181999999997, 止盈2=None, 止盈3=None
2025-06-04 21:11:28.153 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:11:28.153 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 21:11:28.155 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:11:28.155 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 21:11:28.158 | INFO     | chan_analysis:preprocess_klines:65 - K线数据预处理完成，计算了MACD、均线等技术指标
2025-06-04 21:11:28.158 | INFO     | multi_level_analysis:_calculate_stop_levels:1466 - 使用缠论理论计算止盈止损: 止损=2646.6141, 止盈1=2603.38, 止盈2=2487.96, 止盈3=2365.686
2025-06-04 21:11:28.158 | INFO     | multi_level_analysis:check_realtime_signals:1397 - ETH/USDT 1h 实时信号检查完成: 买卖信号均已过时（买:13根K线前，卖:18根K线前），建议观望
2025-06-04 21:11:28.158 | INFO     | notifier:_send_file:129 - 通知已写入文件: notifications/notifications_20250604_211127.txt
2025-06-04 21:11:28.158 | INFO     | __main__:signal_monitor:598 - 无新鲜的买卖点信号，仅保存到文件
2025-06-04 21:11:28.158 | INFO     | __main__:signal_monitor:600 - 已发送 ETH/USDT 1h 买卖点信号通知
2025-06-04 21:15:16.261 | INFO     | __main__:main:627 - 用户中断，正在退出...
