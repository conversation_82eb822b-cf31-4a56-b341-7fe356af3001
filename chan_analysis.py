#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
缠论分析模块：
1. 实现K线的笔和线段划分
2. 判断线段终结条件
3. 基于缠论理论的止盈止损策略
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from loguru import logger
import ta  # 技术指标库

class ChanAnalysis:
    def __init__(self, df=None):
        """
        初始化缠论分析器
        
        Args:
            df: 包含K线数据的DataFrame，包含open, high, low, close, volume列
        """
        self.df = df
        self.fx_list = []  # 分型列表
        self.bi_list = []  # 笔列表
        self.xd_list = []  # 线段列表
        self.pivot_zones = []  # 中枢列表
        
        if df is not None:
            self.preprocess_klines()
    
    def set_klines(self, df):
        """设置K线数据并预处理"""
        self.df = df
        self.preprocess_klines()
    
    def preprocess_klines(self):
        """预处理K线数据，计算各种技术指标"""
        if self.df is None or self.df.empty:
            logger.error("K线数据为空，无法预处理")
            return
        
        # 首先确保数据类型正确
        self._ensure_numeric_data()
        
        # 计算MACD指标
        self.df['macd_diff'] = ta.trend.macd_diff(self.df['close'])
        self.df['macd_signal'] = ta.trend.macd_signal(self.df['close'])
        self.df['macd'] = ta.trend.macd(self.df['close'])
        
        # 计算均线
        self.df['ma5'] = ta.trend.sma_indicator(self.df['close'], window=5)
        self.df['ma10'] = ta.trend.sma_indicator(self.df['close'], window=10)
        self.df['ma20'] = ta.trend.sma_indicator(self.df['close'], window=20)
        
        # 计算成交量均线
        self.df['volume_ma5'] = ta.trend.sma_indicator(self.df['volume'], window=5)
        
        # 计算ATR
        self.df['atr'] = ta.volatility.average_true_range(
            self.df['high'], self.df['low'], self.df['close']
        )
        
        logger.info("K线数据预处理完成，计算了MACD、均线等技术指标")
    
    def _ensure_numeric_data(self):
        """
        确保DataFrame中的价格数据是数字类型
        """
        try:
            # 需要转换为数字的列
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            
            for col in numeric_columns:
                if col in self.df.columns:
                    # 检查当前数据类型
                    current_dtype = str(self.df[col].dtype)
                    
                    # 如果是字符串类型（包括Unicode字符串）
                    if (self.df[col].dtype == 'object' or 
                        current_dtype.startswith('<U') or 
                        current_dtype.startswith('U') or
                        current_dtype.startswith('S')):
                        
                        logger.warning("检测到 {} 列为字符串类型: {}，强制转换为数字类型".format(col, current_dtype))
                        
                        # 尝试多种转换方法
                        try:
                            # 方法1: 直接转换
                            self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
                        except:
                            try:
                                # 方法2: 先转为字符串再转数字
                                self.df[col] = pd.to_numeric(self.df[col].astype(str), errors='coerce')
                            except:
                                try:
                                    # 方法3: 逐个元素转换
                                    self.df[col] = self.df[col].apply(lambda x: float(x) if x != '' and x is not None else np.nan)
                                except:
                                    logger.error(f"无法转换 {col} 列，将使用默认值")
                                    self.df[col] = 0.0
                    
                    # 确保最终是float64类型
                    if self.df[col].dtype != 'float64':
                        self.df[col] = self.df[col].astype('float64', errors='ignore')
                    
                    # 验证转换结果
                    if not pd.api.types.is_numeric_dtype(self.df[col]):
                        logger.error(f"列 {col} 转换失败，最终类型: {self.df[col].dtype}")
                        # 强制设置为数字类型
                        self.df[col] = pd.Series([0.0] * len(self.df), dtype='float64')
            
            # 检查timestamp列
            if 'timestamp' in self.df.columns:
                if not pd.api.types.is_datetime64_any_dtype(self.df['timestamp']):
                    try:
                        self.df['timestamp'] = pd.to_datetime(self.df['timestamp'])
                    except Exception as e:
                        logger.warning(f"时间戳转换失败: {e}")
            
            # 删除包含NaN的行
            before_count = len(self.df)
            self.df = self.df.dropna(subset=numeric_columns)
            after_count = len(self.df)
            
            if before_count != after_count:
                logger.warning(f"删除了 {before_count - after_count} 行包含NaN的数据")
            
            # 重置索引
            self.df = self.df.reset_index(drop=True)
            
            # 验证数据类型
            for col in numeric_columns:
                if col in self.df.columns:
                    if not pd.api.types.is_numeric_dtype(self.df[col]):
                        logger.error(f"列 {col} 仍然不是数字类型: {self.df[col].dtype}")
                        raise ValueError(f"无法将 {col} 列转换为数字类型")
            
            logger.debug(f"数据类型转换成功，数据行数: {len(self.df)}")
            
        except Exception as e:
            logger.error("数据类型转换失败: {}".format(str(e)))
            raise
    
    def find_fractals(self, min_len=3):
        """
        寻找分型（顶分型和底分型）
        
        Args:
            min_len: 最小K线数量，默认为3
            
        Returns:
            list: 分型列表，每个分型为(index, type, price)元组，type为'top'或'bottom'
        """
        if self.df is None or len(self.df) < min_len:
            return []
        
        fx_list = []
        
        # 至少需要3根K线才能形成分型
        for i in range(min_len-1, len(self.df)-min_len+1):
            # 顶分型：中间K线的高点高于两侧K线的高点
            if all(self.df.iloc[i]['high'] > self.df.iloc[i+j]['high'] for j in range(-min_len+1, min_len) if j != 0):
                fx_list.append((i, 'top', self.df.iloc[i]['high']))
            
            # 底分型：中间K线的低点低于两侧K线的低点
            if all(self.df.iloc[i]['low'] < self.df.iloc[i+j]['low'] for j in range(-min_len+1, min_len) if j != 0):
                fx_list.append((i, 'bottom', self.df.iloc[i]['low']))
        
        self.fx_list = fx_list
        logger.info(f"共找到 {len(fx_list)} 个分型")
        return fx_list
    
    def identify_bi(self, min_bi_points=2):
        """
        识别笔
        
        Args:
            min_bi_points: 构成笔的最少分型数量，默认为2
            
        Returns:
            list: 笔列表，每个笔为(start_idx, end_idx, start_val, end_val, direction)
                 direction为'up'或'down'
        """
        if not self.fx_list:
            self.find_fractals()
        
        if len(self.fx_list) < min_bi_points:
            return []
        
        bi_list = []
        
        # 遍历相邻的分型对
        for i in range(len(self.fx_list) - 1):
            curr_fx = self.fx_list[i]
            next_fx = self.fx_list[i + 1]
            
            # 顶分型后必须是底分型，底分型后必须是顶分型
            if (curr_fx[1] == 'top' and next_fx[1] == 'bottom') or (curr_fx[1] == 'bottom' and next_fx[1] == 'top'):
                direction = 'down' if curr_fx[1] == 'top' else 'up'
                bi_list.append((curr_fx[0], next_fx[0], curr_fx[2], next_fx[2], direction))
        
        self.bi_list = bi_list
        logger.info(f"共识别出 {len(bi_list)} 个笔")
        return bi_list
    
    def identify_xd(self, min_xd_bi=3):
        """
        识别线段
        
        Args:
            min_xd_bi: 构成线段的最少笔数量，默认为3
            
        Returns:
            list: 线段列表，每个线段为(start_idx, end_idx, start_val, end_val, direction)
                 direction为'up'或'down'
        """
        if not self.bi_list:
            self.identify_bi()
        
        if len(self.bi_list) < min_xd_bi:
            logger.info(f"笔数量不足({len(self.bi_list)})，无法构成线段")
            return []
        
        xd_list = []
        
        # 简化的线段识别：基于笔的高低点突破
        # 线段是价格的主要运动方向，由多个笔组成
        
        i = 0
        while i < len(self.bi_list) - 2:  # 至少需要3个笔
            start_bi = self.bi_list[i]
            
            # 通过前几个笔确定初始方向
            if i + 2 < len(self.bi_list):
                # 比较第1个笔和第3个笔的价格来确定主要方向
                first_price = start_bi[3]  # 第1个笔的终点
                third_price = self.bi_list[i + 2][3]  # 第3个笔的终点
                
                if third_price > first_price:
                    direction = 'up'
                elif third_price < first_price:
                    direction = 'down'
                else:
                    # 价格相等，跳过
                    i += 1
                    continue
                
                # 寻找线段的结束点
                j = i + 2
                extreme_price = third_price
                end_idx = i + 2
                
                # 继续寻找同方向的延续
                while j + 1 < len(self.bi_list):
                    next_bi = self.bi_list[j + 1]
                    next_price = next_bi[3]
                    
                    if direction == 'up':
                        if next_price > extreme_price:
                            # 继续上升
                            extreme_price = next_price
                            end_idx = j + 1
                        elif next_price < extreme_price * 0.98:  # 下跌超过2%，可能线段结束
                            break
                    else:  # direction == 'down'
                        if next_price < extreme_price:
                            # 继续下降
                            extreme_price = next_price
                            end_idx = j + 1
                        elif next_price > extreme_price * 1.02:  # 上涨超过2%，可能线段结束
                            break
                    
                    j += 1
                
                # 创建线段
                if end_idx > i + 1:  # 确保线段包含足够的笔
                    end_bi = self.bi_list[end_idx]
                    xd_list.append((
                        start_bi[0],     # 起始K线索引
                        end_bi[1],       # 结束K线索引
                        start_bi[2],     # 起始价格
                        extreme_price,   # 结束价格（极值价格）
                        direction        # 方向
                    ))
                    i = end_idx
                else:
                    i += 1
            else:
                break
        
        self.xd_list = xd_list
        logger.info(f"共识别出 {len(xd_list)} 个线段")
        return xd_list
    
    def is_xd_ended(self, last_n_bars=5):
        """
        判断当前线段是否终结
        综合多种信号进行判断:
        1. MACD背驰
        2. 结构破坏
        3. 走势类型变化
        
        Args:
            last_n_bars: 检查最近的K线数量
            
        Returns:
            bool: 是否终结
            dict: 终结原因和详细信息
        """
        if not self.xd_list or len(self.df) < 20:
            return False, {"reason": "数据不足"}
        
        # 当前最新的线段
        curr_xd = self.xd_list[-1]
        direction = curr_xd[4]
        
        # 检查区间
        start_idx = max(0, len(self.df) - last_n_bars)
        check_df = self.df.iloc[start_idx:]
        
        result = {
            "终结": False,
            "MACD背驰": False,
            "结构破坏": False,
            "均线突破": False,
            "形态变化": False,
            "成交量异常": False,
            "细节": {}
        }
        
        # 1. 检查MACD背驰
        if direction == 'up':
            # 上升线段，检查是否有顶背驰
            if check_df['close'].iloc[-1] > check_df['close'].iloc[:-1].max():
                # 价格创新高
                if check_df['macd'].iloc[-1] < check_df['macd'].iloc[:-1].max():
                    # MACD没有创新高，背驰
                    result["MACD背驰"] = True
                    result["细节"]["MACD背驰"] = "价格创新高，但MACD未创新高"
        else:
            # 下降线段，检查是否有底背驰
            if check_df['close'].iloc[-1] < check_df['close'].iloc[:-1].min():
                # 价格创新低
                if check_df['macd'].iloc[-1] > check_df['macd'].iloc[:-1].min():
                    # MACD没有创新低，背驰
                    result["MACD背驰"] = True
                    result["细节"]["MACD背驰"] = "价格创新低，但MACD未创新低"
        
        # 2. 检查结构破坏
        if direction == 'up':
            # 上升线段，结构破坏是否跌破前高点
            last_low = self.df['low'].iloc[-1]
            prev_high = self.df['high'].iloc[:-3].tail(5).max()
            if last_low < prev_high:
                result["结构破坏"] = True
                result["细节"]["结构破坏"] = f"当前低点 {last_low} 跌破了前期高点 {prev_high}"
        else:
            # 下降线段，结构破坏是否突破前低点
            last_high = self.df['high'].iloc[-1]
            prev_low = self.df['low'].iloc[:-3].tail(5).min()
            if last_high > prev_low:
                result["结构破坏"] = True
                result["细节"]["结构破坏"] = f"当前高点 {last_high} 突破了前期低点 {prev_low}"
        
        # 3. 检查均线突破
        if direction == 'up':
            # 上升线段，是否跌破MA20
            if check_df['close'].iloc[-1] < check_df['ma20'].iloc[-1]:
                result["均线突破"] = True
                result["细节"]["均线突破"] = "收盘价跌破MA20均线"
        else:
            # 下降线段，是否突破MA20
            if check_df['close'].iloc[-1] > check_df['ma20'].iloc[-1]:
                result["均线突破"] = True
                result["细节"]["均线突破"] = "收盘价突破MA20均线"
        
        # 4. 检查成交量变化
        avg_vol = check_df['volume'].mean()
        last_vol = check_df['volume'].iloc[-1]
        
        if last_vol > avg_vol * 2:
            result["成交量异常"] = True
            result["细节"]["成交量异常"] = f"当前成交量 {last_vol} 是平均成交量 {avg_vol} 的2倍以上"
        
        # 综合判断
        # 根据缠论终结的多信号联动原则，要求满足至少两个条件
        conditions_met = sum([
            result["MACD背驰"],
            result["结构破坏"],
            result["均线突破"],
            result["成交量异常"]
        ])
        
        if conditions_met >= 2:
            result["终结"] = True
            result["原因"] = f"满足{conditions_met}个终结条件"
        
        return result["终结"], result
    
    def plot_analysis(self, save_path=None):
        """
        绘制分析结果，包括K线、分型、笔和线段
        
        Args:
            save_path: 保存路径，如果为None则自动生成
            
        Returns:
            str: 保存的图表路径
        """
        if self.df is None or self.df.empty:
            logger.error("无法绘制图表：数据为空")
            return None
        
        try:
            # 导入绘图模块
            from chart_plotter import ChartPlotter
            
            # 获取交易对信息和时间周期
            symbol = "未知交易对"
            timeframe = "未知周期"
            
            # 尝试从数据中获取交易对和时间周期信息
            if hasattr(self.df, 'name'):
                symbol = self.df.name
            elif 'symbol' in self.df.attrs:
                symbol = self.df.attrs['symbol']
                
            if 'timeframe' in self.df.attrs:
                timeframe = self.df.attrs['timeframe']
            
            # 创建绘图器
            plotter = ChartPlotter()
            
            # 绘制分析图表
            chart_path = plotter.plot_chan_analysis(
                df=self.df,
                symbol=symbol,
                timeframe=timeframe,
                fx_list=self.fx_list,
                bi_list=self.bi_list,
                xd_list=self.xd_list,
                show_ma=True,
                show_macd=True,
                save_path=save_path
            )
            
            logger.info(f"缠论分析图表已保存: {chart_path}")
            return chart_path
            
        except ImportError:
            logger.error("缺少绘图模块，请安装chart_plotter.py")
            return None
        except Exception as e:
            logger.error(f"绘制分析图表时出错: {str(e)}")
            return None
    
    def analyze(self, df=None):
        """
        一键分析K线数据
        
        Args:
            df: 如果提供，则使用该DataFrame代替之前设置的数据
            
        Returns:
            tuple: (是否终结, 终结原因)
        """
        if df is not None:
            self.set_klines(df)
        
        if self.df is None or self.df.empty:
            logger.error("没有数据可供分析")
            return False, {"reason": "没有数据"}
        
        # 步骤1: 预处理K线
        self.preprocess_klines()
        
        # 步骤2: 识别分型
        self.find_fractals()
        
        # 步骤3: 识别笔
        self.identify_bi()
        
        # 步骤4: 识别线段
        self.identify_xd()
        
        # 步骤5: 判断线段是否终结
        is_ended, reason = self.is_xd_ended()
        
        if is_ended:
            logger.warning(f"检测到线段终结信号: {reason}")
        else:
            logger.info("当前线段未终结")
        
        return is_ended, reason

    def calculate_chan_profit_targets(self, entry_point, signal_type, buy_sell_point_type, 
                                    related_pivot_idx=None, current_level='1h'):
        """
        基于缠论理论计算止盈止损位
        
        Args:
            entry_point: dict, 包含 {'index': int, 'price': float, 'timestamp': str}
            signal_type: str, 'buy' 或 'sell'
            buy_sell_point_type: int, 1, 2, 3 (第一、二、三类买卖点)
            related_pivot_idx: int, 相关中枢的索引
            current_level: str, 当前分析级别
            
        Returns:
            dict: 包含止损位和多个止盈位的详细信息
        """
        try:
            result = {
                'stop_loss': None,
                'profit_targets': [],
                'strategy_description': '',
                'risk_reward_ratio': None,
                'position_management': {}
            }
            
            entry_idx = entry_point['index']
            entry_price = entry_point['price']
            
            # 获取相关中枢信息
            related_pivot = None
            if related_pivot_idx is not None and related_pivot_idx < len(self.pivot_zones):
                related_pivot = self.pivot_zones[related_pivot_idx]
            
            if signal_type == 'buy':
                result = self._calculate_buy_targets_chan_theory(
                    entry_idx, entry_price, buy_sell_point_type, related_pivot, current_level
                )
            else:
                result = self._calculate_sell_targets_chan_theory(
                    entry_idx, entry_price, buy_sell_point_type, related_pivot, current_level
                )
            
            return result
            
        except Exception as e:
            logger.error(f"计算缠论止盈止损位时出错: {str(e)}")
            return self._get_fallback_targets(entry_price, signal_type)
    
    def _calculate_buy_targets_chan_theory(self, entry_idx, entry_price, point_type, related_pivot, level):
        """基于缠论理论计算买点的止盈止损策略"""
        result = {
            'stop_loss': None,
            'profit_targets': [],
            'strategy_description': '',
            'risk_reward_ratio': None,
            'position_management': {}
        }
        
        if point_type == 1:
            # 第一类买点：趋势转折点，风险最小，收益最大
            result = self._first_type_buy_strategy(entry_idx, entry_price, related_pivot, level)
            
        elif point_type == 2:
            # 第二类买点：中枢突破后的回调买点
            result = self._second_type_buy_strategy(entry_idx, entry_price, related_pivot, level)
            
        elif point_type == 3:
            # 第三类买点：中枢震荡中的买点
            result = self._third_type_buy_strategy(entry_idx, entry_price, related_pivot, level)
        
        return result
    
    def _first_type_buy_strategy(self, entry_idx, entry_price, related_pivot, level):
        """第一类买点策略：趋势的最后一跌"""
        result = {
            'profit_targets': [],
            'position_management': {
                'initial_position': 0.3,  # 初始仓位30%
                'add_position_levels': [],  # 加仓位置
                'max_position': 0.8  # 最大仓位80%
            }
        }
        
        # 止损：前期重要低点下方
        previous_important_low = self._find_previous_important_low(entry_idx, lookback=50)
        if previous_important_low:
            result['stop_loss'] = previous_important_low * 0.98  # 重要低点下方2%
        else:
            result['stop_loss'] = entry_price * 0.95  # 默认5%止损
        
        # 止盈策略：分级止盈
        # 第一目标：前期重要阻力位
        resistance_1 = self._find_next_resistance_level(entry_idx, entry_price)
        if resistance_1:
            result['profit_targets'].append({
                'price': resistance_1,
                'percentage': 0.3,  # 30%仓位止盈
                'description': '前期阻力位，减仓30%',
                'expected_return': (resistance_1 / entry_price - 1) * 100
            })
        
        # 第二目标：更高级别的中枢上沿
        if related_pivot:
            higher_pivot_high = self._find_higher_level_pivot_high(related_pivot, level)
            if higher_pivot_high and higher_pivot_high > entry_price * 1.05:
                result['profit_targets'].append({
                    'price': higher_pivot_high,
                    'percentage': 0.4,  # 40%仓位止盈
                    'description': '更高级别中枢上沿，减仓40%',
                    'expected_return': (higher_pivot_high / entry_price - 1) * 100
                })
        
        # 第三目标：趋势延续目标（斐波那契扩展）
        trend_target = self._calculate_trend_extension_target(entry_idx, entry_price, 'up')
        if trend_target:
            result['profit_targets'].append({
                'price': trend_target,
                'percentage': 0.3,  # 剩余30%仓位
                'description': '趋势延续目标，清仓',
                'expected_return': (trend_target / entry_price - 1) * 100
            })
        
        result['strategy_description'] = """
        第一类买点策略（趋势转折）：
        1. 这是风险最小、收益最大的买点
        2. 采用分批建仓，初始30%仓位
        3. 突破阻力位后可适当加仓
        4. 严格执行止损，保护本金
        5. 分级止盈，锁定利润
        """
        
        # 确保至少有三个止盈目标
        self._ensure_three_profit_targets(result, entry_price, 'buy')

        # 计算风险收益比
        if result['profit_targets'] and result['stop_loss']:
            avg_target = np.mean([t['price'] for t in result['profit_targets']])
            risk = entry_price - result['stop_loss']
            reward = avg_target - entry_price
            result['risk_reward_ratio'] = reward / risk if risk > 0 else None

        return result
    
    def _second_type_buy_strategy(self, entry_idx, entry_price, related_pivot, level):
        """第二类买点策略：中枢突破后的回调"""
        result = {
            'profit_targets': [],
            'position_management': {
                'initial_position': 0.5,  # 初始仓位50%
                'add_position_levels': [],
                'max_position': 0.7
            }
        }
        
        # 止损：中枢下沿下方
        if related_pivot:
            pivot_low = related_pivot[3]  # 中枢下沿
            result['stop_loss'] = pivot_low * 0.98
        else:
            result['stop_loss'] = entry_price * 0.97
        
        # 止盈策略
        # 第一目标：中枢上沿上方（确认突破）
        if related_pivot:
            pivot_high = related_pivot[2]  # 中枢上沿
            result['profit_targets'].append({
                'price': pivot_high * 1.02,
                'percentage': 0.3,
                'description': '确认突破中枢上沿，减仓30%',
                'expected_return': (pivot_high * 1.02 / entry_price - 1) * 100
            })
        
        # 第二目标：前期高点
        previous_high = self._find_previous_important_high(entry_idx, lookback=30)
        if previous_high and previous_high > entry_price * 1.03:
            result['profit_targets'].append({
                'price': previous_high,
                'percentage': 0.4,
                'description': '前期重要高点，减仓40%',
                'expected_return': (previous_high / entry_price - 1) * 100
            })
        
        # 第三目标：测量目标（中枢高度的延伸）
        if related_pivot:
            pivot_height = related_pivot[2] - related_pivot[3]  # 中枢高度
            measured_target = related_pivot[2] + pivot_height  # 中枢上沿 + 中枢高度
            if measured_target > entry_price * 1.05:
                result['profit_targets'].append({
                    'price': measured_target,
                    'percentage': 0.3,
                    'description': '测量目标（中枢高度延伸），清仓',
                    'expected_return': (measured_target / entry_price - 1) * 100
                })
        
        # 确保至少有三个止盈目标
        self._ensure_three_profit_targets(result, entry_price, 'buy')

        result['strategy_description'] = """
        第二类买点策略（中枢突破回调）：
        1. 中等风险，中等收益的买点
        2. 止损设在中枢下沿，风险可控
        3. 首要目标是确认突破有效性
        4. 适合稳健型投资者
        """

        return result
    
    def _third_type_buy_strategy(self, entry_idx, entry_price, related_pivot, level):
        """第三类买点策略：中枢震荡中的买点"""
        result = {
            'profit_targets': [],
            'position_management': {
                'initial_position': 0.3,  # 初始仓位30%
                'add_position_levels': [],
                'max_position': 0.5  # 最大仓位50%
            }
        }
        
        # 止损：中枢下沿下方（较紧）
        if related_pivot:
            pivot_low = related_pivot[3]
            result['stop_loss'] = pivot_low * 0.995  # 中枢下沿下方0.5%
        else:
            result['stop_loss'] = entry_price * 0.99
        
        # 止盈策略：快进快出
        # 第一目标：中枢中轴
        if related_pivot:
            pivot_center = (related_pivot[2] + related_pivot[3]) / 2
            if pivot_center > entry_price:
                result['profit_targets'].append({
                    'price': pivot_center,
                    'percentage': 0.5,
                    'description': '中枢中轴，减仓50%',
                    'expected_return': (pivot_center / entry_price - 1) * 100
                })
        
        # 第二目标：中枢上沿
        if related_pivot:
            pivot_high = related_pivot[2]
            result['profit_targets'].append({
                'price': pivot_high * 0.99,
                'percentage': 0.5,
                'description': '接近中枢上沿，清仓',
                'expected_return': (pivot_high * 0.99 / entry_price - 1) * 100
            })
        
        # 确保至少有三个止盈目标
        self._ensure_three_profit_targets(result, entry_price, 'buy')

        result['strategy_description'] = """
        第三类买点策略（中枢震荡）：
        1. 风险相对较高，收益有限
        2. 适合短线操作，快进快出
        3. 止损要严格，一旦跌破中枢立即止损
        4. 目标保守，以中枢上沿为主要目标
        """

        return result
    
    def _calculate_sell_targets_chan_theory(self, entry_idx, entry_price, point_type, related_pivot, level):
        """基于缠论理论计算卖点的止盈止损策略"""
        result = {
            'stop_loss': None,
            'profit_targets': [],
            'strategy_description': '',
            'risk_reward_ratio': None,
            'position_management': {}
        }

        if point_type == 1:
            # 第一类卖点：趋势转折点，风险最小，收益最大
            result = self._first_type_sell_strategy(entry_idx, entry_price, related_pivot, level)

        elif point_type == 2:
            # 第二类卖点：中枢突破后的反弹卖点
            result = self._second_type_sell_strategy(entry_idx, entry_price, related_pivot, level)

        elif point_type == 3:
            # 第三类卖点：中枢震荡中的卖点
            result = self._third_type_sell_strategy(entry_idx, entry_price, related_pivot, level)

        return result

    def _first_type_sell_strategy(self, entry_idx, entry_price, related_pivot, level):
        """第一类卖点策略：趋势的最后一涨"""
        result = {
            'profit_targets': [],
            'position_management': {
                'initial_position': 0.3,  # 初始仓位30%
                'add_position_levels': [],  # 加仓位置
                'max_position': 0.8  # 最大仓位80%
            }
        }

        # 止损：前期重要高点上方
        previous_important_high = self._find_previous_important_high(entry_idx, lookback=50)
        if previous_important_high:
            result['stop_loss'] = previous_important_high * 1.02  # 重要高点上方2%
        else:
            result['stop_loss'] = entry_price * 1.05  # 默认5%止损

        # 止盈策略：分级止盈
        # 第一目标：前期重要支撑位
        support_1 = self._find_next_support_level(entry_idx, entry_price)
        if support_1:
            result['profit_targets'].append({
                'price': support_1,
                'percentage': 0.3,  # 30%仓位止盈
                'description': '前期支撑位，减仓30%',
                'expected_return': (support_1 / entry_price - 1) * 100
            })

        # 第二目标：更高级别的中枢下沿
        if related_pivot:
            higher_pivot_low = self._find_higher_level_pivot_low(related_pivot, level)
            if higher_pivot_low and higher_pivot_low < entry_price * 0.95:
                result['profit_targets'].append({
                    'price': higher_pivot_low,
                    'percentage': 0.4,  # 40%仓位止盈
                    'description': '更高级别中枢下沿，减仓40%',
                    'expected_return': (higher_pivot_low / entry_price - 1) * 100
                })

        # 第三目标：趋势延伸目标
        trend_target = self._calculate_downtrend_target(entry_idx, entry_price, related_pivot)
        if trend_target and trend_target < entry_price * 0.90:
            result['profit_targets'].append({
                'price': trend_target,
                'percentage': 0.3,  # 剩余30%仓位止盈
                'description': '趋势延伸目标，清仓',
                'expected_return': (trend_target / entry_price - 1) * 100
            })

        # 如果没有足够的止盈目标，添加默认目标
        if len(result['profit_targets']) == 0:
            result['profit_targets'].append({
                'price': entry_price * 0.95,
                'percentage': 0.5,
                'description': '默认第一目标（5%下跌）',
                'expected_return': -5.0
            })

        if len(result['profit_targets']) == 1:
            result['profit_targets'].append({
                'price': entry_price * 0.90,
                'percentage': 0.3,
                'description': '默认第二目标（10%下跌）',
                'expected_return': -10.0
            })

        if len(result['profit_targets']) == 2:
            result['profit_targets'].append({
                'price': entry_price * 0.85,
                'percentage': 0.2,
                'description': '默认第三目标（15%下跌）',
                'expected_return': -15.0
            })

        result['strategy_description'] = '第一类卖点：趋势转折，分级减仓策略'
        result['risk_reward_ratio'] = abs(result['profit_targets'][0]['expected_return']) / 5.0 if result['profit_targets'] else 1.0

        return result

    def _second_type_sell_strategy(self, entry_idx, entry_price, related_pivot, level):
        """第二类卖点策略：中枢突破后的反弹卖点"""
        result = {
            'profit_targets': [],
            'position_management': {
                'initial_position': 0.4,  # 初始仓位40%
                'add_position_levels': [],
                'max_position': 0.6
            }
        }

        # 止损：中枢上沿上方
        if related_pivot:
            result['stop_loss'] = related_pivot[2] * 1.01  # 中枢上沿上方1%
        else:
            result['stop_loss'] = entry_price * 1.03  # 默认3%止损

        # 第一目标：中枢下沿
        if related_pivot:
            result['profit_targets'].append({
                'price': related_pivot[3],  # 中枢下沿
                'percentage': 0.4,
                'description': '中枢下沿，减仓40%',
                'expected_return': (related_pivot[3] / entry_price - 1) * 100
            })

        # 第二目标：前期低点
        previous_low = self._find_previous_important_low(entry_idx, lookback=30)
        if previous_low and previous_low < entry_price * 0.97:
            result['profit_targets'].append({
                'price': previous_low,
                'percentage': 0.4,
                'description': '前期重要低点，减仓40%',
                'expected_return': (previous_low / entry_price - 1) * 100
            })

        # 第三目标：测量目标（中枢高度的延伸）
        if related_pivot:
            pivot_height = related_pivot[2] - related_pivot[3]  # 中枢高度
            measured_target = related_pivot[3] - pivot_height  # 中枢下沿 - 中枢高度
            if measured_target > 0 and measured_target < entry_price * 0.95:
                result['profit_targets'].append({
                    'price': measured_target,
                    'percentage': 0.2,
                    'description': '测量目标（中枢高度延伸），清仓',
                    'expected_return': (measured_target / entry_price - 1) * 100
                })

        # 确保至少有三个止盈目标
        self._ensure_three_profit_targets(result, entry_price, 'sell')

        result['strategy_description'] = '第二类卖点：中枢突破失败，回调减仓策略'
        result['risk_reward_ratio'] = abs(result['profit_targets'][0]['expected_return']) / 3.0 if result['profit_targets'] else 1.0

        return result

    def _third_type_sell_strategy(self, entry_idx, entry_price, related_pivot, level):
        """第三类卖点策略：中枢震荡中的卖点"""
        result = {
            'profit_targets': [],
            'position_management': {
                'initial_position': 0.2,  # 初始仓位20%
                'add_position_levels': [],
                'max_position': 0.4
            }
        }

        # 止损：中枢上沿或近期高点
        if related_pivot:
            result['stop_loss'] = max(related_pivot[2], entry_price * 1.02)
        else:
            result['stop_loss'] = entry_price * 1.02  # 默认2%止损

        # 第一目标：中枢中位
        if related_pivot:
            pivot_mid = (related_pivot[2] + related_pivot[3]) / 2
            result['profit_targets'].append({
                'price': pivot_mid,
                'percentage': 0.3,
                'description': '中枢中位，减仓30%',
                'expected_return': (pivot_mid / entry_price - 1) * 100
            })

        # 第二目标：中枢下沿
        if related_pivot:
            result['profit_targets'].append({
                'price': related_pivot[3],
                'percentage': 0.4,
                'description': '中枢下沿，减仓40%',
                'expected_return': (related_pivot[3] / entry_price - 1) * 100
            })

        # 第三目标：中枢下沿下方
        if related_pivot:
            extended_target = related_pivot[3] * 0.98
            result['profit_targets'].append({
                'price': extended_target,
                'percentage': 0.3,
                'description': '中枢下沿下方，清仓',
                'expected_return': (extended_target / entry_price - 1) * 100
            })

        # 确保至少有三个止盈目标
        self._ensure_three_profit_targets(result, entry_price, 'sell')

        result['strategy_description'] = '第三类卖点：中枢震荡，快进快出策略'
        result['risk_reward_ratio'] = abs(result['profit_targets'][0]['expected_return']) / 2.0 if result['profit_targets'] else 1.0

        return result

    def _find_previous_important_low(self, entry_idx, lookback=50):
        """寻找前期重要低点"""
        if entry_idx < lookback:
            start_idx = 0
        else:
            start_idx = entry_idx - lookback
        
        # 在指定范围内寻找最低点
        low_prices = self.df['low'].iloc[start_idx:entry_idx]
        if not low_prices.empty:
            return low_prices.min()
        return None
    
    def _find_previous_important_high(self, entry_idx, lookback=30):
        """寻找前期重要高点"""
        if entry_idx < lookback:
            start_idx = 0
        else:
            start_idx = entry_idx - lookback
        
        high_prices = self.df['high'].iloc[start_idx:entry_idx]
        if not high_prices.empty:
            return high_prices.max()
        return None
    
    def _find_next_resistance_level(self, entry_idx, entry_price):
        """寻找下一个阻力位"""
        # 简化实现：寻找前期高点作为阻力位
        for i in range(max(0, entry_idx-100), entry_idx):
            if self.df['high'].iloc[i] > entry_price * 1.02:
                return self.df['high'].iloc[i]
        return entry_price * 1.05  # 默认5%目标
    
    def _find_higher_level_pivot_high(self, current_pivot, level):
        """寻找更高级别的中枢上沿"""
        # 简化实现：返回当前中枢上沿的1.1倍
        if current_pivot:
            return current_pivot[2] * 1.1
        return None

    def _find_next_support_level(self, entry_idx, entry_price):
        """寻找下一个支撑位"""
        # 寻找前期低点作为支撑位
        for i in range(max(0, entry_idx-100), entry_idx):
            if self.df['low'].iloc[i] < entry_price * 0.98:
                return self.df['low'].iloc[i]
        return entry_price * 0.95  # 默认5%目标

    def _find_higher_level_pivot_low(self, current_pivot, level):
        """寻找更高级别的中枢下沿"""
        # 简化实现：返回当前中枢下沿的0.9倍
        if current_pivot:
            return current_pivot[3] * 0.9
        return None

    def _calculate_downtrend_target(self, entry_idx, entry_price, related_pivot):
        """计算下跌趋势目标"""
        if related_pivot:
            # 基于中枢高度计算下跌目标
            pivot_height = related_pivot[2] - related_pivot[3]
            return related_pivot[3] - pivot_height * 1.5
        else:
            # 默认下跌目标
            return entry_price * 0.85

    def _ensure_three_profit_targets(self, result, entry_price, signal_type):
        """确保至少有三个止盈目标"""
        if signal_type == 'sell':
            # 为卖点添加默认目标
            default_targets = [
                (0.97, 0.3, '默认第一目标（3%下跌）', -3.0),
                (0.94, 0.4, '默认第二目标（6%下跌）', -6.0),
                (0.90, 0.3, '默认第三目标（10%下跌）', -10.0)
            ]
        else:
            # 为买点添加默认目标
            default_targets = [
                (1.03, 0.3, '默认第一目标（3%上涨）', 3.0),
                (1.06, 0.4, '默认第二目标（6%上涨）', 6.0),
                (1.10, 0.3, '默认第三目标（10%上涨）', 10.0)
            ]

        # 补充缺失的目标
        current_count = len(result['profit_targets'])
        for i in range(current_count, 3):
            if i < len(default_targets):
                multiplier, percentage, description, expected_return = default_targets[i]
                result['profit_targets'].append({
                    'price': entry_price * multiplier,
                    'percentage': percentage,
                    'description': description,
                    'expected_return': expected_return
                })
    
    def _calculate_trend_extension_target(self, entry_idx, entry_price, direction):
        """计算趋势延续目标"""
        # 使用斐波那契扩展计算目标位
        if direction == 'up':
            return entry_price * 1.15  # 简化为15%目标
        else:
            return entry_price * 0.85
    
    def _get_fallback_targets(self, entry_price, signal_type):
        """获取备用的简单止盈止损策略"""
        if signal_type == 'buy':
            return {
                'stop_loss': entry_price * 0.95,
                'profit_targets': [
                    {'price': entry_price * 1.05, 'percentage': 0.5, 'description': '5%目标'},
                    {'price': entry_price * 1.10, 'percentage': 0.5, 'description': '10%目标'}
                ],
                'strategy_description': '简化策略',
                'position_management': {'initial_position': 0.5}
            }
        else:
            return {
                'stop_loss': entry_price * 1.05,
                'profit_targets': [
                    {'price': entry_price * 0.95, 'percentage': 0.5, 'description': '5%目标'},
                    {'price': entry_price * 0.90, 'percentage': 0.5, 'description': '10%目标'}
                ],
                'strategy_description': '简化策略',
                'position_management': {'initial_position': 0.5}
            }


# 测试代码
if __name__ == "__main__":
    # 创建一些模拟数据
    import numpy as np
    
    # 创建模拟的上升-下降K线数据
    n = 100
    dates = pd.date_range('2023-01-01', periods=n, freq='5min')
    
    # 创建一个上升然后下降的价格序列
    prices = np.concatenate([
        np.linspace(10000, 12000, n//2),  # 上升阶段
        np.linspace(12000, 10500, n//2)   # 下降阶段
    ])
    
    # 添加一些随机波动
    np.random.seed(42)
    noise = np.random.normal(0, 100, n)
    prices = prices + noise
    
    # 创建OHLC数据
    ohlc = pd.DataFrame({
        'open': prices,
        'high': prices + np.random.normal(50, 30, n),
        'low': prices - np.random.normal(50, 30, n),
        'close': prices + np.random.normal(0, 20, n),
        'volume': np.random.normal(100, 50, n) * (1 + np.sin(np.linspace(0, 4*np.pi, n)))
    }, index=dates)
    
    # 确保high永远是最高价，low永远是最低价
    ohlc['high'] = ohlc[['open', 'high', 'close']].max(axis=1)
    ohlc['low'] = ohlc[['open', 'low', 'close']].min(axis=1)
    
    # 创建缠论分析器
    analyzer = ChanAnalysis(ohlc)
    
    # 执行分析
    is_ended, reason = analyzer.analyze()
    
    print(f"线段是否终结: {is_ended}")
    print(f"原因: {reason}")
    
    # 绘制分析结果
    analyzer.plot_analysis() 