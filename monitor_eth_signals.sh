#!/bin/bash

# 加载钉钉配置
if [ -f "dingtalk_config.json" ]; then
    echo "加载钉钉配置..."
    export DINGTALK_TOKEN=$(cat dingtalk_config.json | python -c "import sys, json; print(json.load(sys.stdin)['token'])")
    export DINGTALK_SECRET=$(cat dingtalk_config.json | python -c "import sys, json; print(json.load(sys.stdin)['secret'])")
fi

# 运行买卖点信号监控，检查ETH/USDT的5分钟、15分钟和1小时周期
python main.py --monitor-signals --symbols ETH/USDT --check-timeframes 5m,15m,1h --monitor-interval 3 --log-level INFO
