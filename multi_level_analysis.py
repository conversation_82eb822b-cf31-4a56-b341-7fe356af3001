#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多级别联立分析模块：
基于缠论理论，通过多个时间级别的联立分析来提高线段终结判断的准确率
"""

import pandas as pd
from loguru import logger
from typing import Dict, List, Tuple, Optional
from data_fetcher import DataFetcher
from chan_analysis import ChanAnalysis
from market_analyzer import MarketAnalyzer
from chart_plotter import ChartPlotter

class MultiLevelAnalysis:
    """
    多级别联立分析器
    
    根据缠论理论：
    1. 大级别决定方向，小级别决定进出点
    2. 多级别共振时，信号更可靠
    3. 级别间的背驰关系是关键判断依据
    """
    
    # 定义级别层次关系（从小到大）
    TIMEFRAME_HIERARCHY = {
        '1m': 1,
        '5m': 5,
        '15m': 15,
        '30m': 30,
        '1h': 60,
        '4h': 240,
        '1d': 1440
    }
    
    def __init__(self, symbol="BTC/USDT", exchange_id=None):
        """
        初始化多级别分析器
        
        参数:
            symbol: 交易对
            exchange_id: 交易所ID
        """
        self.symbol = symbol
        self.exchange_id = exchange_id
        
        # 定义要分析的时间级别（从小到大）
        self.timeframes = ['5m', '15m', '30m', '1h']
        
        # 为每个级别创建数据获取器和分析器
        self.data_fetchers = {}
        self.analyzers = {}
        
        for tf in self.timeframes:
            try:
                self.data_fetchers[tf] = DataFetcher(
                    symbols=symbol, 
                    timeframe=tf, 
                    exchange_id=exchange_id
                )
                self.analyzers[tf] = ChanAnalysis()
                logger.info("初始化 {} 级别分析器成功".format(tf))
            except Exception as e:
                logger.error("初始化 {} 级别分析器失败: {}".format(tf, str(e)))
        
        # 初始化市场分析器
        self.market_analyzer = MarketAnalyzer()
        
        logger.info("多级别分析器初始化完成，监控级别: {}".format(self.timeframes))
    
    def get_multi_level_data(self, limit=200):
        """
        获取多个级别的K线数据
        
        参数:
            limit: 每个级别获取的K线数量
            
        返回:
            Dict[str, pd.DataFrame]: 各级别的K线数据
        """
        multi_data = {}
        
        for tf in self.timeframes:
            try:
                if tf in self.data_fetchers:
                    df = self.data_fetchers[tf].get_klines(symbol=self.symbol, limit=limit)
                    if not df.empty:
                        # 确保数据类型正确
                        df = self._ensure_numeric_data(df)
                        if not df.empty:  # 检查数据转换后是否为空
                            multi_data[tf] = df
                            logger.debug("获取 {} 级别数据成功，数量: {}".format(tf, len(df)))
                        else:
                            logger.warning("获取 {} 级别数据转换后为空".format(tf))
                    else:
                        logger.warning("获取 {} 级别数据为空".format(tf))
                else:
                    logger.warning("未找到 {} 级别的数据获取器".format(tf))
            except Exception as e:
                logger.error("获取 {} 级别数据失败: {}".format(tf, str(e)))
        
        return multi_data
    
    def _ensure_numeric_data(self, df):
        """
        确保DataFrame中的价格数据是数字类型
        
        参数:
            df: 原始DataFrame
            
        返回:
            pd.DataFrame: 转换后的DataFrame
        """
        try:
            df_copy = df.copy()
            
            # 需要转换为数字的列
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            
            for col in numeric_columns:
                if col in df_copy.columns:
                    # 转换为数字类型，无法转换的设为NaN
                    df_copy[col] = pd.to_numeric(df_copy[col], errors='coerce')
            
            # 删除包含NaN的行
            df_copy = df_copy.dropna(subset=numeric_columns)
            
            # 重置索引
            df_copy = df_copy.reset_index(drop=True)
            
            return df_copy
            
        except Exception as e:
            logger.error("数据类型转换失败: {}".format(str(e)))
            return pd.DataFrame()  # 返回空DataFrame
    
    def analyze_single_level(self, timeframe, df):
        """
        分析单个级别的线段状态
        
        参数:
            timeframe: 时间级别
            df: K线数据
            
        返回:
            Dict: 分析结果
        """
        try:
            analyzer = self.analyzers[timeframe]
            
            # 调整数据格式
            kline_data = df.copy()
            if 'timestamp' in kline_data.columns and 'datetime' not in kline_data.columns:
                kline_data['datetime'] = kline_data['timestamp']
                kline_data.set_index('datetime', inplace=True)
            
            # 执行分析
            is_ended, details = analyzer.analyze(kline_data)
            
            # 获取当前走势方向
            current_trend = self._get_current_trend(analyzer)
            
            # 获取背驰信息
            divergence_info = self._check_divergence(analyzer)
            
            result = {
                'timeframe': timeframe,
                'segment_ended': is_ended,
                'details': details,
                'current_trend': current_trend,
                'divergence': divergence_info,
                'last_price': df['close'].iloc[-1] if not df.empty else None,
                'data_quality': len(df)
            }
            
            logger.debug("{} 级别分析完成: 终结={}, 趋势={}".format(timeframe, is_ended, current_trend))
            return result
            
        except Exception as e:
            logger.error("分析 {} 级别时出错: {}".format(timeframe, str(e)))
            return {
                'timeframe': timeframe,
                'segment_ended': False,
                'details': {'error': str(e)},
                'current_trend': 'unknown',
                'divergence': {},
                'last_price': None,
                'data_quality': 0
            }
    
    def _get_current_trend(self, analyzer):
        """
        获取当前走势方向
        
        参数:
            analyzer: 缠论分析器
            
        返回:
            str: 'up', 'down', 'sideways', 'unknown'
        """
        try:
            if hasattr(analyzer, 'xd_list') and analyzer.xd_list:
                # 获取最后一个线段的方向
                last_xd = analyzer.xd_list[-1]
                return last_xd[4] if len(last_xd) > 4 else 'unknown'
            elif hasattr(analyzer, 'bi_list') and analyzer.bi_list:
                # 如果没有线段，看笔的方向
                last_bi = analyzer.bi_list[-1]
                return last_bi[4] if len(last_bi) > 4 else 'unknown'
            else:
                return 'unknown'
        except Exception as e:
            logger.debug("获取趋势方向时出错: {}".format(str(e)))
            return 'unknown'
    
    def _check_divergence(self, analyzer):
        """
        检查背驰情况
        
        参数:
            analyzer: 缠论分析器
            
        返回:
            Dict: 背驰信息
        """
        try:
            divergence_info = {
                'macd_divergence': False,
                'price_divergence': False,
                'volume_divergence': False,
                'strength': 0  # 背驰强度 0-3
            }
            
            if hasattr(analyzer, 'df') and analyzer.df is not None:
                df = analyzer.df
                
                # 检查MACD背驰
                if 'macd' in df.columns:
                    recent_macd = df['macd'].tail(10)
                    if len(recent_macd) >= 5:
                        # 简化的背驰检查：最近的MACD值与价格走势相反
                        if hasattr(analyzer, 'xd_list') and analyzer.xd_list:
                            last_xd = analyzer.xd_list[-1]
                            direction = last_xd[4]
                            
                            if direction == 'up' and recent_macd.iloc[-1] < recent_macd.iloc[-5:].mean():
                                # 上升走势但MACD走弱
                                divergence_info['macd_divergence'] = True
                                divergence_info['strength'] += 1
                            elif direction == 'down' and recent_macd.iloc[-1] > recent_macd.iloc[-5:].mean():
                                # 下降走势但MACD走强
                                divergence_info['macd_divergence'] = True
                                divergence_info['strength'] += 1
                
                # 检查量价背驰
                if 'volume' in df.columns and len(df) >= 10:
                    recent_volume = df['volume'].tail(10)
                    if len(recent_volume) >= 5:
                        # 检查近期成交量是否异常
                        avg_volume = recent_volume.iloc[:-1].mean()
                        current_volume = recent_volume.iloc[-1]
                        
                        if current_volume > avg_volume * 1.5:
                            # 成交量显著放大
                            divergence_info['volume_divergence'] = True
                            divergence_info['strength'] += 1
                
                # 检查价格背驰（高点之间或低点之间的背驰）
                if hasattr(analyzer, 'xd_list') and len(analyzer.xd_list) >= 2:
                    last_xd = analyzer.xd_list[-1]
                    prev_xd = analyzer.xd_list[-2]
                    
                    # 获取最近的两个线段方向
                    direction = last_xd[4]
                    
                    if direction == prev_xd[4]:  # 同向线段
                        if direction == 'up':
                            # 检查上升线段高点的背驰
                            if last_xd[3] > prev_xd[3]:  # 价格创新高
                                # 但动能减弱
                                if df['macd'].iloc[-1] < df['macd'].iloc[-10:-1].max():
                                    divergence_info['price_divergence'] = True
                                    divergence_info['strength'] += 1
                        else:  # 下降线段
                            # 检查下降线段低点的背驰
                            if last_xd[3] < prev_xd[3]:  # 价格创新低
                                # 但动能减弱
                                if df['macd'].iloc[-1] > df['macd'].iloc[-10:-1].min():
                                    divergence_info['price_divergence'] = True
                                    divergence_info['strength'] += 1
            
            return divergence_info
                
        except Exception as e:
            logger.debug("检查背驰时出错: {}".format(str(e)))
            return {
                'macd_divergence': False,
                'price_divergence': False,
                'volume_divergence': False,
                'strength': 0
            }
    
    def multi_level_judgment(self):
        """
        多级别联立判断
        
        返回:
            Dict: 综合判断结果
        """
        logger.info("开始多级别联立分析")
        
        # 获取多级别数据
        multi_data = self.get_multi_level_data()
        
        if not multi_data:
            logger.error("未能获取任何级别的数据")
            return {
                'final_judgment': False,
                'confidence': 0,
                'reason': '无法获取数据',
                'level_results': {}
            }
        
        # 分析各个级别
        level_results = {}
        for tf, df in multi_data.items():
            level_results[tf] = self.analyze_single_level(tf, df)
        
        # 多级别联立判断
        final_result = self._synthesize_multi_level_results(level_results)
        
        logger.info("多级别联立分析完成，最终判断: {}, 置信度: {}".format(final_result['final_judgment'], final_result['confidence']))
        
        return final_result
    
    def _synthesize_multi_level_results(self, level_results):
        """
        综合多级别分析结果
        
        参数:
            level_results: 各级别分析结果
            
        返回:
            Dict: 综合判断结果
        """
        # 初始化结果
        synthesis = {
            'final_judgment': False,
            'confidence': 0,
            'reason': '',
            'level_results': level_results,
            'consensus_levels': [],
            'divergent_levels': [],
            'trend_consistency': False
        }
        
        # 统计各级别的终结信号
        ended_levels = []
        not_ended_levels = []
        trends = []
        total_divergence_strength = 0
        
        for tf, result in level_results.items():
            if result['segment_ended']:
                ended_levels.append(tf)
            else:
                not_ended_levels.append(tf)
            
            trends.append(result['current_trend'])
            total_divergence_strength += result['divergence']['strength']
        
        # 计算级别权重（大级别权重更高）
        level_weights = {}
        for tf in level_results.keys():
            level_weights[tf] = self.TIMEFRAME_HIERARCHY.get(tf, 1)
        
        # 加权计算终结信号强度
        weighted_ended_score = sum(level_weights[tf] for tf in ended_levels)
        total_weight = sum(level_weights.values())
        
        # 趋势一致性检查
        unique_trends = set([t for t in trends if t != 'unknown'])
        trend_consistency = len(unique_trends) <= 1
        synthesis['trend_consistency'] = trend_consistency
        
        # 综合判断逻辑
        confidence = 0
        reasons = []
        
        # 1. 多级别共振判断
        if len(ended_levels) >= 2:
            confidence += 30
            reasons.append("{}个级别出现终结信号".format(len(ended_levels)))
            synthesis['consensus_levels'] = ended_levels
        
        # 2. 大级别权重判断
        if weighted_ended_score / total_weight > 0.5:
            confidence += 25
            reasons.append("大级别终结信号权重较高")
        
        # 3. 背驰强度判断
        if total_divergence_strength >= 3:
            confidence += 20
            reasons.append("多级别背驰强度较高({})".format(total_divergence_strength))
        
        # 4. 趋势一致性加分
        if trend_consistency:
            confidence += 15
            reasons.append("多级别趋势方向一致")
        else:
            synthesis['divergent_levels'] = list(level_results.keys())
        
        # 5. 特殊情况：大级别（1h）终结信号
        if '1h' in ended_levels:
            confidence += 20
            reasons.append("1小时级别出现终结信号")
        
        # 6. 特殊情况：小级别（5m）与大级别方向一致
        if '5m' in level_results and '1h' in level_results:
            if (level_results['5m']['segment_ended'] and 
                level_results['5m']['current_trend'] == level_results['1h']['current_trend']):
                confidence += 10
                reasons.append("小级别与大级别方向一致")
        
        # 最终判断
        synthesis['confidence'] = min(confidence, 100)  # 置信度不超过100
        synthesis['final_judgment'] = confidence >= 60  # 置信度60以上才判断为终结
        synthesis['reason'] = '; '.join(reasons) if reasons else '信号强度不足'
        
        return synthesis
    
    def get_trading_suggestion(self, judgment_result):
        """
        基于多级别分析结果给出交易建议
        
        参数:
            judgment_result: 多级别判断结果
            
        返回:
            Dict: 交易建议
        """
        suggestion = {
            'action': 'hold',  # hold, buy, sell
            'confidence': judgment_result['confidence'],
            'risk_level': 'medium',  # low, medium, high
            'stop_loss': None,
            'take_profit': None,
            'reasoning': ''
        }
        
        if judgment_result['final_judgment']:
            # 有终结信号
            level_results = judgment_result['level_results']
            
            # 判断主要趋势方向
            trends = [r['current_trend'] for r in level_results.values() if r['current_trend'] != 'unknown']
            if trends:
                main_trend = max(set(trends), key=trends.count)
                
                if main_trend == 'up':
                    suggestion['action'] = 'sell'
                    suggestion['reasoning'] = '上升线段终结，建议卖出'
                elif main_trend == 'down':
                    suggestion['action'] = 'buy'
                    suggestion['reasoning'] = '下降线段终结，建议买入'
                
                # 风险等级评估
                if judgment_result['confidence'] >= 80:
                    suggestion['risk_level'] = 'low'
                elif judgment_result['confidence'] >= 60:
                    suggestion['risk_level'] = 'medium'
                else:
                    suggestion['risk_level'] = 'high'
        
        return suggestion

    def comprehensive_market_analysis(self, timeframe='15m', limit=200):
        """
        综合市场分析：结合多级别分析和市场分析器
        
        参数:
            timeframe: 主要分析时间周期
            limit: K线数据数量
            
        返回:
            dict: 综合分析结果
        """
        try:
            logger.info("开始执行 {} 综合市场分析".format(self.symbol))
            
            # 1. 获取多级别数据
            multi_data = self.get_multi_level_data(limit=limit)
            if not multi_data:
                logger.error("无法获取 {} 的多级别K线数据".format(self.symbol))
                return None
            
            # 2. 对每个时间周期执行缠论分析和买卖点识别
            multi_level_chan_analysis = {}
            multi_level_market_analysis = {}
            
            for tf, df in multi_data.items():
                try:
                    logger.info("分析 {} 级别数据".format(tf))
                    
                    # 执行缠论分析
                    analyzer = self.analyzers[tf]
                    analyzer.set_klines(df)
                    
                    # 获取分型、笔、线段
                    fx_list = analyzer.find_fractals()
                    bi_list = analyzer.identify_bi()
                    xd_list = analyzer.identify_xd()
                    
                    # 识别中枢和买卖点
                    try:
                        plotter = ChartPlotter()
                        
                        # 识别中枢区域
                        pivot_zones = plotter.identify_pivot_zones(bi_list, df)
                        
                        # 识别各类买卖点
                        buy_points1, sell_points1 = plotter.identify_first_buy_sell_points(bi_list, df, pivot_zones)
                        buy_points2, sell_points2 = plotter.identify_second_buy_sell_points(bi_list, df, pivot_zones)
                        buy_points3, sell_points3 = plotter.identify_third_buy_sell_points(bi_list, df, pivot_zones)
                        
                        # 汇总所有买卖点
                        buy_points_all = {
                            '1': (buy_points1, sell_points1),
                            '2': (buy_points2, sell_points2), 
                            '3': (buy_points3, sell_points3)
                        }
                        sell_points_all = buy_points_all  # 卖点包含在buy_points_all中
                        
                    except ImportError:
                        logger.error("无法导入ChartPlotter，请确保chart_plotter.py文件存在并可以导入")
                        # 设置空的买卖点和中枢
                        pivot_zones = []
                        buy_points_all = {'1': ([], []), '2': ([], []), '3': ([], [])}
                        sell_points_all = buy_points_all
                    except Exception as e:
                        logger.error("ChartPlotter识别买卖点时出错: {}".format(str(e)))
                        # 设置空的买卖点和中枢
                        pivot_zones = []
                        buy_points_all = {'1': ([], []), '2': ([], []), '3': ([], [])}
                        sell_points_all = buy_points_all
                    
                    # 保存缠论分析结果
                    multi_level_chan_analysis[tf] = {
                        'fractal_count': len(fx_list),
                        'stroke_count': len(bi_list),
                        'segment_count': len(xd_list),
                        'pivot_count': len(pivot_zones),
                        'latest_trend': self._get_current_trend(analyzer),
                        'fx_list': fx_list,
                        'bi_list': bi_list,
                        'xd_list': xd_list,
                        'pivot_zones': pivot_zones
                    }
                    
                    # 使用MarketAnalyzer进行市场状态分析
                    market_analysis = self.market_analyzer.analyze_market_status(
                        df, bi_list, pivot_zones, buy_points_all, sell_points_all
                    )
                    multi_level_market_analysis[tf] = market_analysis
                    
                    logger.info("{} 级别分析完成".format(tf))
                    
                except Exception as e:
                    logger.error("分析 {} 级别时出错: {}".format(tf, str(e)))
                    # 即使某个级别分析失败，也继续分析其他级别
                    multi_level_chan_analysis[tf] = {
                        'fractal_count': 0,
                        'stroke_count': 0,
                        'segment_count': 0,
                        'pivot_count': 0,
                        'latest_trend': 'unknown',
                        'error': str(e)
                    }
                    multi_level_market_analysis[tf] = {
                        'buy_signals': [],
                        'sell_signals': [],
                        'operation_advice': {'action': 'wait', 'confidence': 0},
                        'error': str(e)
                    }
            
            # 3. 执行多级别联立分析
            multi_level_result = self.multi_level_judgment()
            
            # 4. 获取主要时间周期的当前价格
            main_df = multi_data.get(timeframe)
            if main_df is None:
                # 如果主要时间周期数据不存在，使用第一个可用的时间周期
                main_df = next(iter(multi_data.values()))
                timeframe = next(iter(multi_data.keys()))
            
            current_price = main_df['close'].iloc[-1]
            
            # 5. 综合分析结果
            comprehensive_result = {
                'symbol': self.symbol,
                'timeframe': timeframe,
                'timestamp': pd.Timestamp.now(),
                'current_price': current_price,
                
                # 多级别缠论分析结果
                'multi_level_chan_analysis': multi_level_chan_analysis,
                
                # 多级别市场分析结果
                'multi_level_market_analysis': multi_level_market_analysis,
                
                # 多级别联立分析结果
                'multi_level_analysis': multi_level_result,
                
                # 综合交易建议（基于主要时间周期的市场分析）
                'comprehensive_advice': self._generate_comprehensive_advice(
                    multi_level_market_analysis.get(timeframe, {}), 
                    multi_level_result, 
                    current_price
                )
            }
            
            logger.info("{} 综合市场分析完成".format(self.symbol))
            return comprehensive_result
            
        except Exception as e:
            logger.error("综合市场分析失败: {}".format(str(e)))
            return None
    
    def _generate_comprehensive_advice(self, market_analysis, multi_level_result, current_price):
        """
        生成综合交易建议
        
        参数:
            market_analysis: 市场分析结果
            multi_level_result: 多级别分析结果
            current_price: 当前价格
            
        返回:
            dict: 综合交易建议
        """
        advice = {
            'final_action': 'wait',
            'confidence': 0,
            'reasoning': [],
            'risk_level': 'medium',
            'entry_price': current_price,
            'stop_loss': None,
            'take_profit': None,
            'position_size': 'light'
        }
        
        # 获取市场分析建议
        market_advice = market_analysis['operation_advice']
        
        # 获取多级别分析建议
        multi_level_advice = self.get_trading_suggestion(multi_level_result)
        
        # 综合判断逻辑
        confidence_score = 0
        reasons = []
        
        # 1. 市场分析权重 (40%)
        if market_advice['action'] != 'wait':
            confidence_score += market_advice['confidence'] * 0.4
            reasons.append("市场分析建议{}，{}".format(market_advice['action'], market_advice['description']))
            advice['final_action'] = market_advice['action']
            advice['stop_loss'] = market_advice['stop_loss']
            advice['take_profit'] = market_advice['take_profit_conservative']
            advice['position_size'] = market_advice['position_size']
        
        # 2. 多级别分析权重 (35%)
        if multi_level_result['final_judgment']:
            confidence_score += multi_level_result['confidence'] * 0.35
            reasons.append("多级别分析显示线段终结，置信度{}%".format(multi_level_result['confidence']))
            
            # 如果多级别分析与市场分析方向一致，增加置信度
            if multi_level_advice['action'] == market_advice['action']:
                confidence_score += 15
                reasons.append("多级别分析与市场分析方向一致")
            elif multi_level_advice['action'] != 'hold':
                # 如果方向不一致，降低置信度并建议等待
                confidence_score -= 20
                reasons.append("多级别分析与市场分析方向不一致，建议谨慎")
                advice['final_action'] = 'wait'
        
        # 3. 趋势一致性加分 (15%)
        if multi_level_result.get('trend_consistency', False):
            confidence_score += 15
            reasons.append("多级别趋势一致")
        
        # 4. 信号强度评估 (10%)
        strong_signals = len([s for s in market_analysis['buy_signals'] + market_analysis['sell_signals'] 
                            if s['strength'] >= 8])
        if strong_signals > 0:
            confidence_score += 10
            reasons.append("发现{}个强信号".format(strong_signals))
        
        # 最终建议
        advice['confidence'] = min(int(confidence_score), 100)
        advice['reasoning'] = reasons
        
        # 风险等级评估
        if advice['confidence'] >= 80:
            advice['risk_level'] = 'low'
        elif advice['confidence'] >= 60:
            advice['risk_level'] = 'medium'
        else:
            advice['risk_level'] = 'high'
            
        # 如果置信度太低，建议等待
        if advice['confidence'] < 50:
            advice['final_action'] = 'wait'
            advice['reasoning'].append("综合置信度不足，建议等待更明确信号")
        
        return advice
    
    def print_comprehensive_report(self, analysis_result):
        """
        打印综合分析报告
        
        参数:
            analysis_result: 综合分析结果
        """
        if not analysis_result:
            print("❌ 分析结果为空")
            return
        
        print("\n" + "="*100)
        print("📊 综合市场分析报告")
        print("="*100)
        
        print(f"🏷️  交易对: {analysis_result['symbol']}")
        print(f"⏰ 分析时间: {analysis_result['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💰 当前价格: {analysis_result['current_price']:.4f}")
        print(f"📈 主要周期: {analysis_result['timeframe']}")
        
        # 缠论分析摘要
        chan = analysis_result['multi_level_chan_analysis']
        print(f"\n📐 缠论分析摘要:")
        for tf, analysis in chan.items():
            if 'error' in analysis:
                print(f"   {tf}: ❌ 分析失败 - {analysis['error']}")
            else:
                print(f"   {tf}:")
                print(f"     分型: {analysis['fractal_count']} | 笔: {analysis['stroke_count']} | 线段: {analysis['segment_count']} | 中枢: {analysis['pivot_count']}")
                print(f"     当前趋势: {analysis['latest_trend']}")
        
        # 多级别分析摘要
        multi = analysis_result['multi_level_analysis']
        print(f"\n🔄 多级别分析:")
        print(f"   线段终结: {'✅ 是' if multi['final_judgment'] else '❌ 否'}")
        print(f"   置信度: {multi['confidence']}%")
        print(f"   趋势一致: {'✅ 是' if multi['trend_consistency'] else '❌ 否'}")
        print(f"   判断理由: {multi['reason']}")
        
        # 市场分析详情
        market = analysis_result['multi_level_market_analysis']
        print(f"\n📊 市场分析详情:")
        
        for tf, analysis in market.items():
            if 'error' in analysis:
                print(f"   {tf}: ❌ 分析失败 - {analysis['error']}")
                continue
                
            print(f"\n   📈 {tf} 级别:")
            
            # 趋势分析
            if 'trend_analysis' in analysis:
                trend = analysis['trend_analysis']
                print(f"     趋势: {trend['description']}")
            
            # 买卖信号
            if analysis.get('buy_signals'):
                print(f"     🟢 买入信号 ({len(analysis['buy_signals'])}个):")
                for i, signal in enumerate(analysis['buy_signals'][:3]):
                    print(f"        {i+1}. {signal['type']} - 强度:{signal['strength']}/10 - "
                          f"价格:{signal['price']:.4f} - {signal['time_ago_minutes']:.0f}分钟前")
            
            if analysis.get('sell_signals'):
                print(f"     🔴 卖出信号 ({len(analysis['sell_signals'])}个):")
                for i, signal in enumerate(analysis['sell_signals'][:3]):
                    print(f"        {i+1}. {signal['type']} - 强度:{signal['strength']}/10 - "
                          f"价格:{signal['price']:.4f} - {signal['time_ago_minutes']:.0f}分钟前")
            
            if not analysis.get('buy_signals') and not analysis.get('sell_signals'):
                print(f"     ⏳ 当前没有明确的买卖信号")
            
            # 操作建议
            if 'operation_advice' in analysis:
                advice = analysis['operation_advice']
                action_emoji = {'buy': '🟢 买入', 'sell': '🔴 卖出', 'wait': '🟡 等待'}
                print(f"     建议操作: {action_emoji.get(advice['action'], advice['action'])}")
                print(f"     置信度: {advice.get('confidence', 0)}/100")
        
        # 综合交易建议
        advice = analysis_result['comprehensive_advice']
        print(f"\n💡 综合交易建议:")
        action_emoji = {'buy': '🟢 买入', 'sell': '🔴 卖出', 'wait': '🟡 等待'}
        risk_emoji = {'low': '🟢', 'medium': '🟡', 'high': '🔴'}
        
        print(f"   操作建议: {action_emoji.get(advice['final_action'], advice['final_action'])}")
        print(f"   综合置信度: {advice['confidence']}/100")
        print(f"   风险等级: {risk_emoji.get(advice['risk_level'], '')} {advice['risk_level']}")
        
        if advice['final_action'] != 'wait':
            print(f"   入场价格: {advice['entry_price']:.4f}")
            if advice['stop_loss']:
                print(f"   止损位: {advice['stop_loss']:.4f}")
            if advice['take_profit']:
                print(f"   止盈位: {advice['take_profit']:.4f}")
            print(f"   建议仓位: {advice['position_size']}")
        
        print(f"\n🧠 决策理由:")
        for reason in advice['reasoning']:
            print(f"   • {reason}")
        
        # 获取主要时间周期的市场分析
        main_market = market.get(analysis_result['timeframe'], {})
        
        # 等待条件
        if main_market.get('waiting_conditions'):
            print(f"\n⏰ 等待条件:")
            for condition in main_market['waiting_conditions']:
                print(f"   • {condition}")
        
        # 风险提示
        if main_market.get('risk_warning'):
            print(f"\n⚠️ 风险提示:")
            for warning in main_market['risk_warning']:
                print(f"   • {warning}")
        
        print("\n" + "="*100)

    def plot_multi_timeframe_analysis(self, save_path=None, title=None):
        """
        绘制多时间周期缠论分析图表
        
        参数:
            save_path: 保存路径，如果为None则自动生成
            title: 图表标题，如果为None则使用默认标题
        
        返回:
            str: 保存的图表路径
        """
        try:
            # 导入绘图模块
            from chart_plotter import ChartPlotter
            
            # 获取多时间周期数据
            multi_data = self.get_multi_level_data(limit=200)
            
            if not multi_data:
                logger.error("无法绘制多时间周期图表：数据为空")
                return None
            
            # 对每个时间周期执行缠论分析
            fx_dict = {}
            bi_dict = {}
            xd_dict = {}
            
            for tf, df in multi_data.items():
                if df is not None and not df.empty:
                    analyzer = self.analyzers[tf]
                    analyzer.analyze(df)
                    
                    fx_dict[tf] = analyzer.fx_list
                    bi_dict[tf] = analyzer.bi_list
                    xd_dict[tf] = analyzer.xd_list
                    
                    # 为DataFrame添加交易对和时间周期信息，方便绘图
                    df.attrs['symbol'] = self.symbol
                    df.attrs['timeframe'] = tf
            
            # 创建绘图器
            plotter = ChartPlotter()
            
            # 生成默认标题（如果未提供）
            if title is None:
                title = f"{self.symbol} 多时间周期缠论分析"
            
            # 绘制多时间周期分析图表
            chart_path = plotter.plot_multi_timeframe(
                data_dict=multi_data,
                symbol=self.symbol,
                fx_dict=fx_dict,
                bi_dict=bi_dict,
                xd_dict=xd_dict,
                save_path=save_path,
                title=title
            )
            
            logger.info("多时间周期缠论分析图表已保存: {}".format(chart_path))
            return chart_path
            
        except ImportError:
            logger.error("缺少绘图模块，请安装chart_plotter.py")
            return None
        except Exception as e:
            logger.error("绘制多时间周期分析图表时出错: {}".format(str(e)))
            return None

    def check_realtime_signals(self, timeframe='15m'):
        """
        实时检查当前是否有买卖点信号形成
        
        参数:
            timeframe: 要检查的时间周期
            
        返回:
            dict: 实时信号结果，包括买卖点类型、强度和推荐操作
        """
        logger.info("开始检查 {} {} 实时买卖点信号".format(self.symbol, timeframe))
        result = {
            'timestamp': pd.Timestamp.now(),
            'symbol': self.symbol,
            'timeframe': timeframe,
            'buy_signals': [],
            'sell_signals': [],
            'recommendation': 'wait',
            'signal_strength': 0,
            'message': ''
        }
        
        try:
            # 1. 获取最新K线数据
            if timeframe not in self.data_fetchers:
                result['message'] = f"不支持{timeframe}周期，可用周期: {list(self.data_fetchers.keys())}"
                return result
                
            df = self.data_fetchers[timeframe].get_klines(symbol=self.symbol, limit=200)
            if df.empty:
                result['message'] = f"未获取到{timeframe}周期数据"
                return result
            
            # 2. 执行缠论分析
            analyzer = self.analyzers[timeframe]
            analyzer.analyze(df)
            
            # 3. 提取关键数据
            fx_list = analyzer.fx_list
            bi_list = analyzer.bi_list
            xd_list = analyzer.xd_list
            
            if len(bi_list) < 2:
                result['message'] = "笔的数量不足，无法判断买卖点"
                return result
            
            # 4. 识别中枢和买卖点
            try:
                from chart_plotter import ChartPlotter
                plotter = ChartPlotter()
                
                # 识别中枢
                pivot_zones = plotter.identify_pivot_zones(bi_list, df)
                
                # 识别各类买卖点
                buy_points1, sell_points1 = plotter.identify_first_buy_sell_points(bi_list, df, pivot_zones)
                buy_points2, sell_points2 = plotter.identify_second_buy_sell_points(bi_list, df, pivot_zones)
                buy_points3, sell_points3 = plotter.identify_third_buy_sell_points(bi_list, df, pivot_zones)
                
                # 5. 判断最近的买卖点信号（最后8根K线内）
                current_index = len(df) - 1
                recent_range = 8  # 扩大到最近8根K线
                
                # 记录最后一个中枢的位置，用于判断第二类买卖点
                last_pivot_start = -1
                last_pivot_end = -1
                if pivot_zones:
                    last_pivot = pivot_zones[-1]
                    last_pivot_start = last_pivot[0]
                    last_pivot_end = last_pivot[1]
                
                # 检查第一类买卖点
                for i, (idx, price, strength) in enumerate(buy_points1):
                    # 检查是否是最近形成的买点
                    if (current_index - recent_range <= idx <= current_index) or (i == len(buy_points1) - 1 and idx > current_index - 15):
                        # 计算止盈止损点
                        stop_loss, take_profit1, take_profit2, take_profit3 = self._calculate_stop_levels(
                            df, bi_list, pivot_zones, idx, price, 'buy', timeframe)
                        
                        result['buy_signals'].append({
                            'type': '第一类买点',
                            'index': idx,
                            'price': price,
                            'strength': strength + 1,  # 增加强度以提高优先级
                            'bar_position': current_index - idx,  # 0表示当前K线，1表示前一根
                            'description': f"第一类买点 - 向下笔的底分型确认，下跌趋势结束信号",
                            'stop_loss': stop_loss,
                            'take_profit1': take_profit1,
                            'take_profit2': take_profit2,
                            'take_profit3': take_profit3
                        })
                
                for i, (idx, price, strength) in enumerate(sell_points1):
                    # 检查是否是最近形成的卖点
                    if (current_index - recent_range <= idx <= current_index) or (i == len(sell_points1) - 1 and idx > current_index - 15):
                        # 计算止盈止损点
                        stop_loss, take_profit1, take_profit2, take_profit3 = self._calculate_stop_levels(
                            df, bi_list, pivot_zones, idx, price, 'sell', timeframe)
                        
                        result['sell_signals'].append({
                            'type': '第一类卖点',
                            'index': idx,
                            'price': price,
                            'strength': strength + 1,  # 增加强度以提高优先级
                            'bar_position': current_index - idx,
                            'description': f"第一类卖点 - 向上笔的顶分型确认，上涨趋势结束信号",
                            'stop_loss': stop_loss,
                            'take_profit1': take_profit1,
                            'take_profit2': take_profit2,
                            'take_profit3': take_profit3
                        })
                
                # 检查第二类买卖点
                for i, (idx, price, strength, pivot_idx) in enumerate(buy_points2):
                    # 检查是否是最近形成的买点或者是否与最近中枢有关
                    is_recent = current_index - recent_range <= idx <= current_index
                    is_near_last_pivot = last_pivot_end > 0 and idx > last_pivot_end - 5
                    
                    if is_recent or is_near_last_pivot or (i == len(buy_points2) - 1 and idx > current_index - 15):
                        # 计算当前价格与买点价格的偏差百分比
                        price_diff_pct = abs((df['close'].iloc[-1] - price) / price * 100)
                        # 如果当前价格接近买点价格，则增加强度
                        extra_strength = 1 if price_diff_pct < 2 else 0
                        
                        # 计算止盈止损点
                        stop_loss, take_profit1, take_profit2, take_profit3 = self._calculate_stop_levels(
                            df, bi_list, pivot_zones, idx, price, 'buy', timeframe)
                        
                        result['buy_signals'].append({
                            'type': '第二类买点',
                            'index': idx,
                            'price': price,
                            'strength': strength + extra_strength,
                            'bar_position': current_index - idx,
                            'description': f"第二类买点 - 下跌中枢完成后的低点回调买入信号",
                            'price_diff': f"{price_diff_pct:.2f}%",
                            'stop_loss': stop_loss,
                            'take_profit1': take_profit1,
                            'take_profit2': take_profit2,
                            'take_profit3': take_profit3
                        })
                
                for i, (idx, price, strength, pivot_idx) in enumerate(sell_points2):
                    # 检查是否是最近形成的卖点或者是否与最近中枢有关
                    is_recent = current_index - recent_range <= idx <= current_index
                    is_near_last_pivot = last_pivot_end > 0 and idx > last_pivot_end - 5
                    
                    if is_recent or is_near_last_pivot or (i == len(sell_points2) - 1 and idx > current_index - 15):
                        # 计算当前价格与卖点价格的偏差百分比
                        price_diff_pct = abs((df['close'].iloc[-1] - price) / price * 100)
                        # 如果当前价格接近卖点价格，则增加强度
                        extra_strength = 1 if price_diff_pct < 2 else 0
                        
                        # 计算止盈止损点
                        stop_loss, take_profit1, take_profit2, take_profit3 = self._calculate_stop_levels(
                            df, bi_list, pivot_zones, idx, price, 'sell', timeframe)
                        
                        result['sell_signals'].append({
                            'type': '第二类卖点',
                            'index': idx,
                            'price': price,
                            'strength': strength + extra_strength,
                            'bar_position': current_index - idx,
                            'description': f"第二类卖点 - 上涨中枢完成后的高点回调卖出信号",
                            'price_diff': f"{price_diff_pct:.2f}%",
                            'stop_loss': stop_loss,
                            'take_profit1': take_profit1,
                            'take_profit2': take_profit2,
                            'take_profit3': take_profit3
                        })
                
                # 检查第三类买卖点
                for i, (idx, price, strength, pivot_idx, reason) in enumerate(buy_points3):
                    # 检查是否是最近形成的买点或与最后中枢相关
                    is_recent = current_index - recent_range <= idx <= current_index
                    is_near_last_pivot = last_pivot_end > 0 and idx > last_pivot_end - 8
                    
                    if is_recent or is_near_last_pivot or (i == len(buy_points3) - 1 and idx > current_index - 15):
                        # 计算当前价格与买点价格的偏差百分比
                        price_diff_pct = abs((df['close'].iloc[-1] - price) / price * 100)
                        # 如果当前价格接近买点价格，则增加强度
                        extra_strength = 2 if price_diff_pct < 1.5 else 1 if price_diff_pct < 3 else 0
                        
                        # 计算止盈止损点
                        stop_loss, take_profit1, take_profit2, take_profit3 = self._calculate_stop_levels(
                            df, bi_list, pivot_zones, idx, price, 'buy', timeframe)
                        
                        result['buy_signals'].append({
                            'type': '第三类买点',
                            'index': idx,
                            'price': price,
                            'strength': strength + extra_strength,
                            'bar_position': current_index - idx,
                            'reason': reason,
                            'description': f"第三类买点 - {reason}",
                            'price_diff': f"{price_diff_pct:.2f}%",
                            'stop_loss': stop_loss,
                            'take_profit1': take_profit1,
                            'take_profit2': take_profit2,
                            'take_profit3': take_profit3
                        })
                
                for i, (idx, price, strength, pivot_idx, reason) in enumerate(sell_points3):
                    # 检查是否是最近形成的卖点或与最后中枢相关
                    is_recent = current_index - recent_range <= idx <= current_index
                    is_near_last_pivot = last_pivot_end > 0 and idx > last_pivot_end - 8
                    
                    if is_recent or is_near_last_pivot or (i == len(sell_points3) - 1 and idx > current_index - 15):
                        # 计算当前价格与卖点价格的偏差百分比
                        price_diff_pct = abs((df['close'].iloc[-1] - price) / price * 100)
                        # 如果当前价格接近卖点价格，则增加强度
                        extra_strength = 2 if price_diff_pct < 1.5 else 1 if price_diff_pct < 3 else 0
                        
                        # 计算止盈止损点
                        stop_loss, take_profit1, take_profit2, take_profit3 = self._calculate_stop_levels(
                            df, bi_list, pivot_zones, idx, price, 'sell', timeframe)
                        
                        result['sell_signals'].append({
                            'type': '第三类卖点',
                            'index': idx,
                            'price': price,
                            'strength': strength + extra_strength,
                            'bar_position': current_index - idx,
                            'reason': reason,
                            'description': f"第三类卖点 - {reason}",
                            'price_diff': f"{price_diff_pct:.2f}%",
                            'stop_loss': stop_loss,
                            'take_profit1': take_profit1,
                            'take_profit2': take_profit2,
                            'take_profit3': take_profit3
                        })
                
                # 特殊情况：中枢上方运行的突破情况（可能是ETH当前情况）
                if pivot_zones and len(bi_list) > 4:
                    last_pivot = pivot_zones[-1]
                    pivot_high = last_pivot[3]  # 中枢上沿
                    last_close = df['close'].iloc[-1]
                    
                    # 检查最后一个笔
                    last_bi = bi_list[-1]
                    prev_bi = bi_list[-2]
                    
                    # 如果当前价格在中枢上方，且向上突破，可能形成第二类买点
                    if (last_close > pivot_high * 1.01) and last_bi[4] == 'up' and prev_bi[4] == 'down':
                        # 向上突破中枢
                        # 计算止盈止损点
                        stop_loss, take_profit1, take_profit2, take_profit3 = self._calculate_stop_levels(
                            df, bi_list, pivot_zones, current_index, last_close, 'buy', timeframe)
                        
                        result['buy_signals'].append({
                            'type': '潜在第二类买点',
                            'index': current_index,
                            'price': last_close,
                            'strength': 7,  # 给较高权重但不确定性较大
                            'bar_position': 0,
                            'description': f"潜在第二类买点 - 价格位于中枢上方，向上突破，可能形成突破回调买点",
                            'price_diff': f"{((last_close - pivot_high) / pivot_high * 100):.2f}%高于中枢上沿",
                            'stop_loss': stop_loss,
                            'take_profit1': take_profit1,
                            'take_profit2': take_profit2,
                            'take_profit3': take_profit3
                        })
                    
                    # 如果当前价格回调到中枢上沿附近，可能是第二类买点的最佳入场点
                    elif (0.99 * pivot_high <= last_close <= 1.03 * pivot_high) and last_bi[4] == 'down' and prev_bi[4] == 'up':
                        # 回调至中枢上沿
                        # 计算止盈止损点
                        stop_loss, take_profit1, take_profit2, take_profit3 = self._calculate_stop_levels(
                            df, bi_list, pivot_zones, current_index, last_close, 'buy', timeframe)
                        
                        result['buy_signals'].append({
                            'type': '中枢上沿买点',
                            'index': current_index,
                            'price': last_close,
                            'strength': 8,  # 较高权重
                            'bar_position': 0,
                            'description': f"中枢上沿买点 - 上涨突破中枢后回调至中枢上沿支撑位，良好买入机会",
                            'price_diff': f"{((last_close - pivot_high) / pivot_high * 100):.2f}%",
                            'stop_loss': stop_loss,
                            'take_profit1': take_profit1,
                            'take_profit2': take_profit2,
                            'take_profit3': take_profit3
                        })
                
                # 6. 根据信号生成建议
                if result['buy_signals'] and not result['sell_signals']:
                    # 只有买点信号
                    strongest_buy = max(result['buy_signals'], key=lambda x: x['strength'])
                    # 时效性检查：只有10根K线内的信号才产生操作建议，否则建议观望
                    if strongest_buy['bar_position'] <= 10:
                        result['recommendation'] = 'buy'
                        result['signal_strength'] = strongest_buy['strength']
                        result['stop_loss'] = strongest_buy.get('stop_loss')
                        result['take_profit1'] = strongest_buy.get('take_profit1')
                        result['take_profit2'] = strongest_buy.get('take_profit2')
                        result['take_profit3'] = strongest_buy.get('take_profit3')
                        result['message'] = "发现{}，强度: {}/10，位于{}根K线前，止损: {}，止盈1: {}，止盈2: {}，止盈3: {}".format(
                            strongest_buy['type'], strongest_buy['strength'], strongest_buy['bar_position'],
                            strongest_buy.get('stop_loss'), strongest_buy.get('take_profit1'),
                            strongest_buy.get('take_profit2'), strongest_buy.get('take_profit3'))
                    else:
                        result['recommendation'] = 'wait'
                        result['signal_strength'] = strongest_buy['strength']
                        result['message'] = "发现{}，但信号已过时（{}根K线前），建议观望".format(
                            strongest_buy['type'], strongest_buy['bar_position'])
                
                elif result['sell_signals'] and not result['buy_signals']:
                    # 只有卖点信号
                    strongest_sell = max(result['sell_signals'], key=lambda x: x['strength'])
                    # 时效性检查：只有10根K线内的信号才产生操作建议，否则建议观望
                    if strongest_sell['bar_position'] <= 10:
                        result['recommendation'] = 'sell'
                        result['signal_strength'] = strongest_sell['strength']
                        result['stop_loss'] = strongest_sell.get('stop_loss')
                        result['take_profit1'] = strongest_sell.get('take_profit1')
                        result['take_profit2'] = strongest_sell.get('take_profit2')
                        result['take_profit3'] = strongest_sell.get('take_profit3')
                        result['message'] = "发现{}，强度: {}/10，位于{}根K线前，止损: {}，止盈1: {}，止盈2: {}，止盈3: {}".format(
                            strongest_sell['type'], strongest_sell['strength'], strongest_sell['bar_position'],
                            strongest_sell.get('stop_loss'), strongest_sell.get('take_profit1'),
                            strongest_sell.get('take_profit2'), strongest_sell.get('take_profit3'))
                    else:
                        result['recommendation'] = 'wait'
                        result['signal_strength'] = strongest_sell['strength']
                        result['message'] = "发现{}，但信号已过时（{}根K线前），建议观望".format(
                            strongest_sell['type'], strongest_sell['bar_position'])
                
                elif result['buy_signals'] and result['sell_signals']:
                    # 同时有买卖点信号，比较强度
                    strongest_buy = max(result['buy_signals'], key=lambda x: x['strength'])
                    strongest_sell = max(result['sell_signals'], key=lambda x: x['strength'])
                    
                    # 检查最强信号的时效性
                    recent_buy = strongest_buy['bar_position'] <= 10
                    recent_sell = strongest_sell['bar_position'] <= 10
                    
                    if not recent_buy and not recent_sell:
                        # 如果两种信号都过时，建议观望
                        result['recommendation'] = 'wait'
                        result['signal_strength'] = max(strongest_buy['strength'], strongest_sell['strength'])
                        result['message'] = "买卖信号均已过时（买:{}根K线前，卖:{}根K线前），建议观望".format(
                            strongest_buy['bar_position'], strongest_sell['bar_position'])
                    elif recent_buy and not recent_sell:
                        # 只有买入信号是最近的
                        result['recommendation'] = 'buy'
                        result['signal_strength'] = strongest_buy['strength']
                        result['stop_loss'] = strongest_buy.get('stop_loss')
                        result['take_profit1'] = strongest_buy.get('take_profit1')
                        result['take_profit2'] = strongest_buy.get('take_profit2')
                        result['take_profit3'] = strongest_buy.get('take_profit3')
                        result['message'] = "买卖信号同时存在，买点更近，{}，强度: {}/10，止损: {}，止盈1: {}，止盈2: {}，止盈3: {}".format(
                            strongest_buy['type'], strongest_buy['strength'],
                            strongest_buy.get('stop_loss'), strongest_buy.get('take_profit1'),
                            strongest_buy.get('take_profit2'), strongest_buy.get('take_profit3'))
                    elif not recent_buy and recent_sell:
                        # 只有卖出信号是最近的
                        result['recommendation'] = 'sell'
                        result['signal_strength'] = strongest_sell['strength']
                        result['stop_loss'] = strongest_sell.get('stop_loss')
                        result['take_profit1'] = strongest_sell.get('take_profit1')
                        result['take_profit2'] = strongest_sell.get('take_profit2')
                        result['take_profit3'] = strongest_sell.get('take_profit3')
                        result['message'] = "买卖信号同时存在，卖点更近，{}，强度: {}/10，止损: {}，止盈1: {}，止盈2: {}，止盈3: {}".format(
                            strongest_sell['type'], strongest_sell['strength'],
                            strongest_sell.get('stop_loss'), strongest_sell.get('take_profit1'),
                            strongest_sell.get('take_profit2'), strongest_sell.get('take_profit3'))
                    else:
                        # 两种信号都是最近的，按强度比较
                        if strongest_buy['strength'] > strongest_sell['strength']:
                            result['recommendation'] = 'buy'
                            result['signal_strength'] = strongest_buy['strength']
                            result['stop_loss'] = strongest_buy.get('stop_loss')
                            result['take_profit1'] = strongest_buy.get('take_profit1')
                            result['take_profit2'] = strongest_buy.get('take_profit2')
                            result['take_profit3'] = strongest_buy.get('take_profit3')
                            result['message'] = "买卖信号同时存在，买点更强，{}，强度: {}/10，止损: {}，止盈1: {}，止盈2: {}，止盈3: {}".format(
                                strongest_buy['type'], strongest_buy['strength'],
                                strongest_buy.get('stop_loss'), strongest_buy.get('take_profit1'),
                                strongest_buy.get('take_profit2'), strongest_buy.get('take_profit3'))
                        elif strongest_sell['strength'] > strongest_buy['strength']:
                            result['recommendation'] = 'sell'
                            result['signal_strength'] = strongest_sell['strength']
                            result['stop_loss'] = strongest_sell.get('stop_loss')
                            result['take_profit1'] = strongest_sell.get('take_profit1')
                            result['take_profit2'] = strongest_sell.get('take_profit2')
                            result['take_profit3'] = strongest_sell.get('take_profit3')
                            result['message'] = "买卖信号同时存在，卖点更强，{}，强度: {}/10，止损: {}，止盈1: {}，止盈2: {}，止盈3: {}".format(
                                strongest_sell['type'], strongest_sell['strength'],
                                strongest_sell.get('stop_loss'), strongest_sell.get('take_profit1'),
                                strongest_sell.get('take_profit2'), strongest_sell.get('take_profit3'))
                        else:
                            result['recommendation'] = 'wait'
                            result['signal_strength'] = strongest_buy['strength']  # 相等
                            result['message'] = "买卖信号强度相当，建议观望"
                else:
                    result['message'] = "未发现明确的买卖点信号"
            
            except ImportError as e:
                result['message'] = "无法导入ChartPlotter: {}".format(str(e))
                logger.error("无法导入ChartPlotter: {}".format(str(e)))
            except Exception as e:
                result['message'] = "分析买卖点时出错: {}".format(str(e))
                logger.error("分析买卖点时出错: {}".format(str(e)))
                
            logger.info("{} {} 实时信号检查完成: {}".format(self.symbol, timeframe, result['message']))
            return result
            
        except Exception as e:
            result['message'] = f"实时信号检查失败: {str(e)}"
            logger.error(f"实时信号检查失败: {str(e)}")
            return result

    def _calculate_stop_levels(self, df, bi_list, pivot_zones, idx, price, signal_type, timeframe):
        """
        计算买卖点的止盈止损位 - 基于缠论理论的改进版本
        
        参数:
            df: K线数据
            bi_list: 笔列表
            pivot_zones: 中枢列表
            idx: 买卖点所在K线索引
            price: 买卖点价格
            signal_type: 'buy'或'sell'
            timeframe: 时间周期
            
        返回:
            tuple: (止损位, 第一止盈位, 第二止盈位, 第三止盈位)
        """
        try:
            # 使用缠论分析器计算更精确的止盈止损
            from chan_analysis import ChanAnalysis
            
            # 创建缠论分析器实例
            chan_analyzer = ChanAnalysis(df)
            chan_analyzer.pivot_zones = pivot_zones
            
            # 确定买卖点类型（这里需要根据实际情况判断）
            buy_sell_point_type = self._determine_point_type(idx, price, bi_list, pivot_zones, signal_type)
            
            # 找到相关中枢
            related_pivot_idx = self._find_related_pivot(idx, pivot_zones)
            
            # 构建入场点信息
            entry_point = {
                'index': idx,
                'price': price,
                'timestamp': df['timestamp'].iloc[idx] if 'timestamp' in df.columns else str(idx)
            }
            
            # 使用缠论理论计算止盈止损
            targets = chan_analyzer.calculate_chan_profit_targets(
                entry_point=entry_point,
                signal_type=signal_type,
                buy_sell_point_type=buy_sell_point_type,
                related_pivot_idx=related_pivot_idx,
                current_level=timeframe
            )
            
            # 提取止损和止盈位
            stop_loss = targets.get('stop_loss')
            profit_targets = targets.get('profit_targets', [])
            
            # 按照原有格式返回三个止盈位
            take_profit1 = profit_targets[0]['price'] if len(profit_targets) > 0 else None
            take_profit2 = profit_targets[1]['price'] if len(profit_targets) > 1 else None
            take_profit3 = profit_targets[2]['price'] if len(profit_targets) > 2 else None
            
            # 如果缠论计算失败，使用改进的传统方法
            if not stop_loss or not take_profit1:
                return self._calculate_stop_levels_improved_traditional(
                    df, bi_list, pivot_zones, idx, price, signal_type, timeframe
                )
            
            logger.info(f"使用缠论理论计算止盈止损: 止损={stop_loss}, 止盈1={take_profit1}, 止盈2={take_profit2}, 止盈3={take_profit3}")
            return stop_loss, take_profit1, take_profit2, take_profit3
            
        except Exception as e:
            logger.warning(f"缠论止盈止损计算失败，使用改进传统方法: {str(e)}")
            return self._calculate_stop_levels_improved_traditional(
                df, bi_list, pivot_zones, idx, price, signal_type, timeframe
            )
    
    def _determine_point_type(self, idx, price, bi_list, pivot_zones, signal_type):
        """
        判断买卖点类型（第一、二、三类）
        
        返回:
            int: 1, 2, 或 3
        """
        try:
            # 简化的判断逻辑，实际应该更复杂
            
            # 检查是否在中枢附近
            for pivot in pivot_zones:
                # 中枢格式：(start_idx, end_idx, zg, zd, start_date, end_date)
                pivot_start, pivot_end, pivot_high, pivot_low = pivot[:4]
                
                # 如果买卖点在中枢时间范围内或附近
                if abs(idx - pivot_end) <= 10:
                    if signal_type == 'buy':
                        # 如果价格接近中枢下沿，可能是第二类买点
                        if abs(price - pivot_low) / pivot_low < 0.02:
                            return 2
                        # 如果价格在中枢内部，可能是第三类买点
                        elif pivot_low <= price <= pivot_high:
                            return 3
                    else:  # sell
                        # 如果价格接近中枢上沿，可能是第二类卖点
                        if abs(price - pivot_high) / pivot_high < 0.02:
                            return 2
                        # 如果价格在中枢内部，可能是第三类卖点
                        elif pivot_low <= price <= pivot_high:
                            return 3
            
            # 检查是否是趋势转折点（第一类买卖点）
            # 简化判断：如果前面有明显的趋势，当前是反向信号
            if len(bi_list) >= 3:
                recent_bis = bi_list[-3:]
                if signal_type == 'buy':
                    # 如果最近的笔主要是下降的，当前买点可能是第一类
                    down_count = sum(1 for bi in recent_bis if bi[4] == 'down')
                    if down_count >= 2:
                        return 1
                else:  # sell
                    # 如果最近的笔主要是上升的，当前卖点可能是第一类
                    up_count = sum(1 for bi in recent_bis if bi[4] == 'up')
                    if up_count >= 2:
                        return 1
            
            # 默认返回第二类
            return 2
            
        except Exception as e:
            logger.warning(f"判断买卖点类型失败: {str(e)}")
            return 2  # 默认第二类
    
    def _find_related_pivot(self, idx, pivot_zones):
        """
        找到与买卖点相关的中枢
        
        返回:
            int: 中枢索引，如果没有找到返回None
        """
        try:
            # 寻找最近的中枢
            best_pivot_idx = None
            min_distance = float('inf')
            
            for i, pivot in enumerate(pivot_zones):
                # 中枢格式：(start_idx, end_idx, zg, zd, start_date, end_date)
                pivot_start, pivot_end, pivot_high, pivot_low = pivot[:4]
                
                # 计算买卖点到中枢的距离
                if idx >= pivot_start:
                    distance = idx - pivot_end
                else:
                    distance = pivot_start - idx
                
                # 选择最近的中枢
                if distance < min_distance:
                    min_distance = distance
                    best_pivot_idx = i
            
            # 如果距离太远，返回None
            if min_distance > 50:  # 超过50个K线认为太远
                return None
                
            return best_pivot_idx
            
        except Exception as e:
            logger.warning(f"寻找相关中枢失败: {str(e)}")
            return None
    
    def _calculate_stop_levels_improved_traditional(self, df, bi_list, pivot_zones, idx, price, signal_type, timeframe):
        """
        改进的传统止盈止损计算方法
        """
        try:
            # 获取ATR值用于动态止损
            atr_periods = 14
            if 'atr' not in df.columns:
                high_low = df['high'] - df['low']
                high_close = abs(df['high'] - df['close'].shift())
                low_close = abs(df['low'] - df['close'].shift())
                tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
                atr = tr.rolling(atr_periods).mean()
                df['atr'] = atr
            
            current_atr = df['atr'].iloc[-1] if not pd.isna(df['atr'].iloc[-1]) else price * 0.02
            
            if signal_type == 'buy':
                # 改进的买入止损策略
                # 1. 寻找最近的重要支撑位
                support_level = self._find_dynamic_support(df, idx, lookback=30)
                if support_level and support_level < price:
                    stop_loss = support_level - current_atr * 0.5
                else:
                    stop_loss = price - current_atr * 2  # 2倍ATR止损
                
                # 确保止损不超过5%
                stop_loss = max(stop_loss, price * 0.95)
                
                # 改进的买入止盈策略
                # 第一止盈：1.5倍ATR或最近阻力位
                resistance_level = self._find_dynamic_resistance(df, idx, lookback=30)
                if resistance_level and resistance_level > price * 1.02:
                    take_profit1 = resistance_level
                else:
                    take_profit1 = price + current_atr * 1.5
                
                # 第二止盈：基于中枢或斐波那契
                if pivot_zones:
                    latest_pivot = pivot_zones[-1]
                    pivot_height = latest_pivot[2] - latest_pivot[3]
                    take_profit2 = take_profit1 + pivot_height * 0.618  # 黄金分割
                else:
                    take_profit2 = price + current_atr * 3
                
                # 第三止盈：趋势延续目标
                take_profit3 = price + current_atr * 5
                
            else:  # sell信号
                # 改进的卖出止损策略
                resistance_level = self._find_dynamic_resistance(df, idx, lookback=30)
                if resistance_level and resistance_level > price:
                    stop_loss = resistance_level + current_atr * 0.5
                else:
                    stop_loss = price + current_atr * 2
                
                # 确保止损不超过5%
                stop_loss = min(stop_loss, price * 1.05)
                
                # 改进的卖出止盈策略
                support_level = self._find_dynamic_support(df, idx, lookback=30)
                if support_level and support_level < price * 0.98:
                    take_profit1 = support_level
                else:
                    take_profit1 = price - current_atr * 1.5
                
                if pivot_zones:
                    latest_pivot = pivot_zones[-1]
                    pivot_height = latest_pivot[2] - latest_pivot[3]
                    take_profit2 = take_profit1 - pivot_height * 0.618
                else:
                    take_profit2 = price - current_atr * 3
                
                take_profit3 = price - current_atr * 5
            
            # 四舍五入到合理精度
            stop_loss = round(stop_loss, 4)
            take_profit1 = round(take_profit1, 4)
            take_profit2 = round(take_profit2, 4)
            take_profit3 = round(take_profit3, 4)
            
            return stop_loss, take_profit1, take_profit2, take_profit3
            
        except Exception as e:
            logger.error(f"改进传统止盈止损计算失败: {str(e)}")
            # 最后的备用方案
            if signal_type == 'buy':
                return (round(price * 0.97, 4), round(price * 1.05, 4), 
                       round(price * 1.1, 4), round(price * 1.15, 4))
            else:
                return (round(price * 1.03, 4), round(price * 0.95, 4), 
                       round(price * 0.9, 4), round(price * 0.85, 4))
    
    def _find_dynamic_support(self, df, current_idx, lookback=30):
        """动态寻找支撑位"""
        try:
            start_idx = max(0, current_idx - lookback)
            price_range = df['low'].iloc[start_idx:current_idx]
            
            if price_range.empty:
                return None
            
            # 寻找最近的重要低点（局部最小值）
            for i in range(len(price_range) - 3, 2, -1):
                if (price_range.iloc[i] < price_range.iloc[i-1] and 
                    price_range.iloc[i] < price_range.iloc[i-2] and
                    price_range.iloc[i] < price_range.iloc[i+1] and 
                    price_range.iloc[i] < price_range.iloc[i+2]):
                    return price_range.iloc[i]
            
            # 如果没有找到明显的支撑，返回最低点
            return price_range.min()
            
        except Exception:
            return None
    
    def _find_dynamic_resistance(self, df, current_idx, lookback=30):
        """动态寻找阻力位"""
        try:
            start_idx = max(0, current_idx - lookback)
            price_range = df['high'].iloc[start_idx:current_idx]
            
            if price_range.empty:
                return None
            
            # 寻找最近的重要高点（局部最大值）
            for i in range(len(price_range) - 3, 2, -1):
                if (price_range.iloc[i] > price_range.iloc[i-1] and 
                    price_range.iloc[i] > price_range.iloc[i-2] and
                    price_range.iloc[i] > price_range.iloc[i+1] and 
                    price_range.iloc[i] > price_range.iloc[i+2]):
                    return price_range.iloc[i]
            
            # 如果没有找到明显的阻力，返回最高点
            return price_range.max()
            
        except Exception:
            return None


# 测试代码
if __name__ == "__main__":
    # 设置日志
    logger.add("logs/multi_level_analysis_{time}.log", rotation="100 MB")
    
    try:
        # 创建多级别分析器
        analyzer = MultiLevelAnalysis(symbol="BTC/USDT")
        
        # 执行综合市场分析
        result = analyzer.comprehensive_market_analysis()
        
        if result:
            # 打印综合分析报告
            analyzer.print_comprehensive_report(result)
            
            # 也可以单独查看多级别联立分析结果
            multi_level_result = result['multi_level_analysis']
            print("\n=== 多级别联立分析详情 ===")
            print(f"最终判断: {'线段终结' if multi_level_result['final_judgment'] else '线段未终结'}")
            print(f"置信度: {multi_level_result['confidence']}%")
            print(f"判断理由: {multi_level_result['reason']}")
            print(f"趋势一致性: {'是' if multi_level_result['trend_consistency'] else '否'}")
            
            print("\n=== 各级别详细结果 ===")
            for tf, level_result in multi_level_result['level_results'].items():
                print(f"{tf} 级别:")
                print(f"  - 线段终结: {'是' if level_result['segment_ended'] else '否'}")
                print(f"  - 当前趋势: {level_result['current_trend']}")
                print(f"  - 背驰强度: {level_result['divergence']['strength']}")
                print(f"  - 最新价格: {level_result['last_price']}")
        else:
            print("❌ 综合市场分析失败")
        
    except Exception as e:
        print("测试失败: {}".format(str(e)))
        logger.error("多级别分析测试失败: {}".format(str(e))) 