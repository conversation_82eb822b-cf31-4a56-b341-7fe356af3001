#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多级别联立分析模块：
基于缠论理论，通过多个时间级别的联立分析来提高线段终结判断的准确率
"""

import pandas as pd
from loguru import logger
from typing import Dict, List, Tuple, Optional
from data_fetcher import DataFetcher
from chan_analysis import ChanAnalysis
from market_analyzer import MarketAnalyzer
from chart_plotter import ChartPlotter

class MultiLevelAnalysis:
    """
    多级别联立分析器
    
    根据缠论理论：
    1. 大级别决定方向，小级别决定进出点
    2. 多级别共振时，信号更可靠
    3. 级别间的背驰关系是关键判断依据
    """
    
    # 定义级别层次关系（从小到大）
    TIMEFRAME_HIERARCHY = {
        '1m': 1,
        '5m': 5,
        '15m': 15,
        '30m': 30,
        '1h': 60,
        '4h': 240,
        '1d': 1440
    }
    
    def __init__(self, symbol="BTC/USDT", exchange_id=None):
        """
        初始化多级别分析器
        
        参数:
            symbol: 交易对
            exchange_id: 交易所ID
        """
        self.symbol = symbol
        self.exchange_id = exchange_id
        
        # 定义要分析的时间级别（从小到大）
        self.timeframes = ['1m', '5m', '15m', '30m', '1h']
        
        # 为每个级别创建数据获取器和分析器
        self.data_fetchers = {}
        self.analyzers = {}
        
        for tf in self.timeframes:
            try:
                self.data_fetchers[tf] = DataFetcher(
                    symbols=symbol, 
                    timeframe=tf, 
                    exchange_id=exchange_id
                )
                self.analyzers[tf] = ChanAnalysis()
                logger.info("初始化 {} 级别分析器成功".format(tf))
            except Exception as e:
                logger.error("初始化 {} 级别分析器失败: {}".format(tf, str(e)))
        
        # 初始化市场分析器
        self.market_analyzer = MarketAnalyzer()

        # 等待次级别确认的状态管理
        self.waiting_for_confirmation = {
            'is_waiting': False,
            'target_level': None,
            'parent_level': None,
            'parent_signal': None,
            'direction': None,  # 'buy' or 'sell'
            'entry_targets': None,
            'wait_start_time': None,
            'max_wait_minutes': 120  # 最大等待2小时
        }

        logger.info("多级别分析器初始化完成，监控级别: {}".format(self.timeframes))
    
    def get_multi_level_data(self, limit=200):
        """
        获取多个级别的K线数据
        
        参数:
            limit: 每个级别获取的K线数量
            
        返回:
            Dict[str, pd.DataFrame]: 各级别的K线数据
        """
        multi_data = {}
        
        for tf in self.timeframes:
            try:
                if tf in self.data_fetchers:
                    df = self.data_fetchers[tf].get_klines(symbol=self.symbol, limit=limit)
                    if not df.empty:
                        # 确保数据类型正确
                        df = self._ensure_numeric_data(df)
                        if not df.empty:  # 检查数据转换后是否为空
                            multi_data[tf] = df
                            logger.debug("获取 {} 级别数据成功，数量: {}".format(tf, len(df)))
                        else:
                            logger.warning("获取 {} 级别数据转换后为空".format(tf))
                    else:
                        logger.warning("获取 {} 级别数据为空".format(tf))
                else:
                    logger.warning("未找到 {} 级别的数据获取器".format(tf))
            except Exception as e:
                logger.error("获取 {} 级别数据失败: {}".format(tf, str(e)))
        
        return multi_data
    
    def _ensure_numeric_data(self, df):
        """
        确保DataFrame中的价格数据是数字类型
        
        参数:
            df: 原始DataFrame
            
        返回:
            pd.DataFrame: 转换后的DataFrame
        """
        try:
            df_copy = df.copy()
            
            # 需要转换为数字的列
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            
            for col in numeric_columns:
                if col in df_copy.columns:
                    # 转换为数字类型，无法转换的设为NaN
                    df_copy[col] = pd.to_numeric(df_copy[col], errors='coerce')
            
            # 删除包含NaN的行
            df_copy = df_copy.dropna(subset=numeric_columns)
            
            # 重置索引
            df_copy = df_copy.reset_index(drop=True)
            
            return df_copy
            
        except Exception as e:
            logger.error("数据类型转换失败: {}".format(str(e)))
            return pd.DataFrame()  # 返回空DataFrame
    
    def analyze_single_level(self, timeframe, df):
        """
        分析单个级别的线段状态
        
        参数:
            timeframe: 时间级别
            df: K线数据
            
        返回:
            Dict: 分析结果
        """
        try:
            analyzer = self.analyzers[timeframe]
            
            # 调整数据格式
            kline_data = df.copy()
            if 'timestamp' in kline_data.columns and 'datetime' not in kline_data.columns:
                kline_data['datetime'] = kline_data['timestamp']
                kline_data.set_index('datetime', inplace=True)
            
            # 执行分析
            is_ended, details = analyzer.analyze(kline_data)
            
            # 获取当前走势方向
            current_trend = self._get_current_trend(analyzer)
            
            # 获取背驰信息
            divergence_info = self._check_divergence(analyzer)
            
            result = {
                'timeframe': timeframe,
                'segment_ended': is_ended,
                'details': details,
                'current_trend': current_trend,
                'divergence': divergence_info,
                'last_price': df['close'].iloc[-1] if not df.empty else None,
                'data_quality': len(df)
            }
            
            logger.debug("{} 级别分析完成: 终结={}, 趋势={}".format(timeframe, is_ended, current_trend))
            return result
            
        except Exception as e:
            logger.error("分析 {} 级别时出错: {}".format(timeframe, str(e)))
            return {
                'timeframe': timeframe,
                'segment_ended': False,
                'details': {'error': str(e)},
                'current_trend': 'unknown',
                'divergence': {},
                'last_price': None,
                'data_quality': 0
            }
    
    def _get_current_trend(self, analyzer):
        """
        获取当前走势方向
        
        参数:
            analyzer: 缠论分析器
            
        返回:
            str: 'up', 'down', 'sideways', 'unknown'
        """
        try:
            if hasattr(analyzer, 'xd_list') and analyzer.xd_list:
                # 获取最后一个线段的方向
                last_xd = analyzer.xd_list[-1]
                return last_xd[4] if len(last_xd) > 4 else 'unknown'
            elif hasattr(analyzer, 'bi_list') and analyzer.bi_list:
                # 如果没有线段，看笔的方向
                last_bi = analyzer.bi_list[-1]
                return last_bi[4] if len(last_bi) > 4 else 'unknown'
            else:
                return 'unknown'
        except Exception as e:
            logger.debug("获取趋势方向时出错: {}".format(str(e)))
            return 'unknown'
    
    def _check_divergence(self, analyzer):
        """
        检查背驰情况
        
        参数:
            analyzer: 缠论分析器
            
        返回:
            Dict: 背驰信息
        """
        try:
            divergence_info = {
                'macd_divergence': False,
                'price_divergence': False,
                'volume_divergence': False,
                'strength': 0  # 背驰强度 0-3
            }
            
            if hasattr(analyzer, 'df') and analyzer.df is not None:
                df = analyzer.df
                
                # 检查MACD背驰
                if 'macd' in df.columns:
                    recent_macd = df['macd'].tail(10)
                    if len(recent_macd) >= 5:
                        # 简化的背驰检查：最近的MACD值与价格走势相反
                        if hasattr(analyzer, 'xd_list') and analyzer.xd_list:
                            last_xd = analyzer.xd_list[-1]
                            direction = last_xd[4]
                            
                            if direction == 'up' and recent_macd.iloc[-1] < recent_macd.iloc[-5:].mean():
                                # 上升走势但MACD走弱
                                divergence_info['macd_divergence'] = True
                                divergence_info['strength'] += 1
                            elif direction == 'down' and recent_macd.iloc[-1] > recent_macd.iloc[-5:].mean():
                                # 下降走势但MACD走强
                                divergence_info['macd_divergence'] = True
                                divergence_info['strength'] += 1
                
                # 检查量价背驰
                if 'volume' in df.columns and len(df) >= 10:
                    recent_volume = df['volume'].tail(10)
                    if len(recent_volume) >= 5:
                        # 检查近期成交量是否异常
                        avg_volume = recent_volume.iloc[:-1].mean()
                        current_volume = recent_volume.iloc[-1]
                        
                        if current_volume > avg_volume * 1.5:
                            # 成交量显著放大
                            divergence_info['volume_divergence'] = True
                            divergence_info['strength'] += 1
                
                # 检查价格背驰（高点之间或低点之间的背驰）
                if hasattr(analyzer, 'xd_list') and len(analyzer.xd_list) >= 2:
                    last_xd = analyzer.xd_list[-1]
                    prev_xd = analyzer.xd_list[-2]
                    
                    # 获取最近的两个线段方向
                    direction = last_xd[4]
                    
                    if direction == prev_xd[4]:  # 同向线段
                        if direction == 'up':
                            # 检查上升线段高点的背驰
                            if last_xd[3] > prev_xd[3]:  # 价格创新高
                                # 但动能减弱
                                if df['macd'].iloc[-1] < df['macd'].iloc[-10:-1].max():
                                    divergence_info['price_divergence'] = True
                                    divergence_info['strength'] += 1
                        else:  # 下降线段
                            # 检查下降线段低点的背驰
                            if last_xd[3] < prev_xd[3]:  # 价格创新低
                                # 但动能减弱
                                if df['macd'].iloc[-1] > df['macd'].iloc[-10:-1].min():
                                    divergence_info['price_divergence'] = True
                                    divergence_info['strength'] += 1
            
            return divergence_info
                
        except Exception as e:
            logger.debug("检查背驰时出错: {}".format(str(e)))
            return {
                'macd_divergence': False,
                'price_divergence': False,
                'volume_divergence': False,
                'strength': 0
            }
    
    def multi_level_judgment(self):
        """
        多级别联立判断
        
        返回:
            Dict: 综合判断结果
        """
        logger.info("开始多级别联立分析")
        
        # 获取多级别数据
        multi_data = self.get_multi_level_data()
        
        if not multi_data:
            logger.error("未能获取任何级别的数据")
            return {
                'final_judgment': False,
                'confidence': 0,
                'reason': '无法获取数据',
                'level_results': {}
            }
        
        # 分析各个级别
        level_results = {}
        for tf, df in multi_data.items():
            level_results[tf] = self.analyze_single_level(tf, df)
        
        # 多级别联立判断
        final_result = self._synthesize_multi_level_results(level_results)
        
        logger.info("多级别联立分析完成，最终判断: {}, 置信度: {}".format(final_result['final_judgment'], final_result['confidence']))
        
        return final_result
    
    def _synthesize_multi_level_results(self, level_results):
        """
        综合多级别分析结果
        
        参数:
            level_results: 各级别分析结果
            
        返回:
            Dict: 综合判断结果
        """
        # 初始化结果
        synthesis = {
            'final_judgment': False,
            'confidence': 0,
            'reason': '',
            'level_results': level_results,
            'consensus_levels': [],
            'divergent_levels': [],
            'trend_consistency': False
        }
        
        # 统计各级别的终结信号
        ended_levels = []
        not_ended_levels = []
        trends = []
        total_divergence_strength = 0
        
        for tf, result in level_results.items():
            if result['segment_ended']:
                ended_levels.append(tf)
            else:
                not_ended_levels.append(tf)
            
            trends.append(result['current_trend'])
            total_divergence_strength += result['divergence']['strength']
        
        # 计算级别权重（大级别权重更高）
        level_weights = {}
        for tf in level_results.keys():
            level_weights[tf] = self.TIMEFRAME_HIERARCHY.get(tf, 1)
        
        # 加权计算终结信号强度
        weighted_ended_score = sum(level_weights[tf] for tf in ended_levels)
        total_weight = sum(level_weights.values())
        
        # 趋势一致性检查
        unique_trends = set([t for t in trends if t != 'unknown'])
        trend_consistency = len(unique_trends) <= 1
        synthesis['trend_consistency'] = trend_consistency
        
        # 综合判断逻辑
        confidence = 0
        reasons = []
        
        # 1. 多级别共振判断
        if len(ended_levels) >= 2:
            confidence += 30
            reasons.append("{}个级别出现终结信号".format(len(ended_levels)))
            synthesis['consensus_levels'] = ended_levels
        
        # 2. 大级别权重判断
        if weighted_ended_score / total_weight > 0.5:
            confidence += 25
            reasons.append("大级别终结信号权重较高")
        
        # 3. 背驰强度判断
        if total_divergence_strength >= 3:
            confidence += 20
            reasons.append("多级别背驰强度较高({})".format(total_divergence_strength))
        
        # 4. 趋势一致性加分
        if trend_consistency:
            confidence += 15
            reasons.append("多级别趋势方向一致")
        else:
            synthesis['divergent_levels'] = list(level_results.keys())
        
        # 5. 特殊情况：大级别（1h）终结信号
        if '1h' in ended_levels:
            confidence += 20
            reasons.append("1小时级别出现终结信号")
        
        # 6. 特殊情况：小级别（5m）与大级别方向一致
        if '5m' in level_results and '1h' in level_results:
            if (level_results['5m']['segment_ended'] and 
                level_results['5m']['current_trend'] == level_results['1h']['current_trend']):
                confidence += 10
                reasons.append("小级别与大级别方向一致")
        
        # 最终判断
        synthesis['confidence'] = min(confidence, 100)  # 置信度不超过100
        synthesis['final_judgment'] = confidence >= 60  # 置信度60以上才判断为终结
        synthesis['reason'] = '; '.join(reasons) if reasons else '信号强度不足'
        
        return synthesis
    
    def get_trading_suggestion(self, judgment_result):
        """
        基于多级别分析结果给出交易建议
        
        参数:
            judgment_result: 多级别判断结果
            
        返回:
            Dict: 交易建议
        """
        suggestion = {
            'action': 'hold',  # hold, buy, sell
            'confidence': judgment_result['confidence'],
            'risk_level': 'medium',  # low, medium, high
            'stop_loss': None,
            'take_profit': None,
            'reasoning': ''
        }
        
        if judgment_result['final_judgment']:
            # 有终结信号
            level_results = judgment_result['level_results']
            
            # 判断主要趋势方向
            trends = [r['current_trend'] for r in level_results.values() if r['current_trend'] != 'unknown']
            if trends:
                main_trend = max(set(trends), key=trends.count)
                
                if main_trend == 'up':
                    suggestion['action'] = 'sell'
                    suggestion['reasoning'] = '上升线段终结，建议卖出'
                elif main_trend == 'down':
                    suggestion['action'] = 'buy'
                    suggestion['reasoning'] = '下降线段终结，建议买入'
                
                # 风险等级评估
                if judgment_result['confidence'] >= 80:
                    suggestion['risk_level'] = 'low'
                elif judgment_result['confidence'] >= 60:
                    suggestion['risk_level'] = 'medium'
                else:
                    suggestion['risk_level'] = 'high'
        
        return suggestion

    def comprehensive_market_analysis(self, timeframe='15m', limit=200):
        """
        综合市场分析：结合多级别分析和市场分析器
        
        参数:
            timeframe: 主要分析时间周期
            limit: K线数据数量
            
        返回:
            dict: 综合分析结果
        """
        try:
            logger.info("开始执行 {} 综合市场分析".format(self.symbol))
            
            # 1. 获取多级别数据
            multi_data = self.get_multi_level_data(limit=limit)
            if not multi_data:
                logger.error("无法获取 {} 的多级别K线数据".format(self.symbol))
                return None
            
            # 2. 对每个时间周期执行缠论分析和买卖点识别
            multi_level_chan_analysis = {}
            multi_level_market_analysis = {}
            
            for tf, df in multi_data.items():
                try:
                    logger.info("分析 {} 级别数据".format(tf))
                    
                    # 执行缠论分析
                    analyzer = self.analyzers[tf]
                    analyzer.set_klines(df)
                    
                    # 获取分型、笔、线段
                    fx_list = analyzer.find_fractals()
                    bi_list = analyzer.identify_bi()
                    xd_list = analyzer.identify_xd()
                    
                    # 识别中枢和买卖点
                    try:
                        plotter = ChartPlotter()
                        
                        # 识别中枢区域
                        pivot_zones = plotter.identify_pivot_zones(bi_list, df)
                        
                        # 识别各类买卖点
                        buy_points1, sell_points1 = plotter.identify_first_buy_sell_points(bi_list, df, pivot_zones)
                        buy_points2, sell_points2 = plotter.identify_second_buy_sell_points(bi_list, df, pivot_zones)
                        buy_points3, sell_points3 = plotter.identify_third_buy_sell_points(bi_list, df, pivot_zones)
                        
                        # 汇总所有买卖点
                        buy_points_all = {
                            '1': (buy_points1, sell_points1),
                            '2': (buy_points2, sell_points2), 
                            '3': (buy_points3, sell_points3)
                        }
                        sell_points_all = buy_points_all  # 卖点包含在buy_points_all中
                        
                    except ImportError:
                        logger.error("无法导入ChartPlotter，请确保chart_plotter.py文件存在并可以导入")
                        # 设置空的买卖点和中枢
                        pivot_zones = []
                        buy_points_all = {'1': ([], []), '2': ([], []), '3': ([], [])}
                        sell_points_all = buy_points_all
                    except Exception as e:
                        logger.error("ChartPlotter识别买卖点时出错: {}".format(str(e)))
                        # 设置空的买卖点和中枢
                        pivot_zones = []
                        buy_points_all = {'1': ([], []), '2': ([], []), '3': ([], [])}
                        sell_points_all = buy_points_all
                    
                    # 保存缠论分析结果
                    multi_level_chan_analysis[tf] = {
                        'fractal_count': len(fx_list),
                        'stroke_count': len(bi_list),
                        'segment_count': len(xd_list),
                        'pivot_count': len(pivot_zones),
                        'latest_trend': self._get_current_trend(analyzer),
                        'fx_list': fx_list,
                        'bi_list': bi_list,
                        'xd_list': xd_list,
                        'pivot_zones': pivot_zones
                    }
                    
                    # 使用MarketAnalyzer进行市场状态分析
                    market_analysis = self.market_analyzer.analyze_market_status(
                        df, bi_list, pivot_zones, buy_points_all, sell_points_all
                    )
                    multi_level_market_analysis[tf] = market_analysis
                    
                    logger.info("{} 级别分析完成".format(tf))
                    
                except Exception as e:
                    logger.error("分析 {} 级别时出错: {}".format(tf, str(e)))
                    # 即使某个级别分析失败，也继续分析其他级别
                    multi_level_chan_analysis[tf] = {
                        'fractal_count': 0,
                        'stroke_count': 0,
                        'segment_count': 0,
                        'pivot_count': 0,
                        'latest_trend': 'unknown',
                        'error': str(e)
                    }
                    multi_level_market_analysis[tf] = {
                        'buy_signals': [],
                        'sell_signals': [],
                        'operation_advice': {'action': 'wait', 'confidence': 0},
                        'error': str(e)
                    }
            
            # 3. 执行多级别联立分析
            multi_level_result = self.multi_level_judgment()
            
            # 4. 获取主要时间周期的当前价格
            main_df = multi_data.get(timeframe)
            if main_df is None:
                # 如果主要时间周期数据不存在，使用第一个可用的时间周期
                main_df = next(iter(multi_data.values()))
                timeframe = next(iter(multi_data.keys()))
            
            current_price = main_df['close'].iloc[-1]
            
            # 5. 综合分析结果
            comprehensive_result = {
                'symbol': self.symbol,
                'timeframe': timeframe,
                'timestamp': pd.Timestamp.now(),
                'current_price': current_price,
                
                # 多级别缠论分析结果
                'multi_level_chan_analysis': multi_level_chan_analysis,
                
                # 多级别市场分析结果
                'multi_level_market_analysis': multi_level_market_analysis,
                
                # 多级别联立分析结果
                'multi_level_analysis': multi_level_result,
                
                # 综合交易建议（基于主要时间周期的市场分析）
                'comprehensive_advice': self._generate_comprehensive_advice(
                    multi_level_market_analysis.get(timeframe, {}), 
                    multi_level_result, 
                    current_price
                )
            }
            
            logger.info("{} 综合市场分析完成".format(self.symbol))
            return comprehensive_result
            
        except Exception as e:
            logger.error("综合市场分析失败: {}".format(str(e)))
            return None
    
    def _generate_comprehensive_advice(self, market_analysis, multi_level_result, current_price):
        """
        生成综合交易建议
        
        参数:
            market_analysis: 市场分析结果
            multi_level_result: 多级别分析结果
            current_price: 当前价格
            
        返回:
            dict: 综合交易建议
        """
        advice = {
            'final_action': 'wait',
            'confidence': 0,
            'reasoning': [],
            'risk_level': 'medium',
            'entry_price': current_price,
            'stop_loss': None,
            'take_profit': None,
            'position_size': 'light'
        }
        
        # 获取市场分析建议
        market_advice = market_analysis['operation_advice']
        
        # 获取多级别分析建议
        multi_level_advice = self.get_trading_suggestion(multi_level_result)
        
        # 综合判断逻辑
        confidence_score = 0
        reasons = []
        
        # 1. 市场分析权重 (40%)
        if market_advice['action'] != 'wait':
            confidence_score += market_advice['confidence'] * 0.4
            reasons.append("市场分析建议{}，{}".format(market_advice['action'], market_advice['description']))
            advice['final_action'] = market_advice['action']
            advice['stop_loss'] = market_advice['stop_loss']
            advice['take_profit'] = market_advice['take_profit_conservative']
            advice['position_size'] = market_advice['position_size']
        
        # 2. 多级别分析权重 (35%)
        if multi_level_result['final_judgment']:
            confidence_score += multi_level_result['confidence'] * 0.35
            reasons.append("多级别分析显示线段终结，置信度{}%".format(multi_level_result['confidence']))
            
            # 如果多级别分析与市场分析方向一致，增加置信度
            if multi_level_advice['action'] == market_advice['action']:
                confidence_score += 15
                reasons.append("多级别分析与市场分析方向一致")
            elif multi_level_advice['action'] != 'hold':
                # 如果方向不一致，降低置信度并建议等待
                confidence_score -= 20
                reasons.append("多级别分析与市场分析方向不一致，建议谨慎")
                advice['final_action'] = 'wait'
        
        # 3. 趋势一致性加分 (15%)
        if multi_level_result.get('trend_consistency', False):
            confidence_score += 15
            reasons.append("多级别趋势一致")
        
        # 4. 信号强度评估 (10%)
        strong_signals = len([s for s in market_analysis['buy_signals'] + market_analysis['sell_signals'] 
                            if s['strength'] >= 8])
        if strong_signals > 0:
            confidence_score += 10
            reasons.append("发现{}个强信号".format(strong_signals))
        
        # 最终建议
        advice['confidence'] = min(int(confidence_score), 100)
        advice['reasoning'] = reasons
        
        # 风险等级评估
        if advice['confidence'] >= 80:
            advice['risk_level'] = 'low'
        elif advice['confidence'] >= 60:
            advice['risk_level'] = 'medium'
        else:
            advice['risk_level'] = 'high'
            
        # 如果置信度太低，建议等待
        if advice['confidence'] < 50:
            advice['final_action'] = 'wait'
            advice['reasoning'].append("综合置信度不足，建议等待更明确信号")
        
        return advice
    
    def print_comprehensive_report(self, analysis_result):
        """
        打印综合分析报告
        
        参数:
            analysis_result: 综合分析结果
        """
        if not analysis_result:
            print("❌ 分析结果为空")
            return
        
        print("\n" + "="*100)
        print("📊 综合市场分析报告")
        print("="*100)
        
        print(f"🏷️  交易对: {analysis_result['symbol']}")
        print(f"⏰ 分析时间: {analysis_result['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💰 当前价格: {analysis_result['current_price']:.4f}")
        print(f"📈 主要周期: {analysis_result['timeframe']}")
        
        # 缠论分析摘要
        chan = analysis_result['multi_level_chan_analysis']
        print(f"\n📐 缠论分析摘要:")
        for tf, analysis in chan.items():
            if 'error' in analysis:
                print(f"   {tf}: ❌ 分析失败 - {analysis['error']}")
            else:
                print(f"   {tf}:")
                print(f"     分型: {analysis['fractal_count']} | 笔: {analysis['stroke_count']} | 线段: {analysis['segment_count']} | 中枢: {analysis['pivot_count']}")
                print(f"     当前趋势: {analysis['latest_trend']}")
        
        # 多级别分析摘要
        multi = analysis_result['multi_level_analysis']
        print(f"\n🔄 多级别分析:")
        print(f"   线段终结: {'✅ 是' if multi['final_judgment'] else '❌ 否'}")
        print(f"   置信度: {multi['confidence']}%")
        print(f"   趋势一致: {'✅ 是' if multi['trend_consistency'] else '❌ 否'}")
        print(f"   判断理由: {multi['reason']}")
        
        # 市场分析详情
        market = analysis_result['multi_level_market_analysis']
        print(f"\n📊 市场分析详情:")
        
        for tf, analysis in market.items():
            if 'error' in analysis:
                print(f"   {tf}: ❌ 分析失败 - {analysis['error']}")
                continue
                
            print(f"\n   📈 {tf} 级别:")
            
            # 趋势分析
            if 'trend_analysis' in analysis:
                trend = analysis['trend_analysis']
                print(f"     趋势: {trend['description']}")
            
            # 买卖信号
            if analysis.get('buy_signals'):
                print(f"     🟢 买入信号 ({len(analysis['buy_signals'])}个):")
                for i, signal in enumerate(analysis['buy_signals'][:3]):
                    print(f"        {i+1}. {signal['type']} - 强度:{signal['strength']}/10 - "
                          f"价格:{signal['price']:.4f} - {signal['time_ago_minutes']:.0f}分钟前")
            
            if analysis.get('sell_signals'):
                print(f"     🔴 卖出信号 ({len(analysis['sell_signals'])}个):")
                for i, signal in enumerate(analysis['sell_signals'][:3]):
                    print(f"        {i+1}. {signal['type']} - 强度:{signal['strength']}/10 - "
                          f"价格:{signal['price']:.4f} - {signal['time_ago_minutes']:.0f}分钟前")
            
            if not analysis.get('buy_signals') and not analysis.get('sell_signals'):
                print(f"     ⏳ 当前没有明确的买卖信号")
            
            # 操作建议
            if 'operation_advice' in analysis:
                advice = analysis['operation_advice']
                action_emoji = {'buy': '🟢 买入', 'sell': '🔴 卖出', 'wait': '🟡 等待'}
                print(f"     建议操作: {action_emoji.get(advice['action'], advice['action'])}")
                print(f"     置信度: {advice.get('confidence', 0)}/100")
        
        # 综合交易建议
        advice = analysis_result['comprehensive_advice']
        print(f"\n💡 综合交易建议:")
        action_emoji = {'buy': '🟢 买入', 'sell': '🔴 卖出', 'wait': '🟡 等待'}
        risk_emoji = {'low': '🟢', 'medium': '🟡', 'high': '🔴'}
        
        print(f"   操作建议: {action_emoji.get(advice['final_action'], advice['final_action'])}")
        print(f"   综合置信度: {advice['confidence']}/100")
        print(f"   风险等级: {risk_emoji.get(advice['risk_level'], '')} {advice['risk_level']}")
        
        if advice['final_action'] != 'wait':
            print(f"   入场价格: {advice['entry_price']:.4f}")
            if advice['stop_loss']:
                print(f"   止损位: {advice['stop_loss']:.4f}")
            if advice['take_profit']:
                print(f"   止盈位: {advice['take_profit']:.4f}")
            print(f"   建议仓位: {advice['position_size']}")
        
        print(f"\n🧠 决策理由:")
        for reason in advice['reasoning']:
            print(f"   • {reason}")
        
        # 获取主要时间周期的市场分析
        main_market = market.get(analysis_result['timeframe'], {})
        
        # 等待条件
        if main_market.get('waiting_conditions'):
            print(f"\n⏰ 等待条件:")
            for condition in main_market['waiting_conditions']:
                print(f"   • {condition}")
        
        # 风险提示
        if main_market.get('risk_warning'):
            print(f"\n⚠️ 风险提示:")
            for warning in main_market['risk_warning']:
                print(f"   • {warning}")
        
        print("\n" + "="*100)

    def plot_multi_timeframe_analysis(self, save_path=None, title=None):
        """
        绘制多时间周期缠论分析图表
        
        参数:
            save_path: 保存路径，如果为None则自动生成
            title: 图表标题，如果为None则使用默认标题
        
        返回:
            str: 保存的图表路径
        """
        try:
            # 导入绘图模块
            from chart_plotter import ChartPlotter
            
            # 获取多时间周期数据
            multi_data = self.get_multi_level_data(limit=200)
            
            if not multi_data:
                logger.error("无法绘制多时间周期图表：数据为空")
                return None
            
            # 对每个时间周期执行缠论分析
            fx_dict = {}
            bi_dict = {}
            xd_dict = {}
            
            for tf, df in multi_data.items():
                if df is not None and not df.empty:
                    analyzer = self.analyzers[tf]
                    analyzer.analyze(df)
                    
                    fx_dict[tf] = analyzer.fx_list
                    bi_dict[tf] = analyzer.bi_list
                    xd_dict[tf] = analyzer.xd_list
                    
                    # 为DataFrame添加交易对和时间周期信息，方便绘图
                    df.attrs['symbol'] = self.symbol
                    df.attrs['timeframe'] = tf
            
            # 创建绘图器
            plotter = ChartPlotter()
            
            # 生成默认标题（如果未提供）
            if title is None:
                title = f"{self.symbol} 多时间周期缠论分析"
            
            # 绘制多时间周期分析图表
            chart_path = plotter.plot_multi_timeframe(
                data_dict=multi_data,
                symbol=self.symbol,
                fx_dict=fx_dict,
                bi_dict=bi_dict,
                xd_dict=xd_dict,
                save_path=save_path,
                title=title
            )
            
            logger.info("多时间周期缠论分析图表已保存: {}".format(chart_path))
            return chart_path
            
        except ImportError:
            logger.error("缺少绘图模块，请安装chart_plotter.py")
            return None
        except Exception as e:
            logger.error("绘制多时间周期分析图表时出错: {}".format(str(e)))
            return None

    def check_realtime_signals(self, timeframe='15m'):
        """
        实时检查当前是否有买卖点信号形成
        
        参数:
            timeframe: 要检查的时间周期
            
        返回:
            dict: 实时信号结果，包括买卖点类型、强度和推荐操作
        """
        logger.info("开始检查 {} {} 实时买卖点信号".format(self.symbol, timeframe))
        result = {
            'timestamp': pd.Timestamp.now(),
            'symbol': self.symbol,
            'timeframe': timeframe,
            'buy_signals': [],
            'sell_signals': [],
            'recommendation': 'wait',
            'signal_strength': 0,
            'message': ''
        }
        
        try:
            # 1. 获取最新K线数据
            if timeframe not in self.data_fetchers:
                result['message'] = f"不支持{timeframe}周期，可用周期: {list(self.data_fetchers.keys())}"
                return result
                
            df = self.data_fetchers[timeframe].get_klines(symbol=self.symbol, limit=200)
            if df.empty:
                result['message'] = f"未获取到{timeframe}周期数据"
                return result
            
            # 2. 执行缠论分析
            analyzer = self.analyzers[timeframe]
            analyzer.analyze(df)
            
            # 3. 提取关键数据
            fx_list = analyzer.fx_list
            bi_list = analyzer.bi_list
            xd_list = analyzer.xd_list
            
            if len(bi_list) < 2:
                result['message'] = "笔的数量不足，无法判断买卖点"
                return result
            
            # 4. 识别中枢和买卖点
            try:
                from chart_plotter import ChartPlotter
                plotter = ChartPlotter()
                
                # 识别中枢
                pivot_zones = plotter.identify_pivot_zones(bi_list, df)
                
                # 识别各类买卖点
                buy_points1, sell_points1 = plotter.identify_first_buy_sell_points(bi_list, df, pivot_zones)
                buy_points2, sell_points2 = plotter.identify_second_buy_sell_points(bi_list, df, pivot_zones)
                buy_points3, sell_points3 = plotter.identify_third_buy_sell_points(bi_list, df, pivot_zones)
                
                # 5. 判断最近的买卖点信号（最后8根K线内）
                current_index = len(df) - 1
                recent_range = 8  # 扩大到最近8根K线
                
                # 记录最后一个中枢的位置，用于判断第二类买卖点
                last_pivot_start = -1
                last_pivot_end = -1
                if pivot_zones:
                    last_pivot = pivot_zones[-1]
                    last_pivot_start = last_pivot[0]
                    last_pivot_end = last_pivot[1]
                
                # 检查第一类买卖点
                for i, (idx, price, strength) in enumerate(buy_points1):
                    # 检查是否是最近形成的买点
                    if (current_index - recent_range <= idx <= current_index) or (i == len(buy_points1) - 1 and idx > current_index - 15):
                        # 计算止盈止损点
                        stop_loss, take_profit1, take_profit2, take_profit3 = self._calculate_stop_levels(
                            df, bi_list, pivot_zones, idx, price, 'buy', timeframe)
                        
                        # 创建基础信号
                        base_signal = {
                            'type': '第一类买点',
                            'index': idx,
                            'price': price,
                            'strength': strength + 1,  # 增加强度以提高优先级
                            'bar_position': current_index - idx,  # 0表示当前K线，1表示前一根
                            'description': f"第一类买点 - 向下笔的底分型确认，下跌趋势结束信号",
                            'stop_loss': stop_loss,
                            'take_profit1': take_profit1,
                            'take_profit2': take_profit2,
                            'take_profit3': take_profit3
                        }

                        # 增强信号可信度分析
                        enhanced_signal = self._enhance_signal_credibility(base_signal, df, bi_list, pivot_zones, timeframe)
                        result['buy_signals'].append(enhanced_signal)
                
                for i, (idx, price, strength) in enumerate(sell_points1):
                    # 检查是否是最近形成的卖点
                    if (current_index - recent_range <= idx <= current_index) or (i == len(sell_points1) - 1 and idx > current_index - 15):
                        # 计算止盈止损点
                        stop_loss, take_profit1, take_profit2, take_profit3 = self._calculate_stop_levels(
                            df, bi_list, pivot_zones, idx, price, 'sell', timeframe)
                        
                        # 创建基础信号
                        base_signal = {
                            'type': '第一类卖点',
                            'index': idx,
                            'price': price,
                            'strength': strength + 1,  # 增加强度以提高优先级
                            'bar_position': current_index - idx,
                            'description': f"第一类卖点 - 向上笔的顶分型确认，上涨趋势结束信号",
                            'stop_loss': stop_loss,
                            'take_profit1': take_profit1,
                            'take_profit2': take_profit2,
                            'take_profit3': take_profit3
                        }

                        # 增强信号可信度分析
                        enhanced_signal = self._enhance_signal_credibility(base_signal, df, bi_list, pivot_zones, timeframe)
                        result['sell_signals'].append(enhanced_signal)
                
                # 检查第二类买卖点
                for i, (idx, price, strength, pivot_idx) in enumerate(buy_points2):
                    # 检查是否是最近形成的买点或者是否与最近中枢有关
                    is_recent = current_index - recent_range <= idx <= current_index
                    is_near_last_pivot = last_pivot_end > 0 and idx > last_pivot_end - 5
                    
                    if is_recent or is_near_last_pivot or (i == len(buy_points2) - 1 and idx > current_index - 15):
                        # 计算当前价格与买点价格的偏差百分比
                        price_diff_pct = abs((df['close'].iloc[-1] - price) / price * 100)
                        # 如果当前价格接近买点价格，则增加强度
                        extra_strength = 1 if price_diff_pct < 2 else 0
                        
                        # 计算止盈止损点
                        stop_loss, take_profit1, take_profit2, take_profit3 = self._calculate_stop_levels(
                            df, bi_list, pivot_zones, idx, price, 'buy', timeframe)
                        
                        # 创建基础信号
                        base_signal = {
                            'type': '第二类买点',
                            'index': idx,
                            'price': price,
                            'strength': strength + extra_strength,
                            'bar_position': current_index - idx,
                            'description': f"第二类买点 - 下跌中枢完成后的低点回调买入信号",
                            'price_diff': f"{price_diff_pct:.2f}%",
                            'stop_loss': stop_loss,
                            'take_profit1': take_profit1,
                            'take_profit2': take_profit2,
                            'take_profit3': take_profit3
                        }

                        # 增强信号可信度分析
                        enhanced_signal = self._enhance_signal_credibility(base_signal, df, bi_list, pivot_zones, timeframe)
                        result['buy_signals'].append(enhanced_signal)
                
                for i, (idx, price, strength, pivot_idx) in enumerate(sell_points2):
                    # 检查是否是最近形成的卖点或者是否与最近中枢有关
                    is_recent = current_index - recent_range <= idx <= current_index
                    is_near_last_pivot = last_pivot_end > 0 and idx > last_pivot_end - 5
                    
                    if is_recent or is_near_last_pivot or (i == len(sell_points2) - 1 and idx > current_index - 15):
                        # 计算当前价格与卖点价格的偏差百分比
                        price_diff_pct = abs((df['close'].iloc[-1] - price) / price * 100)
                        # 如果当前价格接近卖点价格，则增加强度
                        extra_strength = 1 if price_diff_pct < 2 else 0
                        
                        # 计算止盈止损点
                        stop_loss, take_profit1, take_profit2, take_profit3 = self._calculate_stop_levels(
                            df, bi_list, pivot_zones, idx, price, 'sell', timeframe)
                        
                        # 创建基础信号
                        base_signal = {
                            'type': '第二类卖点',
                            'index': idx,
                            'price': price,
                            'strength': strength + extra_strength,
                            'bar_position': current_index - idx,
                            'description': f"第二类卖点 - 上涨中枢完成后的高点回调卖出信号",
                            'price_diff': f"{price_diff_pct:.2f}%",
                            'stop_loss': stop_loss,
                            'take_profit1': take_profit1,
                            'take_profit2': take_profit2,
                            'take_profit3': take_profit3
                        }

                        # 增强信号可信度分析
                        enhanced_signal = self._enhance_signal_credibility(base_signal, df, bi_list, pivot_zones, timeframe)
                        result['sell_signals'].append(enhanced_signal)
                
                # 检查第三类买卖点
                for i, (idx, price, strength, pivot_idx, reason) in enumerate(buy_points3):
                    # 检查是否是最近形成的买点或与最后中枢相关
                    is_recent = current_index - recent_range <= idx <= current_index
                    is_near_last_pivot = last_pivot_end > 0 and idx > last_pivot_end - 8
                    
                    if is_recent or is_near_last_pivot or (i == len(buy_points3) - 1 and idx > current_index - 15):
                        # 计算当前价格与买点价格的偏差百分比
                        price_diff_pct = abs((df['close'].iloc[-1] - price) / price * 100)
                        # 如果当前价格接近买点价格，则增加强度
                        extra_strength = 2 if price_diff_pct < 1.5 else 1 if price_diff_pct < 3 else 0
                        
                        # 计算止盈止损点
                        stop_loss, take_profit1, take_profit2, take_profit3 = self._calculate_stop_levels(
                            df, bi_list, pivot_zones, idx, price, 'buy', timeframe)
                        
                        # 创建基础信号
                        base_signal = {
                            'type': '第三类买点',
                            'index': idx,
                            'price': price,
                            'strength': strength + extra_strength,
                            'bar_position': current_index - idx,
                            'reason': reason,
                            'description': f"第三类买点 - {reason}",
                            'price_diff': f"{price_diff_pct:.2f}%",
                            'stop_loss': stop_loss,
                            'take_profit1': take_profit1,
                            'take_profit2': take_profit2,
                            'take_profit3': take_profit3
                        }

                        # 增强信号可信度分析
                        enhanced_signal = self._enhance_signal_credibility(base_signal, df, bi_list, pivot_zones, timeframe)
                        result['buy_signals'].append(enhanced_signal)
                
                for i, (idx, price, strength, pivot_idx, reason) in enumerate(sell_points3):
                    # 检查是否是最近形成的卖点或与最后中枢相关
                    is_recent = current_index - recent_range <= idx <= current_index
                    is_near_last_pivot = last_pivot_end > 0 and idx > last_pivot_end - 8
                    
                    if is_recent or is_near_last_pivot or (i == len(sell_points3) - 1 and idx > current_index - 15):
                        # 计算当前价格与卖点价格的偏差百分比
                        price_diff_pct = abs((df['close'].iloc[-1] - price) / price * 100)
                        # 如果当前价格接近卖点价格，则增加强度
                        extra_strength = 2 if price_diff_pct < 1.5 else 1 if price_diff_pct < 3 else 0
                        
                        # 计算止盈止损点
                        stop_loss, take_profit1, take_profit2, take_profit3 = self._calculate_stop_levels(
                            df, bi_list, pivot_zones, idx, price, 'sell', timeframe)
                        
                        # 创建基础信号
                        base_signal = {
                            'type': '第三类卖点',
                            'index': idx,
                            'price': price,
                            'strength': strength + extra_strength,
                            'bar_position': current_index - idx,
                            'reason': reason,
                            'description': f"第三类卖点 - {reason}",
                            'price_diff': f"{price_diff_pct:.2f}%",
                            'stop_loss': stop_loss,
                            'take_profit1': take_profit1,
                            'take_profit2': take_profit2,
                            'take_profit3': take_profit3
                        }

                        # 增强信号可信度分析
                        enhanced_signal = self._enhance_signal_credibility(base_signal, df, bi_list, pivot_zones, timeframe)
                        result['sell_signals'].append(enhanced_signal)
                
                # 特殊情况：中枢上方运行的突破情况（可能是ETH当前情况）
                if pivot_zones and len(bi_list) > 4:
                    last_pivot = pivot_zones[-1]
                    pivot_high = last_pivot[3]  # 中枢上沿
                    last_close = df['close'].iloc[-1]
                    
                    # 检查最后一个笔
                    last_bi = bi_list[-1]
                    prev_bi = bi_list[-2]
                    
                    # 如果当前价格在中枢上方，且向上突破，可能形成第二类买点
                    if (last_close > pivot_high * 1.01) and last_bi[4] == 'up' and prev_bi[4] == 'down':
                        # 向上突破中枢
                        # 计算止盈止损点
                        stop_loss, take_profit1, take_profit2, take_profit3 = self._calculate_stop_levels(
                            df, bi_list, pivot_zones, current_index, last_close, 'buy', timeframe)
                        
                        # 创建基础信号
                        base_signal = {
                            'type': '潜在第二类买点',
                            'index': current_index,
                            'price': last_close,
                            'strength': 7,  # 给较高权重但不确定性较大
                            'bar_position': 0,
                            'description': f"潜在第二类买点 - 价格位于中枢上方，向上突破，可能形成突破回调买点",
                            'price_diff': f"{((last_close - pivot_high) / pivot_high * 100):.2f}%高于中枢上沿",
                            'stop_loss': stop_loss,
                            'take_profit1': take_profit1,
                            'take_profit2': take_profit2,
                            'take_profit3': take_profit3
                        }

                        # 增强信号可信度分析
                        enhanced_signal = self._enhance_signal_credibility(base_signal, df, bi_list, pivot_zones, timeframe)
                        result['buy_signals'].append(enhanced_signal)
                    
                    # 如果当前价格回调到中枢上沿附近，可能是第二类买点的最佳入场点
                    elif (0.99 * pivot_high <= last_close <= 1.03 * pivot_high) and last_bi[4] == 'down' and prev_bi[4] == 'up':
                        # 回调至中枢上沿
                        # 计算止盈止损点
                        stop_loss, take_profit1, take_profit2, take_profit3 = self._calculate_stop_levels(
                            df, bi_list, pivot_zones, current_index, last_close, 'buy', timeframe)
                        
                        # 创建基础信号
                        base_signal = {
                            'type': '中枢上沿买点',
                            'index': current_index,
                            'price': last_close,
                            'strength': 8,  # 较高权重
                            'bar_position': 0,
                            'description': f"中枢上沿买点 - 上涨突破中枢后回调至中枢上沿支撑位，良好买入机会",
                            'price_diff': f"{((last_close - pivot_high) / pivot_high * 100):.2f}%",
                            'stop_loss': stop_loss,
                            'take_profit1': take_profit1,
                            'take_profit2': take_profit2,
                            'take_profit3': take_profit3
                        }

                        # 增强信号可信度分析
                        enhanced_signal = self._enhance_signal_credibility(base_signal, df, bi_list, pivot_zones, timeframe)
                        result['buy_signals'].append(enhanced_signal)
                
                # 6. 根据信号生成建议
                if result['buy_signals'] and not result['sell_signals']:
                    # 只有买点信号
                    strongest_buy = max(result['buy_signals'], key=lambda x: x['strength'])
                    # 使用智能上下文感知系统判断信号有效性
                    should_recommend, recommendation_reason = self._intelligent_signal_validation(
                        strongest_buy, df, timeframe, 'buy'
                    )
                    if should_recommend:
                        result['recommendation'] = 'buy'
                        result['signal_strength'] = strongest_buy['strength']
                        result['stop_loss'] = strongest_buy.get('stop_loss')
                        result['take_profit1'] = strongest_buy.get('take_profit1')
                        result['take_profit2'] = strongest_buy.get('take_profit2')
                        result['take_profit3'] = strongest_buy.get('take_profit3')
                        result['message'] = "发现{}，强度: {}/10，位于{}根K线前，{}，止损: {}，止盈1: {}，止盈2: {}，止盈3: {}".format(
                            strongest_buy['type'], strongest_buy['strength'], strongest_buy['bar_position'],
                            recommendation_reason, strongest_buy.get('stop_loss'), strongest_buy.get('take_profit1'),
                            strongest_buy.get('take_profit2'), strongest_buy.get('take_profit3'))
                    else:
                        result['recommendation'] = 'wait'
                        result['signal_strength'] = strongest_buy['strength']
                        result['message'] = "发现{}，但{}，建议观望或等待次级别进场时机".format(
                            strongest_buy['type'], recommendation_reason)
                
                elif result['sell_signals'] and not result['buy_signals']:
                    # 只有卖点信号
                    strongest_sell = max(result['sell_signals'], key=lambda x: x['strength'])
                    # 使用智能上下文感知系统判断信号有效性
                    should_recommend, recommendation_reason = self._intelligent_signal_validation(
                        strongest_sell, df, timeframe, 'sell'
                    )
                    if should_recommend:
                        result['recommendation'] = 'sell'
                        result['signal_strength'] = strongest_sell['strength']
                        result['stop_loss'] = strongest_sell.get('stop_loss')
                        result['take_profit1'] = strongest_sell.get('take_profit1')
                        result['take_profit2'] = strongest_sell.get('take_profit2')
                        result['take_profit3'] = strongest_sell.get('take_profit3')
                        result['message'] = "发现{}，强度: {}/10，位于{}根K线前，{}，止损: {}，止盈1: {}，止盈2: {}，止盈3: {}".format(
                            strongest_sell['type'], strongest_sell['strength'], strongest_sell['bar_position'],
                            recommendation_reason, strongest_sell.get('stop_loss'), strongest_sell.get('take_profit1'),
                            strongest_sell.get('take_profit2'), strongest_sell.get('take_profit3'))
                    else:
                        result['recommendation'] = 'wait'
                        result['signal_strength'] = strongest_sell['strength']
                        result['message'] = "发现{}，但{}，建议观望或等待次级别进场时机".format(
                            strongest_sell['type'], recommendation_reason)
                
                elif result['buy_signals'] and result['sell_signals']:
                    # 同时有买卖点信号，使用智能上下文感知系统
                    strongest_buy = max(result['buy_signals'], key=lambda x: x['strength'])
                    strongest_sell = max(result['sell_signals'], key=lambda x: x['strength'])

                    # 使用智能系统验证两个信号
                    buy_should_recommend, buy_reason = self._intelligent_signal_validation(
                        strongest_buy, df, timeframe, 'buy'
                    )
                    sell_should_recommend, sell_reason = self._intelligent_signal_validation(
                        strongest_sell, df, timeframe, 'sell'
                    )

                    # 计算时效性权重的综合评分（作为备用决策）
                    def calculate_weighted_score(signal):
                        """计算包含时效性权重的综合评分"""
                        base_strength = signal['strength']
                        bar_position = signal['bar_position']

                        # 时效性权重：越近的信号权重越高
                        if bar_position == 0:
                            time_weight = 2.0  # 当前K线，最高权重
                        elif bar_position <= 2:
                            time_weight = 1.5  # 2根K线内，高权重
                        elif bar_position <= 5:
                            time_weight = 1.2  # 5根K线内，中等权重
                        elif bar_position <= 10:
                            time_weight = 1.0  # 10根K线内，正常权重
                        else:
                            time_weight = 0.5  # 超过10根K线，低权重

                        return base_strength * time_weight

                    buy_weighted_score = calculate_weighted_score(strongest_buy)
                    sell_weighted_score = calculate_weighted_score(strongest_sell)

                    # 智能决策逻辑
                    if buy_should_recommend and sell_should_recommend:
                        # 两个信号都通过智能验证，使用加权评分决策
                        if buy_weighted_score > sell_weighted_score * 1.1:  # 买入信号明显更强
                            result['recommendation'] = 'buy'
                            result['signal_strength'] = strongest_buy['strength']
                            result['stop_loss'] = strongest_buy.get('stop_loss')
                            result['take_profit1'] = strongest_buy.get('take_profit1')
                            result['take_profit2'] = strongest_buy.get('take_profit2')
                            result['take_profit3'] = strongest_buy.get('take_profit3')
                            result['message'] = "买卖信号同时存在，买点综合评分显著更高（{:.1f} vs {:.1f}），{}，强度: {}/10，{}，止损: {}，止盈1: {}，止盈2: {}，止盈3: {}".format(
                                buy_weighted_score, sell_weighted_score, strongest_buy['type'], strongest_buy['strength'], buy_reason,
                                strongest_buy.get('stop_loss'), strongest_buy.get('take_profit1'),
                                strongest_buy.get('take_profit2'), strongest_buy.get('take_profit3'))
                        elif sell_weighted_score > buy_weighted_score * 1.1:  # 卖出信号明显更强
                            result['recommendation'] = 'sell'
                            result['signal_strength'] = strongest_sell['strength']
                            result['stop_loss'] = strongest_sell.get('stop_loss')
                            result['take_profit1'] = strongest_sell.get('take_profit1')
                            result['take_profit2'] = strongest_sell.get('take_profit2')
                            result['take_profit3'] = strongest_sell.get('take_profit3')
                            result['message'] = "买卖信号同时存在，卖点综合评分显著更高（{:.1f} vs {:.1f}），{}，强度: {}/10，{}，止损: {}，止盈1: {}，止盈2: {}，止盈3: {}".format(
                                sell_weighted_score, buy_weighted_score, strongest_sell['type'], strongest_sell['strength'], sell_reason,
                                strongest_sell.get('stop_loss'), strongest_sell.get('take_profit1'),
                                strongest_sell.get('take_profit2'), strongest_sell.get('take_profit3'))
                        else:
                            # 评分接近，建议观望
                            result['recommendation'] = 'wait'
                            result['signal_strength'] = max(strongest_buy['strength'], strongest_sell['strength'])
                            result['message'] = "买卖信号都通过智能验证且强度接近（买:{:.1f}, 卖:{:.1f}），建议观望等待明确方向".format(
                                buy_weighted_score, sell_weighted_score)

                    elif buy_should_recommend and not sell_should_recommend:
                        # 只有买入信号通过智能验证
                        result['recommendation'] = 'buy'
                        result['signal_strength'] = strongest_buy['strength']
                        result['stop_loss'] = strongest_buy.get('stop_loss')
                        result['take_profit1'] = strongest_buy.get('take_profit1')
                        result['take_profit2'] = strongest_buy.get('take_profit2')
                        result['take_profit3'] = strongest_buy.get('take_profit3')
                        result['message'] = "买卖信号同时存在，买点通过智能验证（{}根K线前），{}，强度: {}/10，{}，止损: {}，止盈1: {}，止盈2: {}，止盈3: {}".format(
                            strongest_buy['bar_position'], strongest_buy['type'], strongest_buy['strength'], buy_reason,
                            strongest_buy.get('stop_loss'), strongest_buy.get('take_profit1'),
                            strongest_buy.get('take_profit2'), strongest_buy.get('take_profit3'))

                    elif sell_should_recommend and not buy_should_recommend:
                        # 只有卖出信号通过智能验证
                        result['recommendation'] = 'sell'
                        result['signal_strength'] = strongest_sell['strength']
                        result['stop_loss'] = strongest_sell.get('stop_loss')
                        result['take_profit1'] = strongest_sell.get('take_profit1')
                        result['take_profit2'] = strongest_sell.get('take_profit2')
                        result['take_profit3'] = strongest_sell.get('take_profit3')
                        result['message'] = "买卖信号同时存在，卖点通过智能验证（{}根K线前），{}，强度: {}/10，{}，止损: {}，止盈1: {}，止盈2: {}，止盈3: {}".format(
                            strongest_sell['bar_position'], strongest_sell['type'], strongest_sell['strength'], sell_reason,
                            strongest_sell.get('stop_loss'), strongest_sell.get('take_profit1'),
                            strongest_sell.get('take_profit2'), strongest_sell.get('take_profit3'))

                    else:
                        # 两个信号都未通过智能验证，但仍提供分析信息
                        result['recommendation'] = 'wait'
                        result['signal_strength'] = max(strongest_buy['strength'], strongest_sell['strength'])
                        result['message'] = "买卖信号同时存在但都未通过智能验证，买点：{}，卖点：{}，建议观望或等待次级别时机".format(
                            buy_reason, sell_reason)
                else:
                    result['message'] = "未发现明确的买卖点信号"

                # 7. 检查是否有等待的次级别确认信号
                has_confirmation, confirmation_info = self.check_next_level_confirmation(timeframe, result)

                if has_confirmation:
                    # 有确认信号，立即发出入场提醒
                    result['immediate_entry'] = confirmation_info
                    logger.info(f"检测到{timeframe}级别确认信号，立即入场提醒已生成")

            except ImportError as e:
                result['message'] = "无法导入ChartPlotter: {}".format(str(e))
                logger.error("无法导入ChartPlotter: {}".format(str(e)))
            except Exception as e:
                result['message'] = "分析买卖点时出错: {}".format(str(e))
                logger.error("分析买卖点时出错: {}".format(str(e)))
                
            logger.info("{} {} 实时信号检查完成: {}".format(self.symbol, timeframe, result['message']))
            return result
            
        except Exception as e:
            result['message'] = f"实时信号检查失败: {str(e)}"
            logger.error(f"实时信号检查失败: {str(e)}")
            return result

    def _calculate_stop_levels(self, df, bi_list, pivot_zones, idx, price, signal_type, timeframe):
        """
        计算买卖点的止盈止损位 - 基于缠论理论的改进版本
        
        参数:
            df: K线数据
            bi_list: 笔列表
            pivot_zones: 中枢列表
            idx: 买卖点所在K线索引
            price: 买卖点价格
            signal_type: 'buy'或'sell'
            timeframe: 时间周期
            
        返回:
            tuple: (止损位, 第一止盈位, 第二止盈位, 第三止盈位)
        """
        try:
            # 使用缠论分析器计算更精确的止盈止损
            from chan_analysis import ChanAnalysis
            
            # 创建缠论分析器实例
            chan_analyzer = ChanAnalysis(df)
            chan_analyzer.pivot_zones = pivot_zones
            
            # 确定买卖点类型（这里需要根据实际情况判断）
            buy_sell_point_type = self._determine_point_type(idx, price, bi_list, pivot_zones, signal_type)
            
            # 找到相关中枢
            related_pivot_idx = self._find_related_pivot(idx, pivot_zones)
            
            # 构建入场点信息
            entry_point = {
                'index': idx,
                'price': price,
                'timestamp': df['timestamp'].iloc[idx] if 'timestamp' in df.columns else str(idx)
            }
            
            # 使用缠论理论计算止盈止损
            targets = chan_analyzer.calculate_chan_profit_targets(
                entry_point=entry_point,
                signal_type=signal_type,
                buy_sell_point_type=buy_sell_point_type,
                related_pivot_idx=related_pivot_idx,
                current_level=timeframe
            )
            
            # 提取止损和止盈位
            stop_loss = targets.get('stop_loss')
            profit_targets = targets.get('profit_targets', [])
            
            # 按照原有格式返回三个止盈位
            take_profit1 = profit_targets[0]['price'] if len(profit_targets) > 0 else None
            take_profit2 = profit_targets[1]['price'] if len(profit_targets) > 1 else None
            take_profit3 = profit_targets[2]['price'] if len(profit_targets) > 2 else None
            
            # 如果缠论计算失败，使用改进的传统方法
            if not stop_loss or not take_profit1:
                return self._calculate_stop_levels_improved_traditional(
                    df, bi_list, pivot_zones, idx, price, signal_type, timeframe
                )
            
            logger.info(f"使用缠论理论计算止盈止损: 止损={stop_loss}, 止盈1={take_profit1}, 止盈2={take_profit2}, 止盈3={take_profit3}")
            return stop_loss, take_profit1, take_profit2, take_profit3
            
        except Exception as e:
            logger.warning(f"缠论止盈止损计算失败，使用改进传统方法: {str(e)}")
            return self._calculate_stop_levels_improved_traditional(
                df, bi_list, pivot_zones, idx, price, signal_type, timeframe
            )

    def _intelligent_signal_validation(self, signal, df, timeframe, signal_type):
        """
        智能上下文感知系统：结合市场状态、利润空间等多维度判断信号有效性

        参数:
            signal: 信号对象
            df: K线数据
            timeframe: 时间级别
            signal_type: 'buy' 或 'sell'

        返回:
            tuple: (是否推荐, 推荐理由)
        """
        try:
            # 1. 分析市场上下文
            market_context = self._analyze_market_context(df)

            # 2. 计算利润空间
            profit_analysis = self._calculate_profit_potential(signal, df, market_context)

            # 3. 评估信号时效性
            time_validity = self._calculate_context_adjusted_validity(signal, market_context, timeframe)

            # 4. 综合判断
            return self._make_intelligent_recommendation(
                signal, market_context, profit_analysis, time_validity, timeframe
            )

        except Exception as e:
            logger.error(f"智能信号验证失败: {str(e)}")
            # 降级到简单时效性检查
            is_valid = signal['bar_position'] <= 10
            reason = f"简单时效性检查: {'有效' if is_valid else '已过时'}"
            return is_valid, reason

    def _analyze_market_context(self, df):
        """分析市场上下文"""
        current_price = df['close'].iloc[-1]

        # 趋势强度分析
        ma_short = df['close'].iloc[-5:].mean()
        ma_medium = df['close'].iloc[-10:].mean()
        ma_long = df['close'].iloc[-20:].mean()

        trend_strength = abs(ma_short - ma_long) / ma_long
        trend_direction = 'up' if ma_short > ma_long else 'down'
        is_trending = trend_strength > 0.02

        # 波动率分析
        returns = df['close'].pct_change().iloc[-20:]
        volatility = returns.std()
        is_high_vol = volatility > 0.03

        # 价格位置分析
        recent_high = df['high'].iloc[-20:].max()
        recent_low = df['low'].iloc[-20:].min()
        price_position = (current_price - recent_low) / (recent_high - recent_low) if recent_high != recent_low else 0.5

        # 成交量分析
        avg_volume = df['volume'].iloc[-20:].mean()
        current_volume = df['volume'].iloc[-1]
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0

        return {
            'current_price': current_price,
            'trend_strength': trend_strength,
            'trend_direction': trend_direction,
            'is_trending': is_trending,
            'volatility': volatility,
            'is_high_vol': is_high_vol,
            'price_position': price_position,
            'volume_ratio': volume_ratio,
            'ma_short': ma_short,
            'ma_medium': ma_medium,
            'ma_long': ma_long
        }

    def _calculate_profit_potential(self, signal, df, market_context):
        """计算利润空间潜力"""
        current_price = market_context['current_price']

        # 获取止盈目标
        take_profit1 = signal.get('take_profit1')
        take_profit2 = signal.get('take_profit2')
        take_profit3 = signal.get('take_profit3')
        stop_loss = signal.get('stop_loss')

        profit_analysis = {
            'has_targets': False,
            'profit_potential': 0,
            'risk_reward_ratio': 0,
            'remaining_space': 0,
            'is_profitable': False,
            'next_level_suggestion': None
        }

        if take_profit1 and stop_loss:
            profit_analysis['has_targets'] = True

            # 计算利润空间
            if signal['type'].startswith('买') or 'buy' in signal['type'].lower():
                # 买入信号
                profit_pct1 = (take_profit1 - current_price) / current_price * 100
                risk_pct = (current_price - stop_loss) / current_price * 100

                profit_analysis['profit_potential'] = profit_pct1
                profit_analysis['remaining_space'] = profit_pct1
                profit_analysis['risk_reward_ratio'] = profit_pct1 / risk_pct if risk_pct > 0 else 0
                profit_analysis['is_profitable'] = profit_pct1 > 1.0  # 至少1%利润空间

                # 次级别建议
                if profit_pct1 > 3.0:
                    profit_analysis['next_level_suggestion'] = f"利润空间充足({profit_pct1:.1f}%)，可等待次级别优化进场点"
                elif profit_pct1 > 1.5:
                    profit_analysis['next_level_suggestion'] = f"利润空间一般({profit_pct1:.1f}%)，建议次级别确认后进场"
                else:
                    profit_analysis['next_level_suggestion'] = f"利润空间有限({profit_pct1:.1f}%)，建议等待更好时机"

            else:
                # 卖出信号
                profit_pct1 = (current_price - take_profit1) / current_price * 100
                risk_pct = (stop_loss - current_price) / current_price * 100

                profit_analysis['profit_potential'] = profit_pct1
                profit_analysis['remaining_space'] = profit_pct1
                profit_analysis['risk_reward_ratio'] = profit_pct1 / risk_pct if risk_pct > 0 else 0
                profit_analysis['is_profitable'] = profit_pct1 > 1.0

                # 次级别建议
                if profit_pct1 > 3.0:
                    profit_analysis['next_level_suggestion'] = f"下跌空间充足({profit_pct1:.1f}%)，可等待次级别优化进场点"
                elif profit_pct1 > 1.5:
                    profit_analysis['next_level_suggestion'] = f"下跌空间一般({profit_pct1:.1f}%)，建议次级别确认后进场"
                else:
                    profit_analysis['next_level_suggestion'] = f"下跌空间有限({profit_pct1:.1f}%)，建议等待更好时机"

        return profit_analysis

    def _calculate_context_adjusted_validity(self, signal, market_context, timeframe):
        """根据上下文调整信号有效期"""
        base_validity = {
            '1m': 8, '5m': 12, '15m': 18, '30m': 25, '1h': 35
        }.get(timeframe, 12)

        adjusted_validity = base_validity

        # 根据信号强度调整
        if signal['strength'] >= 9:
            adjusted_validity *= 1.5  # 强信号延长50%
        elif signal['strength'] >= 7:
            adjusted_validity *= 1.2  # 中强信号延长20%
        elif signal['strength'] <= 5:
            adjusted_validity *= 0.8  # 弱信号缩短20%

        # 根据信号类型调整
        if '第一类' in signal['type']:
            adjusted_validity *= 1.3  # 第一类信号最重要
        elif '第三类' in signal['type']:
            adjusted_validity *= 0.9  # 第三类信号相对不重要

        # 根据市场状态调整
        if market_context['is_trending']:
            if '第一类' in signal['type'] or '线段终结' in signal['type']:
                adjusted_validity *= 1.4  # 趋势信号在趋势市场中延长
        else:
            if '中枢' in signal['type']:
                adjusted_validity *= 1.2  # 中枢信号在震荡市场中延长

        # 高波动市场缩短有效期
        if market_context['is_high_vol']:
            adjusted_validity *= 0.7

        return int(adjusted_validity)

    def _make_intelligent_recommendation(self, signal, market_context, profit_analysis, time_validity, timeframe):
        """综合判断是否推荐信号"""
        current_bar_position = signal['bar_position']

        # 基础时效性检查
        if current_bar_position > time_validity:
            # 信号已过时，但检查是否有足够利润空间等待次级别
            if profit_analysis['is_profitable'] and profit_analysis['remaining_space'] > 3.0:
                next_level_map = {'1h': '30m', '30m': '15m', '15m': '5m', '5m': '1m'}
                next_level = next_level_map.get(timeframe, '更小级别')

                # 设置等待次级别确认状态
                self._set_waiting_for_next_level(signal, timeframe, next_level, signal_type, profit_analysis)

                return False, f"信号过时({current_bar_position}>{time_validity}根K线)，但{profit_analysis['next_level_suggestion']}，建议关注{next_level}级别进场时机"
            else:
                return False, f"信号已过时({current_bar_position}>{time_validity}根K线)且利润空间不足({profit_analysis['remaining_space']:.1f}%)"

        # 利润空间检查
        if not profit_analysis['is_profitable']:
            return False, f"利润空间不足({profit_analysis['remaining_space']:.1f}%)，建议等待更好时机"

        # 风险收益比检查
        if profit_analysis['risk_reward_ratio'] < 1.5:
            next_level_map = {'1h': '30m', '30m': '15m', '15m': '5m', '5m': '1m'}
            next_level = next_level_map.get(timeframe, '更小级别')

            # 如果利润空间充足，设置等待次级别确认状态
            if profit_analysis['remaining_space'] > 2.0:
                self._set_waiting_for_next_level(signal, timeframe, next_level, signal_type, profit_analysis)

            return False, f"风险收益比不佳({profit_analysis['risk_reward_ratio']:.1f}:1)，{profit_analysis['next_level_suggestion']}，关注{next_level}级别"

        # 市场状态适配性检查
        signal_relevance = self._calculate_signal_relevance(signal, market_context)
        if signal_relevance < 6.5:
            return False, f"当前市场状态下信号相关性不足({signal_relevance:.1f}/10)，{profit_analysis['next_level_suggestion']}"

        # 通过所有检查，推荐操作
        reason_parts = []
        reason_parts.append(f"利润空间{profit_analysis['remaining_space']:.1f}%")
        reason_parts.append(f"风险收益比{profit_analysis['risk_reward_ratio']:.1f}:1")
        reason_parts.append(f"相关性{signal_relevance:.1f}/10")

        # 根据利润空间给出具体建议
        if profit_analysis['remaining_space'] > 5.0:
            reason_parts.append("建议直接进场")
        elif profit_analysis['remaining_space'] > 3.0:
            next_level_map = {'1h': '30m', '30m': '15m', '15m': '5m', '5m': '1m'}
            next_level = next_level_map.get(timeframe, '更小级别')
            reason_parts.append(f"可直接进场或等待{next_level}级别优化")
        else:
            next_level_map = {'1h': '30m', '30m': '15m', '15m': '5m', '5m': '1m'}
            next_level = next_level_map.get(timeframe, '更小级别')
            reason_parts.append(f"建议等待{next_level}级别确认")

        return True, "智能推荐: " + ", ".join(reason_parts)

    def _calculate_signal_relevance(self, signal, market_context):
        """计算信号在当前市场状态下的相关性"""
        relevance_score = signal['strength']

        # 根据价格位置调整
        if signal['type'].startswith('买') or 'buy' in signal['type'].lower():
            # 买入信号在低位更相关
            if market_context['price_position'] < 0.3:
                relevance_score *= 1.2
            elif market_context['price_position'] > 0.7:
                relevance_score *= 0.8
        else:
            # 卖出信号在高位更相关
            if market_context['price_position'] > 0.7:
                relevance_score *= 1.2
            elif market_context['price_position'] < 0.3:
                relevance_score *= 0.8

        # 根据趋势状态调整
        if market_context['is_trending']:
            if '第一类' in signal['type']:
                relevance_score *= 1.3  # 趋势转折信号在趋势市场更重要

        # 根据成交量调整
        if market_context['volume_ratio'] > 1.5:
            relevance_score *= 1.1  # 放量确认
        elif market_context['volume_ratio'] < 0.7:
            relevance_score *= 0.9  # 缩量减分

        return min(10.0, relevance_score)

    def set_waiting_for_confirmation(self, parent_level, target_level, direction, parent_signal, entry_targets):
        """
        设置等待次级别确认状态

        参数:
            parent_level: 父级别（如15m）
            target_level: 目标级别（如5m）
            direction: 方向 ('buy' or 'sell')
            parent_signal: 父级别信号信息
            entry_targets: 入场目标信息
        """
        from datetime import datetime

        self.waiting_for_confirmation = {
            'is_waiting': True,
            'target_level': target_level,
            'parent_level': parent_level,
            'parent_signal': parent_signal,
            'direction': direction,
            'entry_targets': entry_targets,
            'wait_start_time': datetime.now(),
            'max_wait_minutes': 120
        }

        logger.info(f"设置等待{target_level}级别确认状态：{direction}方向，来自{parent_level}级别信号")

    def check_waiting_timeout(self):
        """检查等待是否超时"""
        if not self.waiting_for_confirmation['is_waiting']:
            return False

        from datetime import datetime, timedelta

        wait_start = self.waiting_for_confirmation['wait_start_time']
        max_wait = self.waiting_for_confirmation['max_wait_minutes']

        if datetime.now() - wait_start > timedelta(minutes=max_wait):
            logger.info(f"等待{self.waiting_for_confirmation['target_level']}级别确认超时，清除等待状态")
            self.clear_waiting_state()
            return True

        return False

    def clear_waiting_state(self):
        """清除等待状态"""
        self.waiting_for_confirmation = {
            'is_waiting': False,
            'target_level': None,
            'parent_level': None,
            'parent_signal': None,
            'direction': None,
            'entry_targets': None,
            'wait_start_time': None,
            'max_wait_minutes': 120
        }
        logger.info("已清除等待次级别确认状态")

    def _set_waiting_for_next_level(self, signal, parent_level, target_level, signal_type, profit_analysis):
        """设置等待次级别确认状态的辅助方法"""
        try:
            # 构建入场目标信息
            entry_targets = {
                'stop_loss': signal.get('stop_loss'),
                'take_profit1': signal.get('take_profit1'),
                'take_profit2': signal.get('take_profit2'),
                'take_profit3': signal.get('take_profit3'),
                'profit_space': profit_analysis['remaining_space'],
                'risk_reward_ratio': profit_analysis['risk_reward_ratio']
            }

            # 设置等待状态
            self.set_waiting_for_confirmation(
                parent_level=parent_level,
                target_level=target_level,
                direction=signal_type,
                parent_signal=signal,
                entry_targets=entry_targets
            )

            logger.info(f"已设置等待{target_level}级别确认：{signal_type}方向，来自{parent_level}级别{signal['type']}")

        except Exception as e:
            logger.error(f"设置等待次级别状态失败: {str(e)}")

    def check_next_level_confirmation(self, timeframe, signals):
        """
        检查是否有等待的次级别确认信号

        参数:
            timeframe: 当前检查的时间级别
            signals: 当前级别的信号列表

        返回:
            tuple: (是否有确认信号, 确认信息)
        """
        if not self.waiting_for_confirmation['is_waiting']:
            return False, None

        # 检查是否超时
        if self.check_waiting_timeout():
            return False, "等待超时，已清除等待状态"

        # 检查是否是目标级别
        if timeframe != self.waiting_for_confirmation['target_level']:
            return False, None

        waiting_direction = self.waiting_for_confirmation['direction']
        parent_signal = self.waiting_for_confirmation['parent_signal']
        entry_targets = self.waiting_for_confirmation['entry_targets']

        # 检查当前级别是否有同方向的信号
        matching_signals = []

        if waiting_direction == 'buy':
            # 寻找买入确认信号
            for signal in signals.get('buy_signals', []):
                # 检查信号是否足够新鲜（3根K线内）
                if signal['bar_position'] <= 3:
                    # 检查信号强度是否足够（至少6分）
                    if signal['strength'] >= 6:
                        matching_signals.append(signal)
        else:
            # 寻找卖出确认信号
            for signal in signals.get('sell_signals', []):
                if signal['bar_position'] <= 3 and signal['strength'] >= 6:
                    matching_signals.append(signal)

        if matching_signals:
            # 找到确认信号，选择最强的
            best_signal = max(matching_signals, key=lambda x: x['strength'])

            # 生成立即入场提醒
            confirmation_info = self._generate_immediate_entry_alert(
                best_signal, parent_signal, entry_targets, timeframe
            )

            # 清除等待状态
            self.clear_waiting_state()

            return True, confirmation_info

        return False, None

    def _generate_immediate_entry_alert(self, confirm_signal, parent_signal, entry_targets, timeframe):
        """生成立即入场提醒"""
        direction = "买入" if self.waiting_for_confirmation['direction'] == 'buy' else "卖出"
        parent_level = self.waiting_for_confirmation['parent_level']

        alert_info = {
            'type': 'immediate_entry',
            'direction': self.waiting_for_confirmation['direction'],
            'timeframe': timeframe,
            'parent_level': parent_level,
            'confirm_signal': confirm_signal,
            'parent_signal': parent_signal,
            'entry_targets': entry_targets,
            'message': f"🚨 立即入场信号！{timeframe}级别确认{direction}！🚨\n"
                      f"父级别: {parent_level} - {parent_signal['type']}\n"
                      f"确认信号: {timeframe} - {confirm_signal['type']} (强度: {confirm_signal['strength']}/10)\n"
                      f"建议操作: 立即{direction}\n"
                      f"止损: {entry_targets['stop_loss']}\n"
                      f"止盈1: {entry_targets['take_profit1']}\n"
                      f"止盈2: {entry_targets['take_profit2']}\n"
                      f"止盈3: {entry_targets['take_profit3']}\n"
                      f"预期利润空间: {entry_targets['profit_space']:.1f}%\n"
                      f"风险收益比: {entry_targets['risk_reward_ratio']:.1f}:1"
        }

        return alert_info

    def _enhance_signal_credibility(self, signal, df, bi_list, pivot_zones, timeframe):
        """
        增强信号的可信度分析，提供详细的判断理由

        参数:
            signal: 信号字典
            df: K线数据
            bi_list: 笔列表
            pivot_zones: 中枢列表
            timeframe: 时间级别

        返回:
            dict: 增强后的信号信息，包含详细判断理由
        """
        try:
            enhanced_signal = signal.copy()

            # 基础信息
            signal_type = signal['type']
            price = signal['price']
            idx = signal['index']
            bar_position = signal['bar_position']
            current_price = df['close'].iloc[-1]

            # 1. 技术形态分析
            technical_analysis = self._analyze_technical_pattern(df, idx, signal_type)

            # 2. 中枢关系分析
            pivot_analysis = self._analyze_pivot_relationship(idx, price, pivot_zones, signal_type)

            # 3. 笔的结构分析
            bi_analysis = self._analyze_bi_structure(idx, bi_list, signal_type)

            # 4. 价格位置分析
            price_analysis = self._analyze_price_position(price, current_price, df, signal_type)

            # 5. 时效性分析
            timing_analysis = self._analyze_signal_timing(bar_position, timeframe)

            # 6. 成交量确认
            volume_analysis = self._analyze_volume_confirmation(df, idx, signal_type)

            # 7. 综合可信度评分
            credibility_score = self._calculate_credibility_score(
                technical_analysis, pivot_analysis, bi_analysis,
                price_analysis, timing_analysis, volume_analysis
            )

            # 8. 生成详细判断理由
            detailed_reason = self._generate_detailed_reason(
                signal_type, technical_analysis, pivot_analysis, bi_analysis,
                price_analysis, timing_analysis, volume_analysis, credibility_score
            )

            # 9. 风险提示
            risk_warnings = self._generate_risk_warnings(
                signal, technical_analysis, pivot_analysis, timing_analysis
            )

            # 增强信号信息
            enhanced_signal.update({
                'credibility_score': credibility_score,
                'detailed_reason': detailed_reason,
                'technical_analysis': technical_analysis,
                'pivot_analysis': pivot_analysis,
                'bi_analysis': bi_analysis,
                'price_analysis': price_analysis,
                'timing_analysis': timing_analysis,
                'volume_analysis': volume_analysis,
                'risk_warnings': risk_warnings,
                'confidence_level': self._get_confidence_level(credibility_score)
            })

            return enhanced_signal

        except Exception as e:
            logger.error(f"增强信号可信度分析失败: {str(e)}")
            # 返回原始信号，添加错误信息
            signal['detailed_reason'] = f"信号分析出错: {str(e)}"
            signal['credibility_score'] = 5.0  # 中等可信度
            signal['confidence_level'] = '中等'
            return signal

    def _analyze_technical_pattern(self, df, idx, signal_type):
        """分析技术形态"""
        try:
            # 获取信号点前后的价格数据
            start_idx = max(0, idx - 10)
            end_idx = min(len(df), idx + 5)
            price_data = df['close'].iloc[start_idx:end_idx]

            # 分析趋势
            if len(price_data) >= 5:
                recent_trend = 'up' if price_data.iloc[-1] > price_data.iloc[-5] else 'down'
                trend_strength = abs(price_data.iloc[-1] - price_data.iloc[-5]) / price_data.iloc[-5] * 100
            else:
                recent_trend = 'unclear'
                trend_strength = 0

            # 分析波动率
            volatility = price_data.std() / price_data.mean() * 100 if len(price_data) > 1 else 0

            # 分析支撑阻力
            resistance_level = price_data.max()
            support_level = price_data.min()
            current_price = df['close'].iloc[idx]

            # 价格在区间中的位置
            price_position = (current_price - support_level) / (resistance_level - support_level) if resistance_level > support_level else 0.5

            return {
                'trend': recent_trend,
                'trend_strength': trend_strength,
                'volatility': volatility,
                'resistance_level': resistance_level,
                'support_level': support_level,
                'price_position': price_position,
                'pattern_quality': 'good' if trend_strength > 2 and volatility < 5 else 'fair' if trend_strength > 1 else 'poor'
            }

        except Exception as e:
            logger.warning(f"技术形态分析失败: {str(e)}")
            return {'pattern_quality': 'unknown', 'trend': 'unclear'}

    def _analyze_pivot_relationship(self, idx, price, pivot_zones, signal_type):
        """分析与中枢的关系"""
        try:
            if not pivot_zones:
                return {'relationship': 'no_pivot', 'distance': 0, 'quality': 'poor'}

            # 找到最近的中枢
            nearest_pivot = None
            min_distance = float('inf')

            for pivot in pivot_zones:
                pivot_start, pivot_end, pivot_high, pivot_low = pivot[:4]
                distance = abs(idx - pivot_end)
                if distance < min_distance:
                    min_distance = distance
                    nearest_pivot = pivot

            if nearest_pivot is None:
                return {'relationship': 'no_pivot', 'distance': 0, 'quality': 'poor'}

            pivot_start, pivot_end, pivot_high, pivot_low = nearest_pivot[:4]
            pivot_center = (pivot_high + pivot_low) / 2

            # 分析价格与中枢的关系
            if price > pivot_high * 1.02:
                relationship = 'above_pivot'
            elif price < pivot_low * 0.98:
                relationship = 'below_pivot'
            elif pivot_low <= price <= pivot_high:
                relationship = 'inside_pivot'
            else:
                relationship = 'near_pivot'

            # 计算距离中枢的时间距离
            time_distance = idx - pivot_end

            # 评估质量
            if time_distance <= 5 and relationship in ['above_pivot', 'below_pivot']:
                quality = 'excellent'
            elif time_distance <= 10 and relationship != 'no_pivot':
                quality = 'good'
            elif time_distance <= 20:
                quality = 'fair'
            else:
                quality = 'poor'

            return {
                'relationship': relationship,
                'distance': time_distance,
                'pivot_high': pivot_high,
                'pivot_low': pivot_low,
                'pivot_center': pivot_center,
                'quality': quality
            }

        except Exception as e:
            logger.warning(f"中枢关系分析失败: {str(e)}")
            return {'relationship': 'unknown', 'distance': 0, 'quality': 'poor'}

    def _analyze_bi_structure(self, idx, bi_list, signal_type):
        """分析笔的结构"""
        try:
            if len(bi_list) < 3:
                return {'structure': 'insufficient', 'quality': 'poor'}

            # 找到信号点附近的笔
            relevant_bis = []
            for bi in bi_list:
                bi_start, bi_end = bi[0], bi[1]
                if abs(idx - bi_end) <= 10:  # 10根K线内的笔
                    relevant_bis.append(bi)

            if len(relevant_bis) < 2:
                return {'structure': 'unclear', 'quality': 'fair'}

            # 分析最近的笔结构
            last_bi = bi_list[-1]
            prev_bi = bi_list[-2] if len(bi_list) > 1 else None

            last_direction = last_bi[4] if len(last_bi) > 4 else 'unknown'
            prev_direction = prev_bi[4] if prev_bi and len(prev_bi) > 4 else 'unknown'

            # 判断结构质量
            if signal_type == 'buy' and last_direction == 'down' and prev_direction == 'up':
                structure = 'reversal_pattern'
                quality = 'good'
            elif signal_type == 'sell' and last_direction == 'up' and prev_direction == 'down':
                structure = 'reversal_pattern'
                quality = 'good'
            elif signal_type == 'buy' and last_direction == 'up':
                structure = 'continuation_pattern'
                quality = 'fair'
            elif signal_type == 'sell' and last_direction == 'down':
                structure = 'continuation_pattern'
                quality = 'fair'
            else:
                structure = 'unclear'
                quality = 'poor'

            return {
                'structure': structure,
                'last_direction': last_direction,
                'prev_direction': prev_direction,
                'quality': quality,
                'relevant_bis_count': len(relevant_bis)
            }

        except Exception as e:
            logger.warning(f"笔结构分析失败: {str(e)}")
            return {'structure': 'unknown', 'quality': 'poor'}

    def _analyze_price_position(self, signal_price, current_price, df, signal_type):
        """分析价格位置"""
        try:
            price_diff_pct = (current_price - signal_price) / signal_price * 100

            # 分析价格偏离程度
            if abs(price_diff_pct) < 0.5:
                position = 'very_close'
                quality = 'excellent'
            elif abs(price_diff_pct) < 1.0:
                position = 'close'
                quality = 'good'
            elif abs(price_diff_pct) < 2.0:
                position = 'moderate'
                quality = 'fair'
            else:
                position = 'far'
                quality = 'poor'

            # 分析价格方向是否符合信号
            direction_match = False
            if signal_type == 'buy' and current_price >= signal_price:
                direction_match = True
            elif signal_type == 'sell' and current_price <= signal_price:
                direction_match = True

            return {
                'price_diff_pct': price_diff_pct,
                'position': position,
                'direction_match': direction_match,
                'quality': quality
            }

        except Exception as e:
            logger.warning(f"价格位置分析失败: {str(e)}")
            return {'position': 'unknown', 'quality': 'poor'}

    def _analyze_signal_timing(self, bar_position, timeframe):
        """分析信号时效性"""
        try:
            # 根据时间级别调整时效性标准
            timeframe_multiplier = {
                '1m': 1.0,
                '5m': 1.2,
                '15m': 1.5,
                '30m': 2.0,
                '1h': 2.5
            }.get(timeframe, 1.0)

            adjusted_position = bar_position / timeframe_multiplier

            if adjusted_position == 0:
                timing = 'current'
                quality = 'excellent'
            elif adjusted_position <= 2:
                timing = 'very_fresh'
                quality = 'excellent'
            elif adjusted_position <= 5:
                timing = 'fresh'
                quality = 'good'
            elif adjusted_position <= 10:
                timing = 'moderate'
                quality = 'fair'
            elif adjusted_position <= 20:
                timing = 'old'
                quality = 'poor'
            else:
                timing = 'very_old'
                quality = 'very_poor'

            return {
                'bar_position': bar_position,
                'adjusted_position': adjusted_position,
                'timing': timing,
                'quality': quality,
                'timeframe_factor': timeframe_multiplier
            }

        except Exception as e:
            logger.warning(f"时效性分析失败: {str(e)}")
            return {'timing': 'unknown', 'quality': 'poor'}

    def _analyze_volume_confirmation(self, df, idx, signal_type):
        """分析成交量确认"""
        try:
            if 'volume' not in df.columns:
                return {'confirmation': 'no_data', 'quality': 'unknown'}

            # 获取信号点的成交量
            signal_volume = df['volume'].iloc[idx]

            # 计算平均成交量
            lookback = min(20, len(df))
            avg_volume = df['volume'].iloc[-lookback:].mean()

            volume_ratio = signal_volume / avg_volume if avg_volume > 0 else 1

            # 判断成交量确认
            if volume_ratio > 1.5:
                confirmation = 'strong'
                quality = 'excellent'
            elif volume_ratio > 1.2:
                confirmation = 'moderate'
                quality = 'good'
            elif volume_ratio > 0.8:
                confirmation = 'normal'
                quality = 'fair'
            else:
                confirmation = 'weak'
                quality = 'poor'

            return {
                'signal_volume': signal_volume,
                'avg_volume': avg_volume,
                'volume_ratio': volume_ratio,
                'confirmation': confirmation,
                'quality': quality
            }

        except Exception as e:
            logger.warning(f"成交量分析失败: {str(e)}")
            return {'confirmation': 'unknown', 'quality': 'poor'}

    def _calculate_credibility_score(self, technical_analysis, pivot_analysis, bi_analysis,
                                   price_analysis, timing_analysis, volume_analysis):
        """计算综合可信度评分"""
        try:
            score = 0.0
            max_score = 10.0

            # 技术形态权重 (20%)
            tech_score = {
                'excellent': 2.0, 'good': 1.5, 'fair': 1.0, 'poor': 0.5, 'unknown': 0.5
            }.get(technical_analysis.get('pattern_quality', 'unknown'), 0.5)

            # 中枢关系权重 (25%)
            pivot_score = {
                'excellent': 2.5, 'good': 2.0, 'fair': 1.5, 'poor': 0.5, 'unknown': 0.5
            }.get(pivot_analysis.get('quality', 'unknown'), 0.5)

            # 笔结构权重 (20%)
            bi_score = {
                'excellent': 2.0, 'good': 1.5, 'fair': 1.0, 'poor': 0.5, 'unknown': 0.5
            }.get(bi_analysis.get('quality', 'unknown'), 0.5)

            # 价格位置权重 (15%)
            price_score = {
                'excellent': 1.5, 'good': 1.2, 'fair': 0.8, 'poor': 0.3, 'unknown': 0.3
            }.get(price_analysis.get('quality', 'unknown'), 0.3)

            # 时效性权重 (15%)
            timing_score = {
                'excellent': 1.5, 'good': 1.2, 'fair': 0.8, 'poor': 0.3, 'very_poor': 0.1, 'unknown': 0.3
            }.get(timing_analysis.get('quality', 'unknown'), 0.3)

            # 成交量权重 (5%)
            volume_score = {
                'excellent': 0.5, 'good': 0.4, 'fair': 0.3, 'poor': 0.1, 'unknown': 0.2
            }.get(volume_analysis.get('quality', 'unknown'), 0.2)

            total_score = tech_score + pivot_score + bi_score + price_score + timing_score + volume_score

            # 确保分数在0-10范围内
            return min(max_score, max(0.0, total_score))

        except Exception as e:
            logger.warning(f"可信度评分计算失败: {str(e)}")
            return 5.0  # 默认中等可信度

    def _generate_detailed_reason(self, signal_type, technical_analysis, pivot_analysis,
                                bi_analysis, price_analysis, timing_analysis, volume_analysis, credibility_score):
        """生成详细的判断理由"""
        try:
            reasons = []

            # 1. 信号类型说明
            signal_desc = {
                '第一类买点': '趋势反转信号，下跌趋势结束',
                '第二类买点': '中枢完成后的回调买入机会',
                '第三类买点': '中枢内部或突破确认买点',
                '第一类卖点': '趋势反转信号，上涨趋势结束',
                '第二类卖点': '中枢完成后的反弹卖出机会',
                '第三类卖点': '中枢内部或跌破确认卖点',
                '中枢上沿买点': '中枢上沿支撑位买入',
                '潜在第二类买点': '可能的突破回调买点'
            }.get(signal_type, '未知信号类型')

            reasons.append(f"信号类型: {signal_desc}")

            # 2. 技术形态分析
            tech_quality = technical_analysis.get('pattern_quality', 'unknown')
            trend = technical_analysis.get('trend', 'unclear')
            trend_strength = technical_analysis.get('trend_strength', 0)

            if tech_quality == 'good':
                reasons.append(f"技术形态良好，当前{trend}趋势，强度{trend_strength:.1f}%")
            elif tech_quality == 'fair':
                reasons.append(f"技术形态一般，趋势{trend}，需谨慎")
            else:
                reasons.append(f"技术形态不明确，市场方向不清")

            # 3. 中枢关系分析
            pivot_quality = pivot_analysis.get('quality', 'unknown')
            relationship = pivot_analysis.get('relationship', 'unknown')
            distance = pivot_analysis.get('distance', 0)

            if pivot_quality == 'excellent':
                reasons.append(f"与中枢关系优秀，位于中枢{relationship}，距离{distance}根K线")
            elif pivot_quality == 'good':
                reasons.append(f"与中枢关系良好，{relationship}位置")
            elif pivot_quality == 'fair':
                reasons.append(f"与中枢关系一般，距离较远({distance}根K线)")
            else:
                reasons.append(f"缺乏明确的中枢支撑")

            # 4. 笔结构分析
            bi_structure = bi_analysis.get('structure', 'unknown')
            bi_quality = bi_analysis.get('quality', 'unknown')

            if bi_quality == 'good':
                reasons.append(f"笔结构清晰，呈现{bi_structure}形态")
            elif bi_quality == 'fair':
                reasons.append(f"笔结构基本符合，但确认度一般")
            else:
                reasons.append(f"笔结构不够清晰")

            # 5. 价格位置分析
            price_position = price_analysis.get('position', 'unknown')
            price_diff = price_analysis.get('price_diff_pct', 0)
            direction_match = price_analysis.get('direction_match', False)

            if price_position == 'very_close':
                reasons.append(f"当前价格与信号价格非常接近(偏差{price_diff:.1f}%)")
            elif price_position == 'close':
                reasons.append(f"当前价格接近信号价格(偏差{price_diff:.1f}%)")
            else:
                reasons.append(f"当前价格偏离信号价格较大(偏差{price_diff:.1f}%)")

            if not direction_match:
                reasons.append(f"⚠️ 价格走势与信号方向不符")

            # 6. 时效性分析
            timing = timing_analysis.get('timing', 'unknown')
            bar_position = timing_analysis.get('bar_position', 0)

            if timing == 'current':
                reasons.append(f"信号在当前K线形成，时效性极佳")
            elif timing == 'very_fresh':
                reasons.append(f"信号非常新鲜({bar_position}根K线前)")
            elif timing == 'fresh':
                reasons.append(f"信号较新鲜({bar_position}根K线前)")
            elif timing == 'moderate':
                reasons.append(f"信号时效性一般({bar_position}根K线前)")
            else:
                reasons.append(f"⚠️ 信号已过时({bar_position}根K线前)")

            # 7. 成交量确认
            volume_confirmation = volume_analysis.get('confirmation', 'unknown')
            volume_ratio = volume_analysis.get('volume_ratio', 1)

            if volume_confirmation == 'strong':
                reasons.append(f"成交量强力确认(是平均量的{volume_ratio:.1f}倍)")
            elif volume_confirmation == 'moderate':
                reasons.append(f"成交量适度确认(是平均量的{volume_ratio:.1f}倍)")
            elif volume_confirmation == 'normal':
                reasons.append(f"成交量正常")
            elif volume_confirmation == 'weak':
                reasons.append(f"⚠️ 成交量偏弱(仅为平均量的{volume_ratio:.1f}倍)")

            # 8. 综合评分
            confidence_level = self._get_confidence_level(credibility_score)
            reasons.append(f"综合可信度: {credibility_score:.1f}/10 ({confidence_level})")

            return " | ".join(reasons)

        except Exception as e:
            logger.warning(f"详细理由生成失败: {str(e)}")
            return f"信号分析: {signal_type}，可信度评分: {credibility_score:.1f}/10"

    def _generate_risk_warnings(self, signal, technical_analysis, pivot_analysis, timing_analysis):
        """生成风险提示"""
        try:
            warnings = []

            # 时效性风险
            timing_quality = timing_analysis.get('quality', 'unknown')
            if timing_quality in ['poor', 'very_poor']:
                warnings.append("信号已过时，入场风险较高")

            # 技术形态风险
            tech_quality = technical_analysis.get('pattern_quality', 'unknown')
            if tech_quality == 'poor':
                warnings.append("技术形态不佳，市场方向不明")

            # 中枢关系风险
            pivot_quality = pivot_analysis.get('quality', 'unknown')
            if pivot_quality == 'poor':
                warnings.append("缺乏中枢支撑，信号可靠性降低")

            # 价格偏离风险
            price_diff = abs(signal.get('price_diff_pct', 0)) if 'price_diff_pct' in signal else 0
            if price_diff > 3:
                warnings.append(f"当前价格偏离信号价格{price_diff:.1f}%，存在滑点风险")

            return warnings

        except Exception as e:
            logger.warning(f"风险提示生成失败: {str(e)}")
            return ["风险分析失败，请谨慎操作"]

    def _get_confidence_level(self, score):
        """根据评分获取置信度等级"""
        if score >= 8.5:
            return "极高"
        elif score >= 7.0:
            return "高"
        elif score >= 5.5:
            return "中等"
        elif score >= 3.5:
            return "较低"
        else:
            return "低"

    def _determine_point_type(self, idx, price, bi_list, pivot_zones, signal_type):
        """
        判断买卖点类型（第一、二、三类）
        
        返回:
            int: 1, 2, 或 3
        """
        try:
            # 简化的判断逻辑，实际应该更复杂
            
            # 检查是否在中枢附近
            for pivot in pivot_zones:
                # 中枢格式：(start_idx, end_idx, zg, zd, start_date, end_date)
                pivot_start, pivot_end, pivot_high, pivot_low = pivot[:4]
                
                # 如果买卖点在中枢时间范围内或附近
                if abs(idx - pivot_end) <= 10:
                    if signal_type == 'buy':
                        # 如果价格接近中枢下沿，可能是第二类买点
                        if abs(price - pivot_low) / pivot_low < 0.02:
                            return 2
                        # 如果价格在中枢内部，可能是第三类买点
                        elif pivot_low <= price <= pivot_high:
                            return 3
                    else:  # sell
                        # 如果价格接近中枢上沿，可能是第二类卖点
                        if abs(price - pivot_high) / pivot_high < 0.02:
                            return 2
                        # 如果价格在中枢内部，可能是第三类卖点
                        elif pivot_low <= price <= pivot_high:
                            return 3
            
            # 检查是否是趋势转折点（第一类买卖点）
            # 简化判断：如果前面有明显的趋势，当前是反向信号
            if len(bi_list) >= 3:
                recent_bis = bi_list[-3:]
                if signal_type == 'buy':
                    # 如果最近的笔主要是下降的，当前买点可能是第一类
                    down_count = sum(1 for bi in recent_bis if bi[4] == 'down')
                    if down_count >= 2:
                        return 1
                else:  # sell
                    # 如果最近的笔主要是上升的，当前卖点可能是第一类
                    up_count = sum(1 for bi in recent_bis if bi[4] == 'up')
                    if up_count >= 2:
                        return 1
            
            # 默认返回第二类
            return 2
            
        except Exception as e:
            logger.warning(f"判断买卖点类型失败: {str(e)}")
            return 2  # 默认第二类
    
    def _find_related_pivot(self, idx, pivot_zones):
        """
        找到与买卖点相关的中枢
        
        返回:
            int: 中枢索引，如果没有找到返回None
        """
        try:
            # 寻找最近的中枢
            best_pivot_idx = None
            min_distance = float('inf')
            
            for i, pivot in enumerate(pivot_zones):
                # 中枢格式：(start_idx, end_idx, zg, zd, start_date, end_date)
                pivot_start, pivot_end, pivot_high, pivot_low = pivot[:4]
                
                # 计算买卖点到中枢的距离
                if idx >= pivot_start:
                    distance = idx - pivot_end
                else:
                    distance = pivot_start - idx
                
                # 选择最近的中枢
                if distance < min_distance:
                    min_distance = distance
                    best_pivot_idx = i
            
            # 如果距离太远，返回None
            if min_distance > 50:  # 超过50个K线认为太远
                return None
                
            return best_pivot_idx
            
        except Exception as e:
            logger.warning(f"寻找相关中枢失败: {str(e)}")
            return None
    
    def _calculate_stop_levels_improved_traditional(self, df, bi_list, pivot_zones, idx, price, signal_type, timeframe):
        """
        改进的传统止盈止损计算方法
        """
        try:
            # 获取ATR值用于动态止损
            atr_periods = 14
            if 'atr' not in df.columns:
                high_low = df['high'] - df['low']
                high_close = abs(df['high'] - df['close'].shift())
                low_close = abs(df['low'] - df['close'].shift())
                tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
                atr = tr.rolling(atr_periods).mean()
                df['atr'] = atr
            
            current_atr = df['atr'].iloc[-1] if not pd.isna(df['atr'].iloc[-1]) else price * 0.02
            
            if signal_type == 'buy':
                # 改进的买入止损策略
                # 1. 寻找最近的重要支撑位
                support_level = self._find_dynamic_support(df, idx, lookback=30)
                if support_level and support_level < price:
                    stop_loss = support_level - current_atr * 0.5
                else:
                    stop_loss = price - current_atr * 2  # 2倍ATR止损
                
                # 确保止损不超过5%
                stop_loss = max(stop_loss, price * 0.95)
                
                # 改进的买入止盈策略
                # 第一止盈：1.5倍ATR或最近阻力位
                resistance_level = self._find_dynamic_resistance(df, idx, lookback=30)
                if resistance_level and resistance_level > price * 1.02:
                    take_profit1 = resistance_level
                else:
                    take_profit1 = price + current_atr * 1.5
                
                # 第二止盈：基于中枢或斐波那契
                if pivot_zones:
                    latest_pivot = pivot_zones[-1]
                    pivot_height = latest_pivot[2] - latest_pivot[3]
                    take_profit2 = take_profit1 + pivot_height * 0.618  # 黄金分割
                else:
                    take_profit2 = price + current_atr * 3
                
                # 第三止盈：趋势延续目标
                take_profit3 = price + current_atr * 5
                
            else:  # sell信号
                # 改进的卖出止损策略
                resistance_level = self._find_dynamic_resistance(df, idx, lookback=30)
                if resistance_level and resistance_level > price:
                    stop_loss = resistance_level + current_atr * 0.5
                else:
                    stop_loss = price + current_atr * 2
                
                # 确保止损不超过5%
                stop_loss = min(stop_loss, price * 1.05)
                
                # 改进的卖出止盈策略
                support_level = self._find_dynamic_support(df, idx, lookback=30)
                if support_level and support_level < price * 0.98:
                    take_profit1 = support_level
                else:
                    take_profit1 = price - current_atr * 1.5
                
                if pivot_zones:
                    latest_pivot = pivot_zones[-1]
                    pivot_height = latest_pivot[2] - latest_pivot[3]
                    take_profit2 = take_profit1 - pivot_height * 0.618
                else:
                    take_profit2 = price - current_atr * 3
                
                take_profit3 = price - current_atr * 5
            
            # 四舍五入到合理精度
            stop_loss = round(stop_loss, 4)
            take_profit1 = round(take_profit1, 4)
            take_profit2 = round(take_profit2, 4)
            take_profit3 = round(take_profit3, 4)
            
            return stop_loss, take_profit1, take_profit2, take_profit3
            
        except Exception as e:
            logger.error(f"改进传统止盈止损计算失败: {str(e)}")
            # 最后的备用方案
            if signal_type == 'buy':
                return (round(price * 0.97, 4), round(price * 1.05, 4), 
                       round(price * 1.1, 4), round(price * 1.15, 4))
            else:
                return (round(price * 1.03, 4), round(price * 0.95, 4), 
                       round(price * 0.9, 4), round(price * 0.85, 4))
    
    def _find_dynamic_support(self, df, current_idx, lookback=30):
        """动态寻找支撑位"""
        try:
            start_idx = max(0, current_idx - lookback)
            price_range = df['low'].iloc[start_idx:current_idx]
            
            if price_range.empty:
                return None
            
            # 寻找最近的重要低点（局部最小值）
            for i in range(len(price_range) - 3, 2, -1):
                if (price_range.iloc[i] < price_range.iloc[i-1] and 
                    price_range.iloc[i] < price_range.iloc[i-2] and
                    price_range.iloc[i] < price_range.iloc[i+1] and 
                    price_range.iloc[i] < price_range.iloc[i+2]):
                    return price_range.iloc[i]
            
            # 如果没有找到明显的支撑，返回最低点
            return price_range.min()
            
        except Exception:
            return None
    
    def _find_dynamic_resistance(self, df, current_idx, lookback=30):
        """动态寻找阻力位"""
        try:
            start_idx = max(0, current_idx - lookback)
            price_range = df['high'].iloc[start_idx:current_idx]
            
            if price_range.empty:
                return None
            
            # 寻找最近的重要高点（局部最大值）
            for i in range(len(price_range) - 3, 2, -1):
                if (price_range.iloc[i] > price_range.iloc[i-1] and 
                    price_range.iloc[i] > price_range.iloc[i-2] and
                    price_range.iloc[i] > price_range.iloc[i+1] and 
                    price_range.iloc[i] > price_range.iloc[i+2]):
                    return price_range.iloc[i]
            
            # 如果没有找到明显的阻力，返回最高点
            return price_range.max()
            
        except Exception:
            return None


# 测试代码
if __name__ == "__main__":
    # 设置日志
    logger.add("logs/multi_level_analysis_{time}.log", rotation="100 MB")
    
    try:
        # 创建多级别分析器
        analyzer = MultiLevelAnalysis(symbol="BTC/USDT")
        
        # 执行综合市场分析
        result = analyzer.comprehensive_market_analysis()
        
        if result:
            # 打印综合分析报告
            analyzer.print_comprehensive_report(result)
            
            # 也可以单独查看多级别联立分析结果
            multi_level_result = result['multi_level_analysis']
            print("\n=== 多级别联立分析详情 ===")
            print(f"最终判断: {'线段终结' if multi_level_result['final_judgment'] else '线段未终结'}")
            print(f"置信度: {multi_level_result['confidence']}%")
            print(f"判断理由: {multi_level_result['reason']}")
            print(f"趋势一致性: {'是' if multi_level_result['trend_consistency'] else '否'}")
            
            print("\n=== 各级别详细结果 ===")
            for tf, level_result in multi_level_result['level_results'].items():
                print(f"{tf} 级别:")
                print(f"  - 线段终结: {'是' if level_result['segment_ended'] else '否'}")
                print(f"  - 当前趋势: {level_result['current_trend']}")
                print(f"  - 背驰强度: {level_result['divergence']['strength']}")
                print(f"  - 最新价格: {level_result['last_price']}")
        else:
            print("❌ 综合市场分析失败")
        
    except Exception as e:
        print("测试失败: {}".format(str(e)))
        logger.error("多级别分析测试失败: {}".format(str(e))) 