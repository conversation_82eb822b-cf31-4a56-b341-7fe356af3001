#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
市场分析模块：提供买卖点判断和操作建议
"""
from datetime import datetime
import pandas as pd

class MarketAnalyzer:
    """
    市场分析器：分析当前市场状态并给出操作建议
    """
    
    def __init__(self):
        self.trend_strength_threshold = {
            'strong': 8,      # 强信号阈值
            'medium': 6,      # 中等信号阈值
            'weak': 4         # 弱信号阈值
        }
    
    def analyze_market_status(self, df, bi_list, pivot_zones, buy_points_all, sell_points_all, segment_analysis=None):
        """
        分析当前市场状态并给出操作建议
        
        参数:
            df: K线数据
            bi_list: 笔列表
            pivot_zones: 中枢列表
            buy_points_all: 所有买点 {'1': (buy_points1, sell_points1), '2': (buy_points2, sell_points2), '3': (buy_points3, sell_points3)}
            sell_points_all: 所有卖点
            segment_analysis: 线段分析结果 {'direction': 'up/down', 'confidence': 0-100, 'end_confirmed': True/False}
            
        返回:
            dict: 分析结果和操作建议
        """
        current_time = datetime.now()
        
        # 确保current_price是数值类型
        try:
            current_price = float(df['close'].iloc[-1])
        except (ValueError, TypeError):
            # 如果转换失败，使用默认值
            current_price = 0.0
        
        # 分析结果字典
        analysis = {
            'timestamp': current_time,
            'current_price': current_price,
            'trend_analysis': {},
            'buy_signals': [],
            'sell_signals': [],
            'operation_advice': {},
            'waiting_conditions': [],
            'risk_warning': [],
            'segment_signals': []  # 新增：基于线段的信号
        }
        
        # 1. 趋势分析
        analysis['trend_analysis'] = self._analyze_trend(df, bi_list, pivot_zones)
        
        # 2. 买卖点分析
        analysis['buy_signals'], analysis['sell_signals'] = self._analyze_signals(
            df, buy_points_all, sell_points_all, current_price
        )
        
        # 3. 线段终结信号分析（新增）
        if segment_analysis:
            analysis['segment_signals'] = self._analyze_segment_signals(df, segment_analysis, current_price)
            # 将线段信号合并到买卖信号中
            if analysis['segment_signals']:
                for signal in analysis['segment_signals']:
                    if signal['action'] == 'buy':
                        analysis['buy_signals'].append(signal)
                    elif signal['action'] == 'sell':
                        analysis['sell_signals'].append(signal)
        
        # 4. 生成操作建议
        analysis['operation_advice'] = self._generate_operation_advice(
            analysis['trend_analysis'], 
            analysis['buy_signals'], 
            analysis['sell_signals'],
            current_price
        )
        
        # 5. 等待条件分析
        analysis['waiting_conditions'] = self._analyze_waiting_conditions(
            df, bi_list, pivot_zones, analysis['buy_signals'], analysis['sell_signals']
        )
        
        # 6. 风险提示
        analysis['risk_warning'] = self._generate_risk_warning(
            analysis['trend_analysis'], 
            analysis['buy_signals'], 
            analysis['sell_signals']
        )
        
        return analysis
    
    def _analyze_trend(self, df, bi_list, pivot_zones):
        """
        分析当前趋势
        """
        trend_analysis = {
            'direction': 'sideways',  # up/down/sideways
            'strength': 0,            # 1-10
            'stage': 'unknown',       # trend_start/trend_middle/trend_end/consolidation
            'description': ''
        }
        
        if len(bi_list) < 3:
            trend_analysis['description'] = "数据不足，无法判断趋势"
            return trend_analysis
        
        # 分析最近3笔的走势
        recent_bi = bi_list[-3:]
        
        # 计算价格变化，确保价格是数值类型
        # bi_list结构: (start_idx, end_idx, start_val, end_val, direction)
        try:
            start_price = float(recent_bi[0][2])  # 第一笔起始价格
            end_price = float(recent_bi[-1][3])   # 最后一笔结束价格
            price_change_pct = (end_price - start_price) / start_price * 100
        except (ValueError, TypeError, IndexError, ZeroDivisionError):
            # 如果计算失败，尝试使用DataFrame的价格数据
            try:
                start_price = float(df['close'].iloc[0])
                end_price = float(df['close'].iloc[-1])
                price_change_pct = (end_price - start_price) / start_price * 100
            except (ValueError, TypeError, IndexError, ZeroDivisionError):
                # 如果仍然失败，返回默认趋势分析
                trend_analysis['description'] = "价格数据异常，无法计算趋势"
                return trend_analysis
        
        # 判断趋势方向
        if price_change_pct > 2:
            trend_analysis['direction'] = 'up'
            trend_analysis['strength'] = min(10, int(abs(price_change_pct)))
        elif price_change_pct < -2:
            trend_analysis['direction'] = 'down'
            trend_analysis['strength'] = min(10, int(abs(price_change_pct)))
        else:
            trend_analysis['direction'] = 'sideways'
            trend_analysis['strength'] = 3
        
        # 判断趋势阶段
        if pivot_zones and len(pivot_zones) > 0:
            try:
                latest_pivot = pivot_zones[-1]
                current_price = float(df['close'].iloc[-1])
                
                # 检查中枢数据结构
                if len(latest_pivot) >= 4:
                    pivot_high = float(latest_pivot[2])  # 中枢上沿
                    pivot_low = float(latest_pivot[3])   # 中枢下沿
                    
                    # 添加容差，避免边界判断问题
                    tolerance = (pivot_high - pivot_low) * 0.01  # 1%容差
                    
                    if current_price > pivot_high + tolerance:  # 在中枢上方
                        if trend_analysis['direction'] == 'up':
                            trend_analysis['stage'] = 'trend_middle'
                        else:
                            trend_analysis['stage'] = 'trend_end'
                    elif current_price < pivot_low - tolerance:  # 在中枢下方
                        if trend_analysis['direction'] == 'down':
                            trend_analysis['stage'] = 'trend_middle'
                        else:
                            trend_analysis['stage'] = 'trend_end'
                    else:  # 在中枢内部
                        trend_analysis['stage'] = 'consolidation'
                else:
                    # 中枢数据结构不完整，使用简化判断
                    trend_analysis['stage'] = 'trend_start'
                    
            except (ValueError, TypeError, IndexError) as e:
                # 如果转换失败，使用简化的阶段判断
                if trend_analysis['strength'] >= 6:
                    trend_analysis['stage'] = 'trend_middle'
                elif trend_analysis['strength'] >= 3:
                    trend_analysis['stage'] = 'trend_start'
                else:
                    trend_analysis['stage'] = 'consolidation'
        else:
            # 没有中枢数据，根据趋势强度判断阶段
            if trend_analysis['direction'] != 'sideways':
                if trend_analysis['strength'] >= 7:
                    trend_analysis['stage'] = 'trend_middle'
                elif trend_analysis['strength'] >= 4:
                    trend_analysis['stage'] = 'trend_start'
                else:
                    trend_analysis['stage'] = 'consolidation'
            else:
                trend_analysis['stage'] = 'consolidation'
        
        # 生成描述
        direction_desc = {'up': 'up_trend', 'down': 'down_trend', 'sideways': 'sideways'}
        stage_desc = {
            'trend_start': 'start',
            'trend_middle': 'middle', 
            'trend_end': 'end',
            'consolidation': 'consolidation'
        }
        
        direction_text = direction_desc[trend_analysis['direction']]
        strength_text = str(trend_analysis['strength'])
        stage_text = stage_desc.get(trend_analysis['stage'], 'unknown')
        
        trend_analysis['description'] = "Current trend: " + direction_text + ", strength: " + strength_text + "/10, stage: " + stage_text
        
        return trend_analysis
    
    def _analyze_signals(self, df, buy_points_all, sell_points_all, current_price):
        """
        分析当前买卖信号
        """
        buy_signals = []
        sell_signals = []
        
        current_time = df['timestamp'].iloc[-1]
        
        # 分析各类买点
        for point_type, (buy_points, sell_points) in buy_points_all.items():
            type_name = {'1': '第一类', '2': '第二类', '3': '第三类'}[point_type]
            
            # 分析买点
            for buy_point in buy_points:
                if point_type == '3':
                    idx, price, strength, pivot_idx, reason = buy_point
                else:
                    idx, price, strength = buy_point[:3]
                    pivot_idx = buy_point[3] if len(buy_point) > 3 else None
                    reason = ""
                
                signal_time = df['timestamp'].iloc[idx]
                
                # 确保price是数值类型
                try:
                    price = float(price)
                    distance_from_current = abs(current_price - price) / price * 100
                except (ValueError, TypeError, ZeroDivisionError):
                    # 如果转换失败，设置默认值
                    price = current_price
                    distance_from_current = 0.0
                
                # 判断信号是否为最近的信号（最近10个周期内）
                time_diff = abs((current_time - signal_time).total_seconds() / 60)  # 分钟差
                if time_diff <= 50:  # 最近50分钟内的信号
                    buy_signals.append({
                        'type': type_name,
                        'time': signal_time,
                        'price': price,
                        'strength': strength,
                        'pivot_idx': pivot_idx,
                        'reason': reason,
                        'distance_from_current': distance_from_current,
                        'time_ago_minutes': time_diff
                    })
            
            # 分析卖点
            for sell_point in sell_points:
                if point_type == '3':
                    idx, price, strength, pivot_idx, reason = sell_point
                else:
                    idx, price, strength = sell_point[:3]
                    pivot_idx = sell_point[3] if len(sell_point) > 3 else None
                    reason = ""
                
                signal_time = df['timestamp'].iloc[idx]
                
                # 确保price是数值类型
                try:
                    price = float(price)
                    distance_from_current = abs(current_price - price) / price * 100
                except (ValueError, TypeError, ZeroDivisionError):
                    # 如果转换失败，设置默认值
                    price = current_price
                    distance_from_current = 0.0
                
                # 判断信号是否为最近的信号
                time_diff = abs((current_time - signal_time).total_seconds() / 60)
                if time_diff <= 500:  # 增加到500分钟（约8小时）内的信号
                    sell_signals.append({
                        'type': type_name,
                        'time': signal_time,
                        'price': price,
                        'strength': strength,
                        'pivot_idx': pivot_idx,
                        'reason': reason,
                        'distance_from_current': distance_from_current,
                        'time_ago_minutes': time_diff
                    })
        
        # 按强度和时间排序
        buy_signals.sort(key=lambda x: (x['strength'], -x['time_ago_minutes']), reverse=True)
        sell_signals.sort(key=lambda x: (x['strength'], -x['time_ago_minutes']), reverse=True)
        
        return buy_signals, sell_signals
    
    def _generate_operation_advice(self, trend_analysis, buy_signals, sell_signals, current_price):
        """
        生成操作建议
        """
        advice = {
            'action': 'wait',  # buy/sell/wait
            'confidence': 0,   # 1-10
            'entry_price': None,
            'stop_loss': None,
            'take_profit_conservative': None,
            'take_profit_aggressive': None,
            'position_size': 'light',  # light/medium/heavy
            'holding_period': 'short',  # short/medium/long
            'description': ''
        }
        
        # 如果有强买入信号
        if buy_signals:
            strongest_buy = buy_signals[0]
            if strongest_buy['strength'] >= self.trend_strength_threshold['strong']:
                advice['action'] = 'buy'
                advice['confidence'] = strongest_buy['strength']
                advice['entry_price'] = current_price
                
                # 根据买点类型设置止盈止损
                if strongest_buy['type'] == '第一类':
                    advice['stop_loss'] = current_price * 0.95
                    advice['take_profit_conservative'] = current_price * 1.05
                    advice['take_profit_aggressive'] = current_price * 1.10
                    advice['position_size'] = 'heavy'
                    advice['holding_period'] = 'long'
                elif strongest_buy['type'] == '第二类':
                    advice['stop_loss'] = current_price * 0.98
                    advice['take_profit_conservative'] = current_price * 1.03
                    advice['take_profit_aggressive'] = current_price * 1.06
                    advice['position_size'] = 'medium'
                    advice['holding_period'] = 'medium'
                else:  # 第三类
                    advice['stop_loss'] = current_price * 0.99
                    advice['take_profit_conservative'] = current_price * 1.01
                    advice['take_profit_aggressive'] = current_price * 1.02
                    advice['position_size'] = 'light'
                    advice['holding_period'] = 'short'
                
                advice['description'] = "Found " + strongest_buy['type'] + " buy point, strength " + str(strongest_buy['strength']) + "/10, suggest buy"
        
        # 如果有强卖出信号
        elif sell_signals:
            strongest_sell = sell_signals[0]
            if strongest_sell['strength'] >= self.trend_strength_threshold['strong']:
                advice['action'] = 'sell'
                advice['confidence'] = strongest_sell['strength']
                advice['entry_price'] = current_price
                
                # 根据卖点类型设置止盈止损
                if strongest_sell['type'] == '第一类':
                    advice['stop_loss'] = current_price * 1.05
                    advice['take_profit_conservative'] = current_price * 0.95
                    advice['take_profit_aggressive'] = current_price * 0.90
                    advice['position_size'] = 'heavy'
                    advice['holding_period'] = 'long'
                elif strongest_sell['type'] == '第二类':
                    advice['stop_loss'] = current_price * 1.02
                    advice['take_profit_conservative'] = current_price * 0.97
                    advice['take_profit_aggressive'] = current_price * 0.94
                    advice['position_size'] = 'medium'
                    advice['holding_period'] = 'medium'
                else:  # 第三类
                    advice['stop_loss'] = current_price * 1.01
                    advice['take_profit_conservative'] = current_price * 0.99
                    advice['take_profit_aggressive'] = current_price * 0.98
                    advice['position_size'] = 'light'
                    advice['holding_period'] = 'short'
                
                advice['description'] = "Found " + strongest_sell['type'] + " sell point, strength " + str(strongest_sell['strength']) + "/10, suggest sell"
        
        # 如果没有强信号，建议等待
        else:
            advice['description'] = "No clear buy/sell signal, suggest wait"
            
            # 根据趋势给出倾向性建议
            if trend_analysis['direction'] == 'up' and trend_analysis['strength'] >= 6:
                advice['description'] += ", but uptrend is strong, watch for pullback buy opportunity"
            elif trend_analysis['direction'] == 'down' and trend_analysis['strength'] >= 6:
                advice['description'] += ", but downtrend is strong, watch for bounce sell opportunity"
        
        return advice
    
    def _analyze_waiting_conditions(self, df, bi_list, pivot_zones, buy_signals, sell_signals):
        """
        分析等待条件
        """
        conditions = []
        
        # 确保current_price是数值类型
        try:
            current_price = float(df['close'].iloc[-1])
        except (ValueError, TypeError):
            # 如果转换失败，返回空条件列表
            return conditions
        
        # 如果没有明确信号，分析等待什么
        if not buy_signals and not sell_signals:
            conditions.append("等待新的分型形成")
            conditions.append("等待笔的完成")
            
            if pivot_zones:
                latest_pivot = pivot_zones[-1]
                
                # 确保zg和zd是数值类型
                try:
                    zg = float(latest_pivot[2])
                    zd = float(latest_pivot[3])
                except (ValueError, TypeError, IndexError):
                    # 如果转换失败，跳过中枢相关分析
                    return conditions
                
                # 设置价格容差，避免浮点数精确比较问题
                price_tolerance = (zg - zd) * 0.02  # 中枢高度的2%作为容差
                
                # 检查最近几根K线的最低点和最高点
                recent_bars = min(3, len(df))  # 最近3根K线
                
                # 确保recent_low和recent_high是数值类型
                try:
                    recent_low = float(df['low'].iloc[-recent_bars:].min())
                    recent_high = float(df['high'].iloc[-recent_bars:].max())
                except (ValueError, TypeError):
                    # 如果转换失败，使用当前价格作为默认值
                    recent_low = current_price
                    recent_high = current_price
                
                if zd < current_price < zg:
                    conditions.append("当前在中枢区间内 (" + str(round(zd, 2)) + "-" + str(round(zg, 2)) + ")，等待突破")
                    conditions.append("如突破 " + str(round(zg, 2)) + " 关注买入机会")
                    conditions.append("如跌破 " + str(round(zd, 2)) + " 关注卖出机会")
                elif current_price > zg:
                    # 检查是否已经回调到位
                    if recent_low <= zg + price_tolerance:
                        conditions.append("价格已回调至中枢上沿 " + str(round(zg, 2)) + " 附近（最近最低点: " + str(round(recent_low, 2)) + "）")
                        conditions.append("回调到位，关注买入信号形成")
                        conditions.append("等待分型确认或成交量配合")
                    elif current_price - zg <= price_tolerance:
                        conditions.append("价格已回调至中枢上沿附近 (" + str(round(zg, 2)) + ")，关注买入信号形成")
                        conditions.append("等待分型确认或成交量配合")
                    else:
                        conditions.append("当前在中枢上方，等待回调至 " + str(round(zg, 2)) + " 寻找买入机会")
                        conditions.append("当前价格: " + str(round(current_price, 2)) + "，距离目标: " + str(round(current_price - zg, 2)))
                elif current_price < zd:
                    # 检查是否已经反弹到位
                    if recent_high >= zd - price_tolerance:
                        conditions.append("价格已反弹至中枢下沿 " + str(round(zd, 2)) + " 附近（最近最高点: " + str(round(recent_high, 2)) + "）")
                        conditions.append("反弹到位，关注卖出信号形成")
                        conditions.append("等待分型确认或成交量配合")
                    elif zd - current_price <= price_tolerance:
                        conditions.append("价格已反弹至中枢下沿附近 (" + str(round(zd, 2)) + ")，关注卖出信号形成")
                        conditions.append("等待分型确认或成交量配合")
                    else:
                        conditions.append("当前在中枢下方，等待反弹至 " + str(round(zd, 2)) + " 寻找卖出机会")
                        conditions.append("当前价格: " + str(round(current_price, 2)) + "，距离目标: " + str(round(zd - current_price, 2)))
        
        # 如果有弱信号，分析如何等待确认
        elif buy_signals and buy_signals[0]['strength'] < self.trend_strength_threshold['strong']:
            conditions.append("买入信号较弱，等待更强确认")
            conditions.append("等待MACD金叉确认")
            conditions.append("等待成交量放大确认")
        
        elif sell_signals and sell_signals[0]['strength'] < self.trend_strength_threshold['strong']:
            conditions.append("卖出信号较弱，等待更强确认")
            conditions.append("等待MACD死叉确认")
            conditions.append("等待成交量放大确认")
        
        return conditions
    
    def _generate_risk_warning(self, trend_analysis, buy_signals, sell_signals):
        """
        生成风险提示
        """
        warnings = []
        
        # 趋势风险
        if trend_analysis['stage'] == 'trend_end':
            warnings.append("⚠️ 可能处于趋势末端，注意反转风险")
        
        # 信号冲突风险
        if buy_signals and sell_signals:
            warnings.append("⚠️ 同时存在买卖信号，市场可能处于震荡状态")
        
        # 强度风险
        if buy_signals and buy_signals[0]['strength'] < 6:
            warnings.append("⚠️ 买入信号强度较弱，建议轻仓操作")
        
        if sell_signals and sell_signals[0]['strength'] < 6:
            warnings.append("⚠️ 卖出信号强度较弱，建议轻仓操作")
        
        # 通用风险提示
        warnings.append("💡 严格执行止损，控制风险")
        warnings.append("💡 分批建仓，避免一次性重仓")
        
        return warnings
    
    def _analyze_segment_signals(self, df, segment_analysis, current_price):
        """
        分析基于线段终结的买卖信号
        """
        signals = []
        
        if not segment_analysis or not segment_analysis.get('end_confirmed'):
            return signals
        
        confidence = segment_analysis.get('confidence', 0)
        direction = segment_analysis.get('direction', '')
        
        # 只有当置信度较高时才生成信号
        if confidence >= 80:  # 80%以上置信度
            current_time = df['timestamp'].iloc[-1]
            
            if direction == 'down':  # 下跌线段终结，可能反转向上
                strength = min(10, int(confidence / 10))  # 将置信度转换为1-10的强度
                signals.append({
                    'type': '线段终结买点',
                    'action': 'buy',
                    'time': current_time,
                    'price': current_price,
                    'strength': strength,
                    'pivot_idx': None,
                    'reason': "下跌线段终结，置信度{}%，预期反转向上".format(confidence),
                    'distance_from_current': 0,
                    'time_ago_minutes': 0
                })
                
            elif direction == 'up':  # 上涨线段终结，可能反转向下
                strength = min(10, int(confidence / 10))
                signals.append({
                    'type': '线段终结卖点',
                    'action': 'sell',
                    'time': current_time,
                    'price': current_price,
                    'strength': strength,
                    'pivot_idx': None,
                    'reason': "上涨线段终结，置信度{}%，预期反转向下".format(confidence),
                    'distance_from_current': 0,
                    'time_ago_minutes': 0
                })
        
        elif confidence >= 60:  # 60-80%置信度，生成弱信号
            current_time = df['timestamp'].iloc[-1]
            
            if direction == 'down':
                signals.append({
                    'type': '线段终结买点(弱)',
                    'action': 'buy',
                    'time': current_time,
                    'price': current_price,
                    'strength': 5,  # 弱信号
                    'pivot_idx': None,
                    'reason': "下跌线段可能终结，置信度{}%，谨慎关注反转".format(confidence),
                    'distance_from_current': 0,
                    'time_ago_minutes': 0
                })
                
            elif direction == 'up':
                signals.append({
                    'type': '线段终结卖点(弱)',
                    'action': 'sell',
                    'time': current_time,
                    'price': current_price,
                    'strength': 5,
                    'pivot_idx': None,
                    'reason': "上涨线段可能终结，置信度{}%，谨慎关注反转".format(confidence),
                    'distance_from_current': 0,
                    'time_ago_minutes': 0
                })
        
        return signals
    
    def print_analysis_report(self, analysis):
        """
        打印分析报告
        """
        print("\n" + "="*80)
        print("📊 Market Analysis Report")
        print("="*80)
        
        print("🕐 Analysis Time: " + analysis['timestamp'].strftime('%Y-%m-%d %H:%M:%S'))
        print("💰 Current Price: " + str(round(analysis['current_price'], 2)))
        
        # 趋势分析
        print("\n📈 Trend Analysis:")
        trend = analysis['trend_analysis']
        print("   " + trend['description'])
        
        # 买卖信号
        print("\n🎯 Current Signals:")
        
        # 显示线段终结信号
        if analysis.get('segment_signals'):
            print("   🔄 Segment End Signals:")
            for i, signal in enumerate(analysis['segment_signals']):
                action_icon = "📈" if signal['action'] == 'buy' else "📉"
                print("      " + str(i+1) + ". " + action_icon + " " + signal['type'] + " - strength:" + str(signal['strength']) + "/10")
                print("         Reason: " + signal['reason'])
        
        if analysis['buy_signals']:
            print("   📈 Buy Signals:")
            for i, signal in enumerate(analysis['buy_signals'][:3]):
                if signal['type'].startswith('线段终结'):
                    continue  # 线段信号已经在上面显示了
                print("      " + str(i+1) + ". " + signal['type'] + " buy point - strength:" + str(signal['strength']) + "/10 - " +
                      "price:" + str(round(signal['price'], 2)) + " - " + str(round(signal['time_ago_minutes'])) + " minutes ago")
                if signal['reason']:
                    print("         Reason: " + signal['reason'])
        
        if analysis['sell_signals']:
            print("   📉 Sell Signals:")
            for i, signal in enumerate(analysis['sell_signals'][:3]):
                if signal['type'].startswith('线段终结'):
                    continue  # 线段信号已经在上面显示了
                print("      " + str(i+1) + ". " + signal['type'] + " sell point - strength:" + str(signal['strength']) + "/10 - " +
                      "price:" + str(round(signal['price'], 2)) + " - " + str(round(signal['time_ago_minutes'])) + " minutes ago")
                if signal['reason']:
                    print("         Reason: " + signal['reason'])
        
        if not analysis['buy_signals'] and not analysis['sell_signals'] and not analysis.get('segment_signals'):
            print("   ⏳ No clear buy/sell signals currently")
        
        # 操作建议
        print("\n💡 Operation Advice:")
        advice = analysis['operation_advice']
        action_desc = {'buy': 'Buy', 'sell': 'Sell', 'wait': 'Wait'}
        size_desc = {'light': 'Light', 'medium': 'Medium', 'heavy': 'Heavy'}
        period_desc = {'short': 'Short', 'medium': 'Medium', 'long': 'Long'}
        
        print("   Action: " + action_desc[advice['action']])
        print("   Confidence: " + str(advice['confidence']) + "/10")
        print("   " + advice['description'])
        
        if advice['action'] != 'wait':
            print("   Suggested Position: " + size_desc[advice['position_size']])
            print("   Holding Period: " + period_desc[advice['holding_period']])
            print("   Entry Price: " + str(round(advice['entry_price'], 2)))
            print("   Stop Loss: " + str(round(advice['stop_loss'], 2)))
            print("   Conservative Take Profit: " + str(round(advice['take_profit_conservative'], 2)))
            print("   Aggressive Take Profit: " + str(round(advice['take_profit_aggressive'], 2)))
        
        # 等待条件
        if analysis['waiting_conditions']:
            print("\n⏰ Waiting Conditions:")
            for condition in analysis['waiting_conditions']:
                print("   • " + condition)
        
        # 风险提示
        print("\n⚠️ Risk Warning:")
        for warning in analysis['risk_warning']:
            print("   " + warning)
        
        print("\n" + "="*80)


# 测试代码
if __name__ == "__main__":
    print("市场分析模块已创建，请在主程序中调用使用") 