#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
通知模块：
1. 日志输出
2. 文件记录
3. 钉钉机器人通知
4. 可扩展为邮件、微信等通知方式
"""

import os
import json
import datetime
import requests
import hmac
import hashlib
import base64
import urllib.parse
import time
from loguru import logger


class Notifier:
    def __init__(self, notification_methods=None):
        """
        初始化通知器
        
        Args:
            notification_methods: 通知方式列表，默认为日志通知和文件通知
        """
        if notification_methods is None:
            notification_methods = ['log', 'file', 'dingtalk']
        
        self.notification_methods = notification_methods
        
        # 创建通知记录目录
        self.notification_dir = 'notifications'
        os.makedirs(self.notification_dir, exist_ok=True)
        
        # 钉钉机器人配置
        self.dingtalk_token = os.environ.get('DINGTALK_TOKEN', '')
        self.dingtalk_secret = os.environ.get('DINGTALK_SECRET', '')
        
        # 尝试从配置文件读取钉钉配置
        self._load_dingtalk_config_from_file()
        
        # 添加运行会话ID，用于将同一次运行的通知写入同一个文件
        self.session_id = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        self.session_notifications = {}  # 存储每个交易对在本次会话中的通知
        
        logger.info("初始化通知器，通知方式: {}".format(notification_methods))
    
    def _load_dingtalk_config_from_file(self):
        """从配置文件加载钉钉配置"""
        config_file = 'dingtalk_config.json'
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    # 如果环境变量未设置，则使用配置文件中的值
                    if not self.dingtalk_token:
                        self.dingtalk_token = config.get('token', '')
                    if not self.dingtalk_secret:
                        self.dingtalk_secret = config.get('secret', '')
                    
                    if self.dingtalk_token:
                        logger.info("已从配置文件加载钉钉机器人配置")
            except Exception as e:
                logger.error(f"从配置文件加载钉钉配置失败: {str(e)}")
    
    def send(self, message):
        """
        发送通知
        
        Args:
            message: 通知消息
        """
        for method in self.notification_methods:
            if method == 'log':
                self._send_log(message)
            elif method == 'file':
                self._send_file(message)
            elif method == 'email':
                self._send_email(message)
            elif method == 'wechat':
                self._send_wechat(message)
            elif method == 'dingtalk':
                self._send_dingtalk(message)
            else:
                logger.warning("不支持的通知方式: {}".format(method))
    
    def _send_log(self, message):
        """记录到日志"""
        logger.info(f"通知: {message}")
    
    def _send_file(self, message):
        """写入到文件"""
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 尝试从消息中提取时间周期和交易对信息
        timeframe = "unknown"
        symbol = "unknown"
        try:
            first_line = message.split('\n')[0]
            if "🚨" in first_line and "买卖点信号提醒" in first_line:
                parts = first_line.strip("🚨 ").split()
                if len(parts) >= 2:
                    symbol = parts[0].replace("/", "_")
                    timeframe = parts[1]
        except:
            pass
        
        # 使用会话ID来命名文件，确保同一次运行的通知写入同一个文件
        filename = os.path.join(self.notification_dir, f"notifications_{self.session_id}.txt")
        
        # 将通知添加到会话存储中
        key = f"{symbol}_{timeframe}"
        self.session_notifications[key] = message
        
        try:
            # 将所有时间周期的通知组合写入文件
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"===== 买卖点信号通知汇总（{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}） =====\n\n")
                for k, content in sorted(self.session_notifications.items()):
                    f.write(f"{'='*80}\n")
                    f.write(content)
                    f.write(f"\n{'='*80}\n\n")
            
            logger.info("通知已写入文件: {}".format(filename))
        except Exception as e:
            logger.error("写入通知到文件失败: {}".format(str(e)))
    
    def _send_email(self, message):
        """发送邮件通知"""
        # 这里可以使用smtplib库实现邮件发送
        # 为简化代码，这里只记录日志
        logger.info(f"【模拟邮件通知】: {message}")
    
    def _send_wechat(self, message):
        """发送微信通知"""
        # 这里可以使用企业微信API或Server酱等服务
        # 为简化代码，这里只记录日志
        logger.info(f"【模拟微信通知】: {message}")
    
    def _send_dingtalk(self, message):
        """发送钉钉通知"""
        if not self.dingtalk_token:
            # 尝试再次从文件加载配置
            self._load_dingtalk_config_from_file()
            
            # 如果仍然没有token，则退出
            if not self.dingtalk_token:
                logger.warning("钉钉通知Token未设置，无法发送钉钉通知")
                return
        
        try:
            # 钉钉机器人webhook地址
            webhook_url = f"https://oapi.dingtalk.com/robot/send?access_token={self.dingtalk_token}"
            
            # 添加签名
            if self.dingtalk_secret:
                timestamp = str(round(time.time() * 1000))
                string_to_sign = f"{timestamp}\n{self.dingtalk_secret}"
                hmac_code = hmac.new(
                    self.dingtalk_secret.encode(), 
                    string_to_sign.encode(), 
                    digestmod=hashlib.sha256
                ).digest()
                sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
                webhook_url = f"{webhook_url}&timestamp={timestamp}&sign={sign}"
            
            # 构造请求数据
            headers = {'Content-Type': 'application/json; charset=utf-8'}
            data = {
                "msgtype": "text",
                "text": {
                    "content": f"交易信号通知\n{message}"
                },
                "at": {
                    "isAtAll": False
                }
            }
            
            # 发送请求
            response = requests.post(webhook_url, headers=headers, data=json.dumps(data))
            
            # 检查响应
            if response.status_code == 200:
                resp_data = response.json()
                if resp_data.get('errcode') == 0:
                    logger.info("钉钉通知发送成功")
                else:
                    logger.error(f"钉钉通知发送失败: {resp_data.get('errmsg')}")
            else:
                logger.error(f"钉钉通知请求失败，状态码: {response.status_code}")
                
        except Exception as e:
            logger.error(f"发送钉钉通知出错: {str(e)}")

    def add_notification_method(self, method):
        """
        添加通知方式
        
        Args:
            method: 通知方式，如'dingtalk'
        """
        if method not in self.notification_methods:
            self.notification_methods.append(method)
            logger.info(f"已添加通知方式: {method}")

    def set_dingtalk_config(self, token, secret=''):
        """
        设置钉钉机器人配置
        
        Args:
            token: 钉钉机器人access_token
            secret: 钉钉机器人加签密钥，可选
        """
        self.dingtalk_token = token
        self.dingtalk_secret = secret
        logger.info("已设置钉钉机器人配置")
        
        # 自动添加钉钉通知方式
        self.add_notification_method('dingtalk')

    def send_log(self, message):
        """
        只发送日志通知
        
        Args:
            message: 通知消息
        """
        self._send_log(message)
    
    def send_file(self, message):
        """
        只发送文件通知
        
        Args:
            message: 通知消息
        """
        self._send_file(message)
    
    def send_dingtalk(self, message):
        """
        只发送钉钉通知
        
        Args:
            message: 通知消息
        """
        self._send_dingtalk(message)


# 测试代码
if __name__ == "__main__":
    # 创建通知器，使用日志和文件通知
    notifier = Notifier(['log', 'file'])
    
    # 如果环境变量中设置了钉钉token，则添加钉钉通知
    dingtalk_token = os.environ.get('DINGTALK_TOKEN')
    if dingtalk_token:
        notifier.set_dingtalk_config(
            token=dingtalk_token,
            secret=os.environ.get('DINGTALK_SECRET', '')
        )
    
    # 发送测试通知
    notifier.send("这是一条测试通知，BTC/USDT 5分钟线段终结") 