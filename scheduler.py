#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
定时调度模块：
1. 每5分钟自动运行一次线段终结判断
2. 支持多个交易对的监控
"""

import time
import datetime
import schedule
import pandas as pd
from loguru import logger
import threading

from data_fetcher import DataFetcher
from chan_analysis import ChanAnalysis
from notifier import Notifier


class SegmentMonitor:
    def __init__(self, symbols=None, exchange_id='gate'):
        """
        初始化线段监控器
        
        Args:
            symbols: 监控的交易对列表，默认为BTC/USDT和ETH/USDT
            exchange_id: 交易所ID，默认为gate
        """
        if symbols is None:
            symbols = ['BTC/USDT', 'ETH/USDT']
            
        self.symbols = symbols
        self.data_fetcher = DataFetcher(symbols=symbols, exchange_id=exchange_id)
        self.analyzers = {symbol: ChanAnalysis() for symbol in symbols}
        self.notifier = Notifier()
        
        # 记录上一次的终结状态，避免重复通知
        self.last_ended_status = {symbol: False for symbol in symbols}
        
        logger.info(f"初始化线段监控器，监控交易对: {symbols}")
    
    def check_segment_end(self, symbol):
        """
        检查指定交易对的线段是否终结
        
        Args:
            symbol: 交易对，如BTC/USDT
            
        Returns:
            bool: 是否终结
            dict: 终结原因
        """
        # 获取K线数据
        klines = self.data_fetcher.get_klines(symbol=symbol)
        
        if klines.empty:
            logger.error(f"获取 {symbol} 的K线数据失败")
            return False, {"reason": "获取K线数据失败"}
        
        # 分析K线
        analyzer = self.analyzers[symbol]
        is_ended, reason = analyzer.analyze(klines)
        
        return is_ended, reason
    
    def run_check(self):
        """运行一次检查，遍历所有交易对"""
        logger.info(f"开始检查线段终结情况，时间: {datetime.datetime.now()}")
        
        for symbol in self.symbols:
            is_ended, reason = self.check_segment_end(symbol)
            
            # 保存状态变化信息，用于通知
            current_status = is_ended
            previous_status = self.last_ended_status[symbol]
            self.last_ended_status[symbol] = current_status
            
            # 线段终结状态变化时才发送通知（从未终结变为终结）
            if current_status and not previous_status:
                # 发送通知
                message = f"{symbol} 5分钟线段终结，时间: {datetime.datetime.now()}\n"
                message += f"原因: {reason}"
                
                self.notifier.send(message)
                
                # 生成分析图表
                try:
                    # 获取K线数据
                    klines = self.data_fetcher.get_klines(symbol=symbol, limit=200)
                    
                    # 为数据添加交易对和时间周期信息
                    klines.attrs['symbol'] = symbol
                    klines.attrs['timeframe'] = '5m'
                    
                    # 生成图表
                    analyzer = self.analyzers[symbol]
                    chart_path = analyzer.plot_analysis()
                    
                    if chart_path:
                        logger.info(f"已为 {symbol} 生成线段终结分析图表: {chart_path}")
                        
                        # 添加图表路径到通知消息
                        additional_message = f"\n已生成分析图表: {chart_path}"
                        self.notifier.send(additional_message)
                except Exception as e:
                    logger.error(f"为 {symbol} 生成分析图表时出错: {str(e)}")
                
                logger.warning(message)
            else:
                logger.info(f"{symbol} 线段终结状态: {'已终结' if is_ended else '未终结'}")
    
    def start_schedule(self):
        """启动定时调度"""
        # 先运行一次检查
        self.run_check()
        
        # 设置每5分钟运行一次
        schedule.every(5).minutes.do(self.run_check)
        
        logger.info("启动定时调度，每5分钟检查一次线段终结情况")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("用户中断，停止监控")
        except Exception as e:
            logger.error(f"监控过程中发生错误: {str(e)}")


class Scheduler:
    """
    简单的调度器，用于定时执行任务
    """
    def __init__(self, interval=5):
        """
        初始化调度器
        
        参数:
            interval: 任务执行间隔，单位为分钟
        """
        self.interval = interval
        self.jobs = []
        self.stop_event = threading.Event()
        self.thread = None
        logger.info(f"初始化调度器，间隔: {interval}分钟")
    
    def add_job(self, job_func):
        """
        添加任务
        
        参数:
            job_func: 要执行的函数，不带参数
        """
        self.jobs.append(job_func)
        logger.info(f"添加任务: {job_func.__name__}")
    
    def _run(self):
        """
        内部运行方法，在单独的线程中执行
        """
        logger.info("调度器线程启动")
        
        while not self.stop_event.is_set():
            # 执行所有任务
            for job in self.jobs:
                try:
                    logger.debug(f"执行任务: {job.__name__}")
                    job()
                except Exception as e:
                    logger.error(f"执行任务 {job.__name__} 时出错: {str(e)}")
            
            # 等待下一次执行
            logger.debug(f"等待 {self.interval} 分钟后执行下一次任务")
            # 使用循环等待，以便能够及时响应停止信号
            wait_seconds = self.interval * 60
            for _ in range(wait_seconds):
                if self.stop_event.is_set():
                    break
                time.sleep(1)
    
    def start(self):
        """
        启动调度器
        """
        if self.thread and self.thread.is_alive():
            logger.warning("调度器已经在运行")
            return
        
        # 重置停止事件
        self.stop_event.clear()
        
        # 创建并启动线程
        self.thread = threading.Thread(target=self._run, daemon=True)
        self.thread.start()
        
        logger.info("调度器已启动")
    
    def stop(self):
        """
        停止调度器
        """
        logger.info("正在停止调度器")
        self.stop_event.set()
        
        if self.thread and self.thread.is_alive():
            # 等待线程结束
            self.thread.join(timeout=10)
            if self.thread.is_alive():
                logger.warning("调度器线程未能在10秒内结束")
            else:
                logger.info("调度器线程已结束")
        
        logger.info("调度器已停止")


# 测试代码
if __name__ == "__main__":
    # 设置日志级别和格式
    logger.add("segment_monitor.log", rotation="500 MB")
    
    # 创建监控器 - 监控多个交易对
    symbols = ['BTC/USDT', 'ETH/USDT', 'XRP/USDT']  # 可以根据需要添加更多交易对
    monitor = SegmentMonitor(symbols=symbols)
    
    # 启动定时调度
    monitor.start_schedule() 