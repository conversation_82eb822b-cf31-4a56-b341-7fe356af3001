from loguru import logger
import os
import pandas as pd
from datetime import datetime
from data_fetcher import DataFetcher
from chan_analysis import ChanAnalysis
from chart_plotter import ChartPlotter


class Analyzer:
    def __init__(self, exchange='gate', symbols=None, output_dir='outputs', chart_dir='charts'):
        """
        初始化分析器
        
        参数:
            exchange: 交易所名称，如 'gate', 'binance' 等
            symbols: 交易对列表，如 ['BTC/USDT', 'ETH/USDT']
            output_dir: 输出目录
            chart_dir: 图表保存目录
        """
        if symbols is None:
            symbols = ['BTC/USDT']
            
        self.exchange = exchange
        self.symbols = symbols
        self.output_dir = output_dir
        self.chart_dir = chart_dir
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(chart_dir, exist_ok=True)
        
        # 初始化数据获取器
        self.fetcher = DataFetcher(exchange, symbols)
        
        # 初始化缠论分析器
        self.chan = ChanAnalysis()
        
        # 初始化图表绘制器
        self.plotter = ChartPlotter(chart_dir)
        
        logger.info(f"分析器初始化完成，交易所: {exchange}, 交易对: {symbols}")
        
    def analyze_symbol(self, symbol, timeframes, limit=100):
        """
        分析指定交易对在多个时间周期下的状态
        
        参数:
            symbol: 交易对，如 'BTC/USDT'
            timeframes: 时间周期列表，如 ['5m', '15m', '1h']
            limit: 获取K线数量
            
        返回:
            dict: 分析结果字典
        """
        results = {}
        data_dict = {}
        fx_lists = {}
        bi_lists = {}
        xd_lists = {}
        
        for tf in timeframes:
            # 获取K线数据
            df = self.fetcher.get_klines(symbol, tf, limit)
            if df is None or df.empty:
                logger.error(f"无法获取 {symbol} {tf} 的K线数据")
                continue
                
            # 预处理K线数据
            df = self.chan.preprocess_klines(df)
            data_dict[tf] = df.copy()
            
            # 进行缠论分析
            fx_list, bi_list, xd_list, trend, current_direction, msg = self.chan.analyze(df)
            fx_lists[tf] = fx_list
            bi_lists[tf] = bi_list
            xd_lists[tf] = xd_list
            
            # 识别中枢区域
            pivot_zones = self.plotter.identify_pivot_zones(bi_list, df)
            
            # 识别第一类买卖点
            buy_points, sell_points = self.plotter.identify_first_buy_sell_points(bi_list, df, pivot_zones)
            
            # 识别第二类买卖点
            second_buy_points, second_sell_points = self.plotter.identify_second_buy_sell_points(bi_list, df, pivot_zones)
            
            # 汇总分析结果
            tf_result = {
                'trend': trend,
                'current_direction': current_direction,
                'message': msg,
                'fractal_count': len(fx_list),
                'stroke_count': len(bi_list),
                'segment_count': len(xd_list),
                'pivot_count': len(pivot_zones),
                'first_buy_points': [{'idx': idx, 'price': price, 'strength': strength} 
                                   for idx, price, strength in buy_points],
                'first_sell_points': [{'idx': idx, 'price': price, 'strength': strength} 
                                    for idx, price, strength in sell_points],
                'second_buy_points': [{'idx': idx, 'price': price, 'strength': strength, 'pivot': pivot_idx+1} 
                                    for idx, price, strength, pivot_idx in second_buy_points],
                'second_sell_points': [{'idx': idx, 'price': price, 'strength': strength, 'pivot': pivot_idx+1} 
                                     for idx, price, strength, pivot_idx in second_sell_points]
            }
            
            # 添加最新价格和时间信息
            if not df.empty:
                latest = df.iloc[-1]
                tf_result['latest_price'] = latest['close']
                tf_result['latest_time'] = latest['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
                
                # 添加MACD状态
                if 'macd' in df.columns and 'macd_signal' in df.columns:
                    last_idx = len(df) - 1
                    if last_idx > 0:
                        macd_cross = 'none'
                        if df['macd'].iloc[last_idx] > df['macd_signal'].iloc[last_idx] and \
                           df['macd'].iloc[last_idx-1] <= df['macd_signal'].iloc[last_idx-1]:
                            macd_cross = 'golden'  # 金叉
                        elif df['macd'].iloc[last_idx] < df['macd_signal'].iloc[last_idx] and \
                             df['macd'].iloc[last_idx-1] >= df['macd_signal'].iloc[last_idx-1]:
                            macd_cross = 'dead'  # 死叉
                        
                        tf_result['macd_cross'] = macd_cross
                        tf_result['macd_value'] = df['macd'].iloc[last_idx]
                        tf_result['macd_signal'] = df['macd_signal'].iloc[last_idx]
                        tf_result['macd_diff'] = df['macd_diff'].iloc[last_idx]
            
            results[tf] = tf_result
        
        # 绘制多时间周期图表
        if data_dict and len(data_dict) > 0:
            chart_path = self.plotter.plot_chan_analysis_multi(
                symbol, data_dict, fx_lists, bi_lists, xd_lists,
                show_ma=True, show_macd=True, 
                show_pivots=True, show_line=False,
                show_buy_sell_points=True,
                show_second_buy_sell_points=True
            )
            if chart_path:
                logger.info(f"多时间周期图表已保存: {chart_path}")
                results['chart_path'] = chart_path
        
        return results
        
    def generate_report(self, results, symbol):
        """
        生成分析报告
        
        参数:
            results: 分析结果字典
            symbol: 交易对，如 'BTC/USDT'
            
        返回:
            str: 分析报告文本
        """
        if not results:
            return f"{symbol} 没有有效的分析结果"
        
        report = []
        report.append(f"# {symbol} 缠论分析报告")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        # 按时间周期排序
        tf_order = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '8h', '12h', '1d', '3d', '1w', '1M']
        timeframes = sorted([tf for tf in results.keys() if tf != 'chart_path'], 
                          key=lambda x: tf_order.index(x) if x in tf_order else 999)
        
        for tf in timeframes:
            tf_result = results[tf]
            report.append(f"## {tf} 时间周期分析")
            
            # 添加基本信息
            report.append(f"- 最新价格: {tf_result.get('latest_price', 'N/A')}")
            report.append(f"- 最新时间: {tf_result.get('latest_time', 'N/A')}")
            report.append(f"- 当前趋势: {tf_result.get('trend', 'N/A')}")
            report.append(f"- 当前方向: {tf_result.get('current_direction', 'N/A')}")
            
            # 添加缠论分析结果
            report.append(f"- 分型数量: {tf_result.get('fractal_count', 0)}")
            report.append(f"- 笔数量: {tf_result.get('stroke_count', 0)}")
            report.append(f"- 线段数量: {tf_result.get('segment_count', 0)}")
            report.append(f"- 中枢数量: {tf_result.get('pivot_count', 0)}")
            
            # 添加MACD信息
            if 'macd_cross' in tf_result:
                macd_cross_str = {
                    'golden': '金叉(看涨)',
                    'dead': '死叉(看跌)',
                    'none': '无交叉'
                }.get(tf_result['macd_cross'], '未知')
                
                report.append(f"- MACD状态: {macd_cross_str}")
                report.append(f"- MACD值: {tf_result.get('macd_value', 0):.4f}")
                report.append(f"- Signal值: {tf_result.get('macd_signal', 0):.4f}")
                report.append(f"- Diff值: {tf_result.get('macd_diff', 0):.4f}")
            
            # 添加第一类买卖点信息
            first_buy_points = tf_result.get('first_buy_points', [])
            if first_buy_points:
                report.append("\n### 第一类买点信息")
                for i, point in enumerate(first_buy_points):
                    idx = point['idx']
                    price = point['price']
                    strength = point['strength']
                    report.append(f"{i+1}. 价格: {price}, 强度: {strength}/10")
            
            first_sell_points = tf_result.get('first_sell_points', [])
            if first_sell_points:
                report.append("\n### 第一类卖点信息")
                for i, point in enumerate(first_sell_points):
                    idx = point['idx']
                    price = point['price']
                    strength = point['strength']
                    report.append(f"{i+1}. 价格: {price}, 强度: {strength}/10")
                    
            # 添加第二类买卖点信息
            second_buy_points = tf_result.get('second_buy_points', [])
            if second_buy_points:
                report.append("\n### 第二类买点信息")
                for i, point in enumerate(second_buy_points):
                    idx = point['idx']
                    price = point['price']
                    strength = point['strength']
                    pivot = point['pivot']
                    report.append(f"{i+1}. 价格: {price}, 强度: {strength}/10, 关联中枢: {pivot}")
            
            second_sell_points = tf_result.get('second_sell_points', [])
            if second_sell_points:
                report.append("\n### 第二类卖点信息")
                for i, point in enumerate(second_sell_points):
                    idx = point['idx']
                    price = point['price']
                    strength = point['strength']
                    pivot = point['pivot']
                    report.append(f"{i+1}. 价格: {price}, 强度: {strength}/10, 关联中枢: {pivot}")
            
            # 添加分析消息
            if tf_result.get('message'):
                report.append(f"\n### 分析结论")
                report.append(tf_result['message'])
            
            report.append("\n" + "-" * 50 + "\n")
        
        # 添加综合分析
        report.append("## 综合分析")
        
        # 检查多时间周期的一致性
        trends = [results[tf].get('trend') for tf in timeframes if tf in results]
        directions = [results[tf].get('current_direction') for tf in timeframes if tf in results]
        
        # 判断趋势一致性
        if all(t == trends[0] for t in trends if t):
            report.append(f"- 多周期趋势一致: {trends[0]}")
        else:
            trend_counts = {}
            for t in trends:
                if t:
                    trend_counts[t] = trend_counts.get(t, 0) + 1
            majority_trend = max(trend_counts.items(), key=lambda x: x[1])[0] if trend_counts else "未知"
            report.append(f"- 多周期趋势不一致，主要趋势: {majority_trend}")
        
        # 判断方向一致性
        if all(d == directions[0] for d in directions if d):
            report.append(f"- 多周期方向一致: {directions[0]}")
        else:
            dir_counts = {}
            for d in directions:
                if d:
                    dir_counts[d] = dir_counts.get(d, 0) + 1
            majority_dir = max(dir_counts.items(), key=lambda x: x[1])[0] if dir_counts else "未知"
            report.append(f"- 多周期方向不一致，主要方向: {majority_dir}")
        
        # 买卖点信号汇总
        all_first_buy = sum(len(results[tf].get('first_buy_points', [])) for tf in timeframes if tf in results)
        all_first_sell = sum(len(results[tf].get('first_sell_points', [])) for tf in timeframes if tf in results)
        all_second_buy = sum(len(results[tf].get('second_buy_points', [])) for tf in timeframes if tf in results)
        all_second_sell = sum(len(results[tf].get('second_sell_points', [])) for tf in timeframes if tf in results)
        
        report.append(f"- 第一类买点总数: {all_first_buy}")
        report.append(f"- 第一类卖点总数: {all_first_sell}")
        report.append(f"- 第二类买点总数: {all_second_buy}")
        report.append(f"- 第二类卖点总数: {all_second_sell}")
        
        # 添加强烈信号提示
        strong_signals = []
        
        # 检查每个时间周期的强买卖点
        for tf in timeframes:
            if tf in results:
                tf_result = results[tf]
                
                # 检查强第一类买点
                strong_first_buys = [p for p in tf_result.get('first_buy_points', []) if p['strength'] >= 8]
                if strong_first_buys:
                    strong_signals.append(f"{tf}周期发现强第一类买点，强度: {strong_first_buys[0]['strength']}/10")
                
                # 检查强第一类卖点
                strong_first_sells = [p for p in tf_result.get('first_sell_points', []) if p['strength'] >= 8]
                if strong_first_sells:
                    strong_signals.append(f"{tf}周期发现强第一类卖点，强度: {strong_first_sells[0]['strength']}/10")
                
                # 检查强第二类买点
                strong_second_buys = [p for p in tf_result.get('second_buy_points', []) if p['strength'] >= 8]
                if strong_second_buys:
                    strong_signals.append(f"{tf}周期发现强第二类买点，强度: {strong_second_buys[0]['strength']}/10，关联中枢: {strong_second_buys[0]['pivot']}")
                
                # 检查强第二类卖点
                strong_second_sells = [p for p in tf_result.get('second_sell_points', []) if p['strength'] >= 8]
                if strong_second_sells:
                    strong_signals.append(f"{tf}周期发现强第二类卖点，强度: {strong_second_sells[0]['strength']}/10，关联中枢: {strong_second_sells[0]['pivot']}")
                
                # 检查MACD金叉/死叉
                if tf_result.get('macd_cross') == 'golden':
                    strong_signals.append(f"{tf}周期MACD金叉形成，看涨信号")
                elif tf_result.get('macd_cross') == 'dead':
                    strong_signals.append(f"{tf}周期MACD死叉形成，看跌信号")
        
        if strong_signals:
            report.append("\n### 强信号提示")
            for signal in strong_signals:
                report.append(f"- {signal}")
        
        # 添加策略建议
        report.append("\n### 策略建议")
        
        # 根据多周期情况给出建议
        if all_second_buy > all_second_sell and all_first_buy > all_first_sell:
            report.append("- 多周期买点信号强于卖点，建议考虑做多")
        elif all_second_sell > all_second_buy and all_first_sell > all_first_buy:
            report.append("- 多周期卖点信号强于买点，建议考虑做空")
        else:
            report.append("- 多周期买卖信号强度相当，建议观望或轻仓操作")
        
        # 强调周期共振
        if len(timeframes) >= 2:
            # 检查是否存在强第二类买点共振
            strong_second_buy_tfs = [tf for tf in timeframes if tf in results and 
                                  any(p['strength'] >= 7 for p in results[tf].get('second_buy_points', []))]
            
            # 检查是否存在强第二类卖点共振
            strong_second_sell_tfs = [tf for tf in timeframes if tf in results and 
                                   any(p['strength'] >= 7 for p in results[tf].get('second_sell_points', []))]
            
            if len(strong_second_buy_tfs) >= 2:
                report.append(f"- 发现多周期第二类买点共振！({', '.join(strong_second_buy_tfs)}周期)")
                report.append("- 多周期共振买点出现，是较强的做多信号")
                
            if len(strong_second_sell_tfs) >= 2:
                report.append(f"- 发现多周期第二类卖点共振！({', '.join(strong_second_sell_tfs)}周期)")
                report.append("- 多周期共振卖点出现，是较强的做空信号")
                
        # 添加图表路径
        if 'chart_path' in results:
            chart_path = results['chart_path']
            report.append(f"\n## 分析图表")
            report.append(f"图表保存路径: {chart_path}")
        
        return "\n".join(report) 